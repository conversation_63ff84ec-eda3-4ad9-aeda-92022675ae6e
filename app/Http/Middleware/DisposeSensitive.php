<?php

namespace App\Http\Middleware;

use App\Constants\ResponseCode;
use App\Models\Sensitive;
use App\Traits\ResponseTrait;
use Closure;

class DisposeSensitive
{
    use ResponseTrait;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, ...$inputs)
    {
        $map = [];
        foreach ($inputs as $input) {
            if ($request->has($input)) {
                $map[$input] = $request->$input;
            }
        }

        $warnings = Sensitive::where('type', 'warning')->get();
        foreach ($map as $key => $value) {
            foreach ($warnings as $sensitive) {
                if (strpos((string) $value, $sensitive->word) === false) {
                    continue;
                }
                return $this->responseError(
                    sprintf('内容包含「%s」为敏感词', $sensitive->word),
                    ResponseCode::PARAM_ERR
                );
            }
        }

        $replaces = Sensitive::where('type', 'replace')
            ->get()
            ->keyBy('word')
            ->map(function ($sensitive) {
                return $sensitive->replace;
            })
            ->toArray();
        $map = array_map(function ($value) use ($replaces) {
            return strtr((string) $value, $replaces);
        }, $map);
        $souceInput = $request->except($inputs);
        $request->replace($map);
        $request->merge($souceInput);

        return $next($request);
    }
}
