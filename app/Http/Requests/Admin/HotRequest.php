<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class HotRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'title' => 'required',
                ];
            case "PUT":
                return [];
            default :
                return [];

        }

    }

    public function messages()
    {
        return [
            'title.required' => '标题不能为空',
        ];
    }
}
