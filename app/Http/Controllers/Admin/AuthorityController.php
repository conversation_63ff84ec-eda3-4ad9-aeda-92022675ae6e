<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Authority;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\AuthorityRequest;

class AuthorityController extends Controller
{
    /**
     * 权限列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $name = $request->query('name');
        $alias = $request->query('alias');

        $authority = Authority::query()
            ->when($name, function ($query) use ($name) {
                return $query->where('name', 'like', '%' . $name . '%');
            })
            ->when($alias, function ($query) use ($alias) {
                return $query->where('alias', 'like', '%' . $alias . '%');
            })
            ->orderBy('sort')
            ->get();

        return $this->responseSuccess($authority);
    }

    /**
     * 创建权限
     *
     * @param AuthorityRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function store(AuthorityRequest $request)
    {
        $alias = $request->input('alias');
        $name = $request->input('name');
        $icon = $request->input('icon');
        $url = $request->input('url');
        $order = $request->input('sort', 0);
        $type = $request->input('type');
        $show = $request->input('show', 1);
        $pid = $request->input('pid', 0);

        if ($pid) {
            $pidAuthority = Authority::query()->where('id', $pid)->first();
            if (!$pidAuthority) {
                throw new ApiException('父级权限不存在', ResponseCode::NOT_FOUND);
            }
            if ($pidAuthority->type > Authority::MENU_TYPE && $pidAuthority->type >= $type) {
                throw new ApiException('权限类型错误', ResponseCode::PARAM_ERR);
            }
        }

        Authority::create([
            'alias' => $alias,
            'name' => $name,
            'icon' => $icon,
            'url' => $url,
            'sort' => $order,
            'type' => $type,
            'pid' => $pid,
            'show' => $show
        ]);

        return $this->responseSuccess(null, '创建权限成功');
    }

    /**
     * 权限详情
     *
     * @param Authority $authority
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Authority $authority)
    {
        return $this->responseSuccess($authority);
    }

    /**
     * 修改权限
     *
     * @param AuthorityRequest $request
     * @param Authority $authority
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(AuthorityRequest $request, Authority $authority)
    {
        $authority->update(
            $request->only(['name', 'alias', 'icon', 'url', 'sort', 'type', 'pid', 'show'])
        );
        return $this->responseSuccess($authority, '修改权限成功');
    }

    /**
     * 删除权限
     *
     * @param Authority $authority
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(Authority $authority)
    {
        $child = Authority::query()->where('pid', $authority->id)->first();
        if ($child) {
            throw new ApiException('请先删除子节点', ResponseCode::FORBIDDEN);
        }
        foreach ($authority->roles as $role) {
            $role->permissions()->detach($authority->id);
        }
        $authority->delete();

        return $this->responseSuccess(null, '删除权限成功');
    }
}
