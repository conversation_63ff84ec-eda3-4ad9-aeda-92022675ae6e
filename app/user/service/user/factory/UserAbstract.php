<?php
/**
 * 用户登录接口
 */

namespace app\user\service\user\factory;

use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\libraries\models\BaseModel;
use app\libraries\utils\EncryptUtil;
use app\user\cache\TokenCache;
use app\user\event\entity\MemberChangeEntity;
use app\user\model\PlatformUserModel;
use app\user\service\TokenService;
use app\user\service\user\entity\OtherUserInfoEntity;
use app\user\service\user\entity\TokenEntity;
use app\user\service\WechatService;
use TencentCloud\Cls\V20201016\Models\LogInfo;

abstract class UserAbstract
{
    /**
     * 三方登录唯一健
     * @var string
     */
    protected string $otherUnionIdField = '';

    /**
     * 来源类型
     * @var int
     */
    protected int $sourceType = 0;

    /**
     * 三方用户信息对象
     * @var BaseModel
     */
    protected BaseModel $otherModel;

    /**
     * 平台guid
     * @var string
     */
    protected string $tableGuid = '';

    abstract public function __construct(BaseModel $table);

    /**
     * 三方用户登录
     * @param $params
     * @return OtherUserInfoEntity
     */
    abstract public function otherLogin(array $params): OtherUserInfoEntity;

    /**
     * 验证token，并获取加解密key
     *
     * @param $token
     * @return TokenEntity
     */
    abstract public function verifyTokenByDb($token): TokenEntity;

    /**
     * 获取手机号
     *
     * @param $code
     * @return string
     */
    abstract public function getUserPhoneNumber(string $code): string;

    /**
     * 唤起登录
     *
     * @param $params
     * @return string
     * @throws ApiException
     */
    public function getRedirectUrl($params)
    {
        throwException(SysErrorCode::SYS_ERROR_CODE, '未配置唤起登录');
        return '';
    }

    /**
     * 授权手机号
     *
     * @param string $code
     * @return bool
     * @throws ApiException
     */
    public function authorizePhone(string $code): bool
    {
        $mobile = $this->getUserPhoneNumber($code);

        // 绑定手机号
        $platformUserModel = (new PlatformUserModel())
            ->where('sys_id', TokenService::getInstance()->getTokenEntity()->userId)
            ->findOrEmpty();

        if ($platformUserModel->isEmpty()) {
            throwException(SysErrorCode::NOT_LOGIN);
        }

        $platformUserModel->mobile = $mobile;
        $platformUserModel->save();

        return true;

    }

    /**
     * 用户登录
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function login($params): array
    {
        // 用户登录信息
        $otherUserInfoEntity = $this->otherLogin($params);

        // 授权手机号
        if (!empty($params['mobileCode'])) {
            $mobile = $this->getUserPhoneNumber($params['mobileCode']);
        }

        // 获取用户id
        $platformUser = new PlatformUserModel;
        $platformUser = $platformUser
            ->where($this->otherUnionIdField, $otherUserInfoEntity->unionId)
            ->findOrEmpty();

        // 不存在用户需要注册
        if ($platformUser->isEmpty()) {
            $insertData = [
                $this->otherUnionIdField => $otherUserInfoEntity->unionId,
                'head_imgurl' => $otherUserInfoEntity->headImgUrl,
                'nickname' => $otherUserInfoEntity->nickname,
                'source_type' => $this->sourceType,
                'table_guid' => $otherUserInfoEntity->tableGuid,
                'level' => [0],
                'user_no' => order_no('H'),
            ];
            if (!empty($mobile)) {
                $insertData['mobile'] = $mobile;
            }

            $platformUser->save($insertData);

            // 用户信息变更事件
            $entity = new MemberChangeEntity();
            $entity->userId = $platformUser->sysId;
            $entity->changType = MemberChangeEntity::CHANGE_TYPE_CREATE;
            event('userInfoChange', $entity);
        } else {
            $oldMobile = EncryptUtil::decryptNumberidentification($platformUser->mobileSecretKey, $platformUser->mobile);
            // 电话号码存在且发生变化
            if (!empty($mobile) && $mobile != $oldMobile) {
                $insertData['mobile'] = $mobile;
                $platformUser->save($insertData);

                // 用户信息变更事件
                $entity = new MemberChangeEntity();
                $entity->userId = $platformUser->sysId;
                $entity->changType = MemberChangeEntity::CHANGE_TYPE_UPDATE;
                event('userInfoChange', $entity);
            }
        }

        // 用户登录，获取token
        $tokenEntity = TokenService::getInstance()
            ->setTokenEntity($platformUser, $this->sourceType, $otherUserInfoEntity)
            ->getTokenEntity();

        // 存入缓存
        TokenCache::getInstance()->setItem($tokenEntity->loginToken, $tokenEntity);

        return [
            'token' => $tokenEntity->loginToken,
            'secret' => $tokenEntity->loginSecret,
        ];
    }

    /**
     * token 验证
     * @param $token
     * @return TokenEntity
     * @throws ApiException
     */
    public function verifyToken($token): TokenEntity
    {
        // 验证缓存
        $tokenEntity = $this->verifyTokenByCache($token);
        if (empty($tokenEntity->userId)) {
            // 验证数据库
            $tokenEntity = $this->verifyTokenByDb($token);
        }

        // 验证token
        if ($this->tableGuid != $tokenEntity->otherUserInfoEntity->tableGuid ||
            $this->sourceType != $tokenEntity->sourceType ||
            $token != $tokenEntity->loginToken
        ) {
            throwException(SysErrorCode::NOT_LOGIN);
        }

        return $tokenEntity;
    }

    /**
     * 验证token-缓存
     * @param $token
     * @return TokenEntity
     * @throws ApiException
     */
    protected function verifyTokenByCache($token): TokenEntity
    {
        // 验证缓存
        $tokenEntity = TokenCache::getInstance()->getItem($token);
        // 缓存不存在
        if (empty($tokenEntity->userId) || empty($tokenEntity->userGuid)) {
            return new TokenEntity();
        }

        // 获取用户信息
        $user = PlatformUserModel::where('sys_id', $tokenEntity->userId)
            ->where('status', PlatformUserModel::STATUS_YES)
            ->findOrEmpty();
        if ($user->isEmpty()) {
            throwException(SysErrorCode::NOT_LOGIN);
        }

        TokenService::getInstance()->setTokenEntityObject($tokenEntity);
        return $tokenEntity;
    }

    /**
     * 获取jssdk签名
     * @param string $url
     * @return array
     */
    public function getJsSdkConfigArray(string $url): array
    {
        $wxApp = WechatService::getInstance()->initAccountApp($this->wxTable);
        $APIs = [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'chooseWXPay',
            "hideMenuItems",
            "getLocation",
            "openLocation",
            "hideAllNonBaseMenuItem",
            "showMenuItems",
            'chooseImage',
            'getLocalImgData',
        ];
        LogInfo(__CLASS__ . '-' . __FUNCTION__, '生成JSSDK配置的参数', '生成JSSDK配置的参数', [
            'url' => $url,
            'jsApiList' => $APIs,
        ]);
        return $wxApp->jssdk->setUrl($url)->getConfigArray($APIs);
    }

    /**
     * 创建菜单
     */
    public function createMenu()
    {
        LogInfo(__CLASS__, "微信公众号菜单", "功能暂未开放");
    }
}