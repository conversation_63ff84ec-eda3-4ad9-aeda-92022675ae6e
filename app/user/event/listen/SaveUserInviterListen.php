<?php

namespace app\user\event\listen;

use app\libraries\utils\queue\event\AsyncJob;
use app\libraries\utils\Util;
use app\user\event\entity\UserInviterEntity;
use app\user\logic\api\UserInfoLogic;
use Throwable;

class SaveUserInviterListen extends AsyncJob
{

    protected int $delay = 10; //延迟10秒执行


    public function handle(UserInviterEntity $userInviterEntity)
    {
        LogInfo(
            __CLASS__ . '-' . __FUNCTION__,
            '更新用户邀请关系',
            '更新用户邀请关系事件',
            $userInviterEntity->toArray()
        );

        try {
            (new UserInfoLogic())->saveInviter([
                'inviterGuid' => $userInviterEntity->inviterGuid,
                'userId' => $userInviterEntity->userId,
                'startTime' => $userInviterEntity->startTime,
                'count' => $userInviterEntity->count,
            ]);

            if ($userInviterEntity->count > 20) {
                LogError(
                    __CLASS__,
                    '更新用户邀请关系',
                    '事件自定义的重试达20次,用户注册订阅事件可能发生异常,需排查一下',
                    [
                        [
                            'inviterGuid' => $userInviterEntity->inviterGuid,
                            'userId' => $userInviterEntity->userId,
                            'startTime' => $userInviterEntity->startTime,
                            'count' => $userInviterEntity->count,
                        ]
                    ]
                );
            }
        } catch (Throwable $e) {
            LogError(
                __CLASS__,
                '更新用户邀请关系',
                '更新用户邀请关系事件-异常',
                [
                    'error' => Util::normalizeException($e),
                ]
            );
        }
    }
}