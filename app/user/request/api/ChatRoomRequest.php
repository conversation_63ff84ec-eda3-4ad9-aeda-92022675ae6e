<?php

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @Time: 2023/9/3   22:24
 */

namespace app\user\request\api;

use app\Request;

class ChatRoomRequest extends Request
{
    protected array $msgs = [
        'roomPassword' => '请输入房间密码',
    ];


    protected function getRule(): array
    {
        return [
            'roomCreate' => [
               'roomTitle' => ['require'],
               'roomDesc' => ['require'],
               'isNeedPassword' => ['require'],
            ],
            'roomJoin' => [
                'roomNumber' => ['require'],
            ]
        ];
    }
}
