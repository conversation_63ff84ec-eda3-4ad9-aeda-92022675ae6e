<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/3/25   17:30
 */

namespace app\user\logic\api;

use app\constdir\SysErrorCode;
use app\constdir\SysTime;
use app\libraries\exception\ApiException;
use app\libraries\service\getID3\getID3Service;
use app\libraries\service\token\TokenService;
use app\libraries\utils\CosService;
use app\libraries\utils\ImgUtil;
use app\merchant\models\MerchantConfigModel;
use app\square\models\PageShareConfigModel;
use app\square\models\UserChatLunciHelpModel;
use app\user\cache\LoginCache;
use app\user\cache\UserCache;
use app\user\models\UserAssetsModel;
use app\user\models\UserBigVipCardModel;
use app\user\models\UserChatCollectionModel;
use app\user\models\UserChatLunciHelpUsersModel;
use app\user\models\UserHeadImgModel;
use app\user\models\UsersModel;
use app\user\service\UserService;
use app\user\service\WechatService;
use EasyWeChat\Factory;
use app\user\models\UserSecretKeyModel;

class UserInfoLogic
{
    /**
     * 用户信息
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $baseUserInfo = UsersModel::getInstance()->where('sys_id', $uid)->findOrEmpty();
        if ($baseUserInfo->isEmpty()) {
            return [
                'sysId' => 0,
                'guid' => '',
                'isLogin' => false,
                'nickname' => '',
                'headImgUrl' => '',
                'chat_count' => 0,
                'parentUser' => [],
                'loginText' => '为了更好的提供服务，请您先完成手机号码注册哦',
                'mobile' => '',
                'email' => '',
                'openid' => '',
                'gzhOpenid' => '',
            ];
        }
        $userAssets = UserAssetsModel::getInstance()->where('platform_user_sys_id', $uid)->find();
        //上级用户信息
        $parentUserInfo = new \stdClass();
        if ($baseUserInfo['parentUid'] > 0) {
            $parentUserInfo = UsersModel::getInstance()
                ->where('sys_id', $baseUserInfo->parentUid)
                ->field('sys_id,nickname,head_imgurl')
                ->findOrEmpty();
        }
        // 处理用户头像，判断是否存在http或者https
        $headImg = $baseUserInfo['headImgurl'];
        if (!str_contains($headImg, 'http') && !str_contains($headImg, 'https')) {
            $headImg = config('app.upload_image_url') . $headImg;
        }
        return [
            'sysId' => $baseUserInfo['sys_id'],
            'guid' => $baseUserInfo['guid'],
            'isLogin' => true,
            'nickname' => $baseUserInfo['nickname'],
            'headImgUrl' => $headImg,
            'chat_count' => $userAssets['chatCount'],
            'parentUser' => $parentUserInfo,
            'loginText' => '为了更好的提供服务，请您先完成手机号码注册哦',
            'mobile' => $baseUserInfo['mobile'],
            'email' => $baseUserInfo['email'] ?? '',
            'openid' => $baseUserInfo['openId'],
            'gzhOpenid' => $baseUserInfo['gzhOpenid'],
        ];
    }

    public function uploadImg()
    {
        $file = request()->file('img');
        $merchantUuid = request()->post('merchant_guid', '');
        $fileType = $file->getMime();
        $ableMime = [
            "image/jpeg",
            "image/png",
            "image/webp",
            "image/gif",
        ];
        if (!in_array($fileType, $ableMime)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '无效的图片类型');
        }
        if ($file->getSize() > 1024 * 1024 * 20) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '图片尺寸不能大于20M');
        }
        // 上传文件到本地
        $savename = \think\facade\Filesystem::disk('public')->putFile('upload/temp', $file);
        // 图片内容检测
        $realPath = public_path() . $savename;
        // ImgUtil::imgSecCheck($realPath, $merchantUuid);
        $cosImgs =  CosService::uploadLocal($savename, $fileType, 'image');
        return $cosImgs;
    }

    /**
     * 上传视频
     * @return array
     * @throws ApiException
     */
    public function uploadVideo()
    {
        // https://github.com/JamesHeinrich/getID3
        $file = request()->file('video');
        $fileType = $file->getMime();
        $ableMime = [
            "video/mp4",
        ];
        if (!in_array($fileType, $ableMime)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '无效的视频类型');
        }
        if ($file->getSize() > 1024 * 1024 * 100) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '视频尺寸不能大于100M');
        }
        $savename = \think\facade\Filesystem::disk('public')->putFile('upload/tmp', $file);
        $cosImgs =  CosService::uploadLocal($savename, $fileType, 'video');
        return $cosImgs;
    }

    /**
     *  上传音频
     */
    public function uploadVoice()
    {
        $file = request()->file('voice');
        $fileType = $file->getMime();
        // $ableMime = [
        //     "audio/mp3",
        //     "audio/mpeg",
        //     "audio/wav",
        //     "audio/x-m4a",
        //     "audio/x-wav"
        // ];
        // if (!in_array($fileType, $ableMime)) {
        //     throwException(SysErrorCode::SYS_PARAMS_ERROR, '无效的音频类型');
        // }
        if ($file->getSize() > 1024 * 1024 * 10) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '音频尺寸不能大于10M');
        }
        $savename = \think\facade\Filesystem::disk('public')->putFile('upload/voice', $file);
        $filePath  = public_path()  . $savename;
        $duration = getID3Service::getAudioDuration($filePath);
        $cosImgs =  CosService::uploadLocal($savename, $fileType, 'voice');
        return [
            'voiceUrl' => $cosImgs['data'],
            'duration' => $duration,
        ];
    }

    /**
     * 上传txt
     * @return array
     * @throws ApiException
     */
    public function uploadTxt()
    {
        $file = request()->file('txt');
        $fileType = $file->getMime();
        if ($file->getSize() > 1024 * 1024 * 20) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '文件不能大于20M');
        }
        $savename = \think\facade\Filesystem::disk('public')->putFile('upload/txt', $file);
        $cosImgs =  CosService::uploadLocal($savename, $fileType, 'file');
        return $cosImgs;
    }

    /**
     * 上传文件
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function uploadFile()
    {
        $file = request()->file('file');
        if (!$file) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '文件不能为空');
        }
        $savename = \think\facade\Filesystem::disk('public')->putFile('upload/file', $file);
        // 获取文件名称
        $filename = $file->getOriginalName();
        // 去除文件名中的文件后缀
        $filename = substr($filename, 0, strrpos($filename, '.'));
        $cosFile =  CosService::uploadLocal($savename, 'application/octet-stream', 'file', null, $filename);
        return $cosFile;
    }

    /**
     * 修改个人信息
     * @param $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function update($data): array
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户信息不存在');
        }
        if (!empty($data['nickname'])) {
            $userInfo->nickname = $data['nickname'];
        }
        if (!empty($data['headImgUrl'])) {
            $userInfo->headImgurl = $data['headImgUrl'];
        }
        if (isset($data['email'])) {
            // 验证邮箱格式
            if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '邮箱格式不正确');
            }
            $userInfo->email = $data['email'];
        }
        $userInfo->save();

        return [];
    }

    public function sysHeadImg()
    {
        return UserHeadImgModel::getInstance()->order('show_order')->select()->toArray();
    }

    /**
     * 解绑上级数字人
     * @return array
     */
    public function unbind()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        UsersModel::getInstance()->where('sys_id', $userId)->update([
            'parent_uid' => 0
        ]);

        return [];
    }

    /**
     * 获取公众号网页授权回调地址
     * @param $data
     * @return string
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function gzhGetRedirectUrl($data)
    {
        $app = $this->getGzhApp($data['merchantGuid']);
        return  $app->oauth->scopes(['snsapi_userinfo'])->redirect($data['notifyUrl']);
    }

    /**
     * 获取公众号app
     * @param $merchantGuid
     * @return \EasyWeChat\OfficialAccount\Application
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getGzhApp($merchantGuid)
    {
        $merchantConfig = MerchantConfigModel::getMerchantConfigs($merchantGuid);
        if (
            empty($merchantConfig[MerchantConfigModel::CONFIG_GZH_APP_ID])
            || empty($merchantConfig[MerchantConfigModel::CONFIG_GZH_SECRET])
        ) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '公众号未配置');
        }
        $config = [
            'app_id' => $merchantConfig[MerchantConfigModel::CONFIG_GZH_APP_ID],
            'secret' => $merchantConfig[MerchantConfigModel::CONFIG_GZH_SECRET],
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];
        return Factory::officialAccount($config);
    }

    /**
     * 公众号授权登录
     * @param $data
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function gzhCodeLogin($data)
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $uid)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户信息不存在');
        }
        $app = $this->getGzhApp($data['merchantGuid']);
        $wxUser = $app->oauth->userFromCode($data['code']);
        $openId = $wxUser->getId();
        if (!$openId) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权登录失败，请重试');
        }
        $userInfo->gzhOpenid = $openId;
        $userInfo->merchantGuid = $data['merchantGuid'];
        $userInfo->save();
        return [];
    }

    /**
     * 完成小程序授权登录
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function sureXiaoyiCodeLogin($data)
    {
        $code = $data['code'];
        $codeArr = explode('=', $code);
        if (isset($codeArr[1])) {
            $code = $codeArr[1];
        }
        $codeInfo = UserCache::getInstance()->getXiaoyiLoginCode($code);
        if (!$codeInfo) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '登录码已失效，请重新获取');
        }
        if ($data['merchantGuid'] != $codeInfo['merchant_guid']) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '商家不匹配');
        }
        if ($codeInfo['is_login']) {
            return [];
        }
        // 完成PC端登录token
        $token = get_guid();
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $baseUserInfo = UsersModel::getInstance()->where('sys_id', $uid)->findOrEmpty();
        if ($baseUserInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户信息不存在');
        }
        $baseUserInfo->loginPcToken = $token;
        $baseUserInfo->save();
        // 登录确认
        $loginCodeInfo = [
            'merchant_guid' => $data['merchantGuid'],
            'code' => $code,
            'is_login' => true,
            'from' => 'xiaoyipc',
            'token' => $token,
        ];
        // 重新储存登录情况
        UserCache::getInstance()->xiaoyiLoginCode($code, $loginCodeInfo);
        // 如果授权登录成功，判断是否为10分钟内的注册的用户，如果是新注册的用户，绑定邀请关系
        $registerTime = $baseUserInfo['create_time'];
        if (time() - $registerTime < SysTime::FIFTEEN_MINUTES) {
            $registerCode = $codeInfo['invitationText'] ?? '';
            $registerCodeType = $codeInfo['invitationType'] ?? '';
            UserService::getInstance()->registerReward($baseUserInfo->sysId, $registerCode, $registerCodeType);
        }
        return [];
    }

    /**
     * 获取页面分享
     * @param $param
     * @return array|string[]
     * @throws ApiException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\RuntimeException
     */
    public function getPageShareConfig($param)
    {
        $shareConfig = [
            'shareTitle' => '',
            'shareDesc' => '',
            'shareImg' => '',
        ];
        $defaultConfig = PageShareConfigModel::getInstance()
            ->where('share_path', $param['sharePath'])
            ->findOrEmpty();
        if (!$defaultConfig->isEmpty()) {
            $shareConfig = [
                'shareTitle' => $defaultConfig['share_title'],
                'shareDesc' => $defaultConfig['page_desc'],
                'shareImg' => $defaultConfig['share_img'],
            ];
        }
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        if (!$uid) {
            return [];
        }
        $pathQuery = $param['pathQuery'] ?? 'uid=' . $uid;
        // 判断当前页面的路径是否在缓存中存在
        $cacheShareImg = UserCache::getInstance()->getPageShareConfig($param['sharePath']);
        if ($cacheShareImg) {
            $shareConfig['shareCodeImg'] = $cacheShareImg;
        } else {
            $merchantGuid = TokenService::getInstance()->getTokenEntity()->merchantGuid;
            $app = WechatService::getInstance()->getMiniProgramApp($merchantGuid);
            $response = $app->app_code->getQrCode($param['sharePath']);
            if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                $saveName = 'separation_user_' . md5($param['sharePath']) . '.png';
                $response->saveAs(public_path('/user/separation'), $saveName);
                // 存储到cos
                $filePath = '/user/separation/' . $saveName;
                $cosImgs =  CosService::uploadLocal($filePath, "image/png", 'image');
                if (empty($cosImgs['data'])) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '获取小程序码失败');
                }
                $shareConfig['shareCodeImg'] = $cosImgs['data'];
            }
        }
        return $shareConfig;
    }

    /**
     * PC工作台绑定
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function buildPcPhone($data)
    {
        //手机验证码验证
        $phoneCode = LoginCache::getInstance()->getSmsCode($data['phone']);
        if (empty($phoneCode['code']) || $phoneCode['code'] != $data['smsCode']) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '手机验证码错误');
        }
        if ((time() - $phoneCode['sendTime']) > SysTime::FIFTEEN_MINUTES) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '验证码已失效');
        }
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        UsersModel::getInstance()->where('sys_id', $userId)->update([
            'mobile' => $data['phone']
        ]);
        return [];
    }


    public function joinHelpUsers($data)
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $existJoin = UserChatLunciHelpUsersModel::getInstance()
            ->where('platform_user_sys_id', $uid)
            ->findOrEmpty();
        if (!$existJoin->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您已经加入了创作者联盟');
        }
        $bigVipUserInfo = UserBigVipCardModel::getInstance()
            ->where('platform_user_sys_id', $uid)
            ->findOrEmpty();
        if ($bigVipUserInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '很抱歉，当前仅限大会员用户加入');
        }
        // 创建创作者联盟用户
        $helpUserModel = new UserChatLunciHelpUsersModel();
        $helpUserModel->platformUserSysId = $uid;
        $helpUserModel->nickname = $data['nickname'];
        $helpUserModel->avatarUrl = $data['avatarUrl'];
        $helpUserModel->introduction = $data['introduction'];
        $helpUserModel->save();

        return [];
    }


    /**
     * 获取用户加入创作者联盟信息
     * @param $data
     * @return array
     */
    public function helpUserInfo()
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $helpUserInfo = UserChatLunciHelpUsersModel::getInstance()
            ->where('platform_user_sys_id', $uid)
            ->findOrEmpty();
        if ($helpUserInfo->isEmpty()) {
            return [
                'is_join' => false,
                'info' => []
            ];
        }
        return [
            'is_join' => true,
            'info' => $helpUserInfo
        ];
    }

    /**
     * 创作者协作数据统计
     * @param $data
     * @return array
     */
    public function helpUserOverview()
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $helpUserInfo = UserChatLunciHelpUsersModel::getInstance()
            ->where('platform_user_sys_id', $uid)
            ->findOrEmpty();
        if ($helpUserInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您还未加入创作者联盟');
        }
        // 累计调试次数
        $totalChatCount = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $uid)
            ->sum('debug_chat_count');
        // 协助中聊天轮次总数
        $doingHelpCount = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $uid)
            ->where('help_status', UserChatLunciHelpModel::HELP_STATUS_DOING)
            ->whereNull('deleted_at')
            ->count();
        // 已完成聊天轮次总数
        $finishHelpCount = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $uid)
            ->where('help_status', UserChatLunciHelpModel::HELP_STATUS_FINISHED)
            ->whereNull('deleted_at')
            ->count();
        // 累计协作奖励点数
        $totalRewardPoints = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $uid)
            ->sum('help_reward_points');

        return [
            'totalChatCount' => $totalChatCount,
            'doingHelpCount' => $doingHelpCount,
            'finishHelpCount' => $finishHelpCount,
            'totalRewardPoints' => $totalRewardPoints,
        ];
    }

    /**
     * 获取我的邀请码
     * @return array
     */
    public function getInvitationCode()
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $inviteCode = UsersModel::getInviteCode($uid);
        return [
            'inviteCode' => $inviteCode,
        ];
    }

    /**
     * 保存用户秘钥
     * @param $params
     * @return array
     */
    public function saveSecretKey($params)
    {
        $saveList = [];
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        foreach ($params['saveSecretKeyList'] as $key => $value) {
            $saveList[] = [
                'merchantGuid' => $params['merchantGuid'],
                'platformUserSysId' => $userId,
                'secretKeyType' => $value['secretKeyType'],
                'secretKey' => $value['secretKey'],
                'extendInfo' => $value['extendInfo'] ?? [],
            ];
        }
        // 先删除对应的秘钥
        UserSecretKeyModel::destroy(function ($query) use ($params, $userId) {
            $query->where('merchant_guid', $params['merchantGuid'])
                ->where('platform_user_sys_id', $userId)
                ->whereIn('secret_key_type', array_column($params['saveSecretKeyList'], 'secretKeyType'));
        }, true);
        // 再保存
        foreach ($saveList as $value) {
            $saveKey = new UserSecretKeyModel();
            $saveKey->merchantGuid = $value['merchantGuid'];
            $saveKey->platformUserSysId = $value['platformUserSysId'];
            $saveKey->secretKeyType = $value['secretKeyType'];
            $saveKey->secretKey = $value['secretKey'];
            $saveKey->extendInfo = $value['extendInfo'];
            $saveKey->save();
        }

        return [];
    }

    /**
     * 获取用户密钥列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getSecretKeyList($params)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;

        // 密钥初始化，如果没有配置要默认添加扣子和dify的空密钥
        UserSecretKeyModel::initSecretKey($userId, $params['merchantGuid']);

        $query = UserSecretKeyModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('merchant_guid', $params['merchantGuid'])
            ->order('create_time', 'desc');

        $result = $query->select()->toArray();

        // 格式化返回数据
        foreach ($result as &$item) {
            $item['secretKeyTypeText'] = UserSecretKeyModel::SECRET_KEY_TYPE_MAP[$item['secretKeyType']] ?? '';
            $item['createTime'] = date('Y-m-d H:i:s', $item['createTime']);
            $item['updateTime'] = date('Y-m-d H:i:s', $item['updateTime']);
        }

        return $result;
    }
}
