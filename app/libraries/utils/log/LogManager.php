<?php
/*
 * @Author: 杨红兵 
 * @Date: 2022-07-27 17:34:47 
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-07-27 17:35:59
 */

namespace app\libraries\utils\log;

use EasyWeChat\Kernel\Log\LogManager as LogLogManager;
use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\StreamHandler;

class LogManager extends LogLogManager
{
    /**
     * Create an instance of the single file log driver.
     *
     * @param array $config
     *
     * @return \Psr\Log\LoggerInterface
     *
     * @throws \Exception
     */
    protected function createSingleDriver(array $config)
    {
        return new Logger($this->parseChannel($config), [
            $this->prepareHandler(new StreamHandler(
                $config['path'],
                $this->level($config),
                $config['bubble'] ?? true,
                $config['permission'] ?? null,
                $config['locking'] ?? false
            ), $config),
        ]);
    }

    /**
     * Get a Monolog formatter instance.
     *
     * @return \Monolog\Formatter\FormatterInterface
     */
    protected function formatter()
    {
        return new JsonFormatter();
    }
}