<?php
/*
 * @Author: 杨红兵
 * @Date: 2022-10-04 08:11:36
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-10-04 08:12:51
 */

namespace app\libraries\swoole;

use think\swoole\Pool as SwoolePool;

class Pool extends SwoolePool
{
    public function close(string $key)
    {
        $ret = $this->pools[$key]->close();
        unset($this->pools[$key]);
        return $ret;
    }

    public function closeAll()
    {
        foreach ($this->pools as $pool) {
            $pool->close();
        }
        $this->pools = [];
    }
}
