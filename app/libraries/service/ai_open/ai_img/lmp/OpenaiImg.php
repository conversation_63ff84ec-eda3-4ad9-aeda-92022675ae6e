<?php

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @Time: 2023/7/17   08:33
 */

namespace app\libraries\service\ai_open\ai_img\lmp;

use app\libraries\service\ai_open\ai_img\AiImgAbstract;
use app\libraries\service\ai_open\ai_img\entity\AiImgCreateEntity;
use app\libraries\service\cos\CosService;
use Orhanerday\OpenAi\OpenAi;

class OpenaiImg extends AiImgAbstract
{
    /**
     * 生成图片
     * @param AiImgCreateEntity $entity
     * @return array
     * @throws \Exception
     */
    public function createImg(AiImgCreateEntity $entity): array
    {
        $open_ai_key = config('chatgpt.api_key');
        $open_ai = new OpenAi($open_ai_key);
        $complete = $open_ai->image([
            "prompt" => $entity->prompt,
            "n" => $entity->n,
            "size" => $entity->size,
            "response_format" => "url",
        ]);
        if (empty($complete['data'])) {
            //TODO 日志记录
            throw new \Exception('生成失败，请稍后重试');
        }
        $openaiImgs = array_column($complete['data'], 'url');
        //Fixme: 需要根据外部文件链接，拉取并上传到腾讯云，下面是之前腾讯云cos示例代码
        //$cosImgs =  CosService::getInstance()->uploadByOutUrl($openaiImgs, 'openai_img');
        $cosImgs = $openaiImgs;
        return $cosImgs;
    }

    /**
     * 获取图片查询结果
     * @param string $orderNo
     * @return array
     */
    public function queryImgResult(string $orderNo): array
    {
        // TODO: 根据开放平台调用订单号，返回图片创作结果，需要开发平台完成订单查询接口
        return [];
    }
}
