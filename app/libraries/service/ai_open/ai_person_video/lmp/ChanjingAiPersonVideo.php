<?php

/**
 * 蝉镜数字人视频服务实现类
 * @author: AI Assistant
 * @Time: 2025/01/13
 */

namespace app\libraries\service\ai_open\ai_person_video\lmp;

use app\libraries\service\ai_open\ai_person_video\AiPersonVideoAbstract;
use app\libraries\service\ai_open\ai_person_video\entity\Mp3VideoEntity;
use app\libraries\service\ai_open\ai_person_video\entity\TextVideoEntity;
use app\useragent\models\MerchantAiAgentConfigModel;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use think\facade\Cache;

class ChanjingAiPersonVideo extends AiPersonVideoAbstract
{
    /**
     * 蝉镜API基础地址
     */
    private const BASE_URL = 'https://open-api.chanjing.cc';

    /**
     * Access Token缓存键前缀
     */
    private const TOKEN_CACHE_PREFIX = 'chanjing_access_token_';

    /**
     * HTTP客户端
     */
    private Client $client;

    /**
     * 商户GUID
     */
    private string $merchantGuid;

    /**
     * 构造函数
     * @param string $merchantGuid 商户GUID
     */
    public function __construct(string $merchantGuid = '')
    {
        $this->merchantGuid = $merchantGuid;
        $this->client = new Client([
            'base_uri' => self::BASE_URL,
            'timeout' => 30,
            'headers' => [
                'Content-Type' => 'application/json; charset=utf-8',
            ]
        ]);
    }

    /**
     * 获取Access Token
     * @return string
     * @throws \Exception
     */
    private function getAccessToken(): string
    {
        // 获取配置
        $appId = MerchantAiAgentConfigModel::getShuzirenChanjingAppid($this->merchantGuid);
        $secretKey = MerchantAiAgentConfigModel::getShuzirenChanjingKey($this->merchantGuid);

        if (empty($appId) || empty($secretKey)) {
            throw new \Exception('数字人配置未设置，请先配置APP ID和Secret Key');
        }

        $cacheKey =  self::TOKEN_CACHE_PREFIX . md5($this->merchantGuid) . $appId;

        // 先从缓存获取
        $cachedToken = Cache::get($cacheKey);
        if ($cachedToken) {
            return $cachedToken;
        }

        try {
            $response = $this->client->post('/open/v1/access_token', [
                'json' => [
                    'app_id' => $appId,
                    'secret_key' => $secretKey,
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (!$result || $result['code'] !== 0) {
                $errorMsg = $result['msg'] ?? '获取Access Token失败';
                throw new \Exception("蝉镜API错误: {$errorMsg}");
            }

            $accessToken = $result['data']['access_token'] ?? '';
            $expireIn = $result['data']['expire_in'] ?? 0;

            if (empty($accessToken)) {
                throw new \Exception('获取Access Token失败：返回数据异常');
            }

            // 缓存Token，提前5分钟过期
            $cacheTime = max(0, $expireIn - time() - 300);
            if ($cacheTime > 0) {
                Cache::set($cacheKey, $accessToken, $cacheTime);
            }

            return $accessToken;
        } catch (GuzzleException $e) {
            throw new \Exception('请求蝉镜API失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送API请求
     * @param string $method HTTP方法
     * @param string $uri 接口URI
     * @param array $data 请求数据
     * @return mixed
     * @throws \Exception
     */
    private function request(string $method, string $uri, array $data = [])
    {
        try {
            $accessToken = $this->getAccessToken();

            $options = [
                'headers' => [
                    'access_token' => $accessToken,
                ]
            ];

            if (!empty($data)) {
                if (strtoupper($method) === 'GET') {
                    $options['query'] = $data;
                } else {
                    $options['json'] = $data;
                }
            }

            $response = $this->client->request($method, $uri, $options);
            $result = json_decode($response->getBody()->getContents(), true);
            if (!$result) {
                throw new \Exception('API响应数据格式错误');
            }

            if ($result['code'] !== 0) {
                $errorMsg = $result['msg'] ?? '未知错误';
                throw new \Exception("蝉镜API错误: {$errorMsg}");
            }

            return $result['data'] ?? [];
        } catch (GuzzleException $e) {
            throw new \Exception('请求蝉镜API失败: ' . $e->getMessage());
        }
    }

    /**
     * 通过文字创建数字人视频
     * @param TextVideoEntity $entity
     * @return array
     * @throws \Exception
     */
    public function createTextVideo(TextVideoEntity $entity): array
    {
        // TODO: 根据蝉镜API文档实现文字转视频接口
        // 这里需要根据具体的蝉镜API文档来实现
        throw new \Exception('蝉镜文字转视频接口待实现，请提供具体的API文档');
    }

    /**
     * 通过音频创建数字人视频
     * @param Mp3VideoEntity $entity
     * @return array
     * @throws \Exception
     */
    public function createMp3Video(Mp3VideoEntity $entity): array
    {
        // TODO: 根据蝉镜API文档实现音频转视频接口
        // 这里需要根据具体的蝉镜API文档来实现
        throw new \Exception('蝉镜音频转视频接口待实现，请提供具体的API文档');
    }

    /**
     * 获取数字人视频创作结果
     * @param string $videoId 创作数字人的工作id
     * @return array
     * @throws \Exception
     */
    public function getVideoResult(string $videoId): array
    {
        // TODO: 根据蝉镜API文档实现获取视频结果接口
        // 这里需要根据具体的蝉镜API文档来实现
        throw new \Exception('蝉镜获取视频结果接口待实现，请提供具体的API文档');
    }

    /**
     * 获取可用声音列表
     * @return array[]
     */
    public function getVoiceList(): array
    {
        // TODO: 根据蝉镜API文档实现获取声音列表接口
        // 暂时返回空数组，等待具体API文档
        return [];
    }

    /**
     * 获取公共形象列表
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     * @throws \Exception
     */
    public function getPersonList(int $page = 1, int $pageSize = 20): array
    {
        $data = [
            'page' => $page,
            'size' => $pageSize,
        ];

        return $this->request('GET', '/open/v1/list_common_dp', $data);
    }

    /**
     * 获取定制形象列表
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     * @throws \Exception
     */
    public function getCustomPersonList(int $page = 1, int $pageSize = 20): array
    {
        $data = [
            'page' => $page,
            'page_size' => $pageSize,
        ];

        return $this->request('POST', '/open/v1/list_customised_person', $data);
    }

    /**
     * 创建视频合成任务
     * @param array $data 请求参数
     * @return mixed
     * @throws \Exception
     */
    public function createVideo(array $data)
    {
        return $this->request('POST', '/open/v1/create_video', $data); // 直接返回视频ID字符串
    }

    /**
     * 获取视频详情
     * @param string $videoId 视频ID
     * @return array
     * @throws \Exception
     */
    public function getVideoDetail(string $videoId): array
    {
        return $this->request('GET', '/open/v1/video', ['id' => $videoId]);
    }

    /**
     * 创建定制数字人形象
     * @param string $name 定制数字人名称
     * @param string $materialVideo 外网可下载播放的视频文件链接
     * @param string $callback 回调地址
     * @param string $trainType 训练类型：voice-仅声音，figure-仅形象，both-声音和形象
     * @param string $language 语种：cn-中文，en-英文
     * @return string 返回定制的数字人ID
     * @throws \Exception
     */
    public function createCustomPerson(string $name, string $materialVideo, string $callback = '', string $trainType = 'both', string $language = 'cn'): string
    {
        $data = [
            'name' => $name,
            'material_video' => $materialVideo,
            'train_type' => $trainType,
            'language' => $language,
        ];

        // 添加回调地址（如果提供）
        if (!empty($callback)) {
            $data['callback'] = $callback;
        }

        return $this->request('POST', '/open/v1/create_customised_person', $data);
    }

    /**
     * 获取定制数字人详情
     * @param string $personId 数字人ID
     * @return array
     * @throws \Exception
     */
    public function getCustomPersonDetail(string $personId): array
    {
        return $this->request('GET', '/open/v1/customised_person', ['id' => $personId]);
    }

    /**
     * 清除Access Token缓存
     * @return void
     */
    public function clearTokenCache(): void
    {
        $cacheKey = self::TOKEN_CACHE_PREFIX . md5($this->merchantGuid);
        Cache::delete($cacheKey);
    }

    /**
     * 获取API错误码说明
     * @param int $code
     * @return string
     */
    public static function getErrorMessage(int $code): string
    {
        $errorMessages = [
            0 => '响应成功',
            400 => '传入参数格式错误',
            10400 => 'AccessToken验证失败，APP状态有误',
            40000 => '参数错误',
            40001 => '超出QPS限制',
            40002 => '制作视频时长到达上限',
            50000 => '系统内部错误',
            50011 => '作品存在不合法规文字内容',
            51000 => '系统错误',
        ];

        return $errorMessages[$code] ?? '未知错误';
    }

    public function getPortraitList(): array
    {
        // TODO: Implement getPortraitList() method.
    }
}
