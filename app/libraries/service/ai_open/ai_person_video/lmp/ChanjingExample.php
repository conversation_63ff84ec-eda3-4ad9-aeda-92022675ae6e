<?php

/**
 * 蝉镜数字人使用示例
 * @author: AI Assistant
 * @Time: 2025/01/13
 */

namespace app\libraries\service\ai_open\ai_person_video\lmp;

use app\libraries\service\ai_open\ai_person_video\entity\TextVideoEntity;
use app\libraries\service\ai_open\ai_person_video\entity\Mp3VideoEntity;

class ChanjingExample
{
    /**
     * 使用示例
     */
    public static function example()
    {
        try {
            // 初始化蝉镜数字人客户端
            $merchantGuid = 'your_merchant_guid_here';
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);
            
            // 示例1：创建文字转视频
            $textEntity = new TextVideoEntity(
                'zh-CN-XiaoxiaoNeural', // 声音ID
                'https://example.com/avatar.jpg', // 人物头像URL
                '你好，欢迎使用蝉镜数字人服务！' // 视频文本内容
            );
            
            // 调用文字转视频接口（需要具体API文档实现）
            // $textVideoResult = $chanjing->createTextVideo($textEntity);
            
            // 示例2：创建音频转视频
            $mp3Entity = new Mp3VideoEntity();
            // 设置音频相关参数...
            
            // 调用音频转视频接口（需要具体API文档实现）
            // $mp3VideoResult = $chanjing->createMp3Video($mp3Entity);
            
            // 示例3：获取视频结果
            // $videoId = 'your_video_id_here';
            // $videoResult = $chanjing->getVideoResult($videoId);
            
            // 示例4：获取可用声音列表
            // $voiceList = $chanjing->getVoiceList();
            
            // 示例5：清除Token缓存（当配置更新时）
            // $chanjing->clearTokenCache();
            
            echo "蝉镜数字人客户端初始化成功！\n";
            echo "请根据具体的API文档实现相应的接口方法。\n";
            
        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 配置检查示例
     */
    public static function checkConfig(string $merchantGuid)
    {
        try {
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);
            
            // 尝试获取Access Token来验证配置
            $reflection = new \ReflectionClass($chanjing);
            $method = $reflection->getMethod('getAccessToken');
            $method->setAccessible(true);
            
            $token = $method->invoke($chanjing);
            
            if ($token) {
                echo "蝉镜配置验证成功！\n";
                echo "Access Token: " . substr($token, 0, 20) . "...\n";
            }
            
        } catch (\Exception $e) {
            echo "配置验证失败: " . $e->getMessage() . "\n";
        }
    }
}

// 使用示例
// ChanjingExample::example();
// ChanjingExample::checkConfig('your_merchant_guid');
