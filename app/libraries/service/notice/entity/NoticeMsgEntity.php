<?php
/**
 * @author: xuzhengyang
 * @Time: 2023/1/9   16:06
 */


namespace app\libraries\service\notice\entity;

use app\libraries\utils\entity\BaseEntity;

class NoticeMsgEntity extends BaseEntity
{

    /**
     * 推送模板id
     * @var string
     */
    public string $templateId = '';


    /**
     * 用户uid
     * @var integer
     */
    public int $uid = 0;

    /**
     * 通知标题
     * @var string
     */
    public string $title = "";

    /**
     * 通知内容
     * @var array
     */
    public array $body = [];


    /**
     * 发送时间戳
     * @var int
     */
    public int $sendTime = 0;

    /**
     * 通知跳转地址
     * @var string
     */
    public string $noticeLinkUrl = '';


    /**
     * 关键业务id
     * @var string
     */
    public string $extendId = '';

    /**
     * 提前发送的天数
     * @var int
     */
    public int $beforeSendDay = 0;

    /**
     * 提前发送的时间点
     * @var string
     */
    public string $beforeSendTime = "10:00:00";

    /**
     * 单次发送标识，当前值不为空，发送时需检验当前值是否存在于redis中，不存在则不发送
     * @var string
     */
    public string $msgKey = '';


}