<?php

namespace app\libraries\service\file_extract;

use app\libraries\service\dify\ZhipuDatabaseService;
use app\libraries\service\dify\XinghuoDatabaseService;

/**
 * 文件萃取服务
 * 统一封装智谱和星火的文件萃取功能
 */
class FileExtractService 
{
    /**
     * 实时萃取文件内容
     * @param string $fileUrl 文件URL
     * @param string $provider 萃取服务商：zhipu|xinghuo
     * @return string 萃取的文本内容
     */
    public static function extractFileContent(string $fileUrl, string $provider = 'xinghuo'): string
    {
        if (empty($fileUrl)) {
            return '';
        }

        try {
            if ($provider === 'zhipu') {
                return self::extractWithZhipu($fileUrl);
            } 
            
            if ($provider === 'xinghuo') {
                return self::extractWithXinghuo($fileUrl);
            }

            throw new \Exception('不支持的萃取服务商：' . $provider);
            
        } catch (\Exception $e) {
            // 记录错误日志，但不抛出异常，避免影响正常聊天
            LogError('file_extract', '文件萃取失败', $e->getMessage(), [
                'fileUrl' => $fileUrl,
                'provider' => $provider
            ]);
            return ''; // 萃取失败返回空字符串
        }
    }

    /**
     * 使用智谱服务萃取文件
     * @param string $fileUrl
     * @return string
     * @throws \Exception
     */
    private static function extractWithZhipu(string $fileUrl): string
    {
        $service = new ZhipuDatabaseService();
        
        // 1. 上传文件到智谱
        $uploadResult = $service->uploadFile($fileUrl, 'file-extract');
        
        if (empty($uploadResult['id'])) {
            throw new \Exception('智谱文件上传失败');
        }
        
        // 2. 萃取文件内容
        $content = $service->extractFileContent($uploadResult['id']);
        
        return $content ?: '';
    }

    /**
     * 使用星火服务萃取文件
     * @param string $fileUrl
     * @return string
     * @throws \Exception
     */
    private static function extractWithXinghuo(string $fileUrl): string
    {
        $service = new XinghuoDatabaseService();
        
        // 1. 上传文件到星火
        $uploadResult = $service->uploadFile($fileUrl);
        
        if (empty($uploadResult['fileId'])) {
            throw new \Exception('星火文件上传失败');
        }
        
        // 2. 获取文档分块内容
        $chunks = $service->fileChunkLists($uploadResult['fileId']);
        
        $content = '';
        if (is_array($chunks)) {
            foreach ($chunks as $chunk) {
                if (isset($chunk['content'])) {
                    $content .= $chunk['content'] . "\n";
                }
            }
        }
        
        return trim($content);
    }
}
