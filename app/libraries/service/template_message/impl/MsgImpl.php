<?php
/**
 * @Author: zhangcc
 * @Date: 2022/8/17
 */

namespace app\libraries\service\template_message\impl;

use app\libraries\service\template_message\MsgInterface;
use app\libraries\utils\traits\Signleton;
use app\libraries\utils\wechat\EasyWeChatUtil;

class MsgImpl implements MsgInterface
{
    use Signleton;

    /**
     * 发送模版消息
     *
     * @param $wxItem
     * @param $params
     * @return string
     */
    public function send($wxItem, $params): string
    {
        $client = EasyWeChatUtil::getInstance()->initAccountApp($wxItem);

        LogInfo(
            __CLASS__ . '-' . __FUNCTION__,
            '公众号消息模版通知',
            '入口',
            $params
        );

        $res = $client->template_message->send($params);
        LogInfo(
            __CLASS__ . '-' . __FUNCTION__,
            '公众号消息模版通知',
            '返回值',
            ['res' => $res]
        );
        if (!empty($res['errcode'])) {
            LogError(
                __CLASS__ . '-' . __FUNCTION__,
                '公众号消息模版通知',
                '发送异常-',
                [$res['errmsg']]
            );
        }

        return strval($res['msgid'] ?? '');
    }
}