<?php

/**
 * @author: xuzhengyang
 * @Time: 2024/7/21   21:20
 */

namespace app\merchant\controller\admin;

use app\merchant\AdminBaseController;
use app\merchant\request\admin\MemberCardRequest;
use app\merchant\logic\admin\MemberCardLogic;

class MemberCard extends AdminBaseController
{
    /**
     * 创建会员卡
     * @param MemberCardRequest $request
     * @param MemberCardLogic $logic
     * @return \app\merchant\models\MemberCardGoodsModel
     */
    public function create(MemberCardRequest $request, MemberCardLogic $logic)
    {
        $data = $request->param();
        return $logic->create($data);
    }

    /**
     * 更新会员卡
     * @param MemberCardRequest $request
     * @param MemberCardLogic $logic
     * @return \app\merchant\models\MemberCardGoodsModel|array|mixed|\think\Model
     * @throws \Exception
     */
    public function update(MemberCardRequest $request, MemberCardLogic $logic)
    {
        $data = $request->param();
        return $logic->update($data);
    }

    /**
     * 会员卡列表
     * @param MemberCardRequest $request
     * @param MemberCardLogic $logic
     * @return \app\merchant\models\MemberCardGoodsModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index(MemberCardRequest $request, MemberCardLogic $logic)
    {
        $data = $request->param();
        return $logic->list($data);
    }


    /**
     * 删除会员卡
     * @param MemberCardRequest $request
     * @param MemberCardLogic $logic
     * @return array
     * @throws \Exception
     */
    public function delete(MemberCardRequest $request, MemberCardLogic $logic)
    {
        $data = $request->param();
        return $logic->delete($data);
    }
}
