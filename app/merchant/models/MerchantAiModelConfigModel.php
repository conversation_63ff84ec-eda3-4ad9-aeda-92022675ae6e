<?php

declare(strict_types=1);

namespace app\merchant\models;

use app\libraries\models\BaseModel;

/**
 * 商户AI模型配置模型
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商户GUID
 * @property string $modelName 模型名称
 * @property string $modelDisplayName 模型显示名称
 * @property string $modelProvider 模型厂商
 * @property string $modelType 模型类型
 * @property string $apiKey API密钥
 * @property string $apiSecret API密钥（部分厂商需要）
 * @property string $baseUrl API基础地址
 * @property string $deploymentName 部署名称
 * @property string $region 部署区域
 * @property string $modelVersion 模型版本
 * @property int $maxTokens 最大token数
 * @property float $temperature 默认温度参数
 * @property array $customParams 自定义参数
 * @property int $status 状态
 * @property int $isDefault 是否为该厂商的默认配置
 * @property int $sortOrder 排序权重
 * @property string $remark 备注说明
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 */
class MerchantAiModelConfigModel extends BaseModel
{
    protected $table = 'merchant_ai_model_config';

    // 状态常量
    public const STATUS_ENABLED = 1;   // 启用
    public const STATUS_DISABLED = 2;  // 禁用

    public const STATUS_TEXT = [
        self::STATUS_ENABLED => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    // 是否默认配置常量
    public const DEFAULT_NO = 0;   // 否
    public const DEFAULT_YES = 1;  // 是

    public const DEFAULT_TEXT = [
        self::DEFAULT_NO => '否',
        self::DEFAULT_YES => '是',
    ];

    public const MODEL_VENDOR_LIST = [
        [
            'name' => 'DeepSeek',
            'show_name' => 'DeepSeek',
            'sign' => 'deepseek',
        ],
        [
            'name' => '智谱清言',
            'show_name' => '智谱清言',
            'sign' => 'zhipu',
        ],
        [
            'name' => 'Azure OpenAI',
            'show_name' => '微软AI',
            'sign' => 'azure',
        ],
        [
            'name' => '千帆大模型',
            'show_name' => '文心一言',
            'sign' => 'wenxin',
        ],
        [
            'name' => '星火大模型',
            'show_name' => '讯飞星火',
            'sign' => 'xinghuo',
        ],
        [
            'name' => 'closeAI',
            'show_name' => 'closeAI',
            'sign' => 'closeai',
        ]
    ];

    // 模型类型常量
    public const MODEL_TYPE_CHAT = 'chat';           // 聊天模型
    public const MODEL_TYPE_EMBEDDING = 'embedding'; // 嵌入模型
    public const MODEL_TYPE_IMAGE = 'image';         // 图像模型
    public const MODEL_TYPE_AUDIO = 'audio';         // 音频模型
    public const MODEL_TYPE_VIDEO = 'video';         // 视频模型

    public const MODEL_TYPE_TEXT = [
        self::MODEL_TYPE_CHAT => '聊天模型',
        self::MODEL_TYPE_EMBEDDING => '嵌入模型',
        self::MODEL_TYPE_IMAGE => '图像模型',
        self::MODEL_TYPE_AUDIO => '音频模型',
        self::MODEL_TYPE_VIDEO => '视频模型',
    ];

    // 常见模型厂商常量
    public const PROVIDER_OPENAI = 'openai';
    public const PROVIDER_ANTHROPIC = 'anthropic';
    public const PROVIDER_BAIDU = 'baidu';
    public const PROVIDER_ALIBABA = 'alibaba';
    public const PROVIDER_TENCENT = 'tencent';
    public const PROVIDER_ZHIPU = 'zhipu';
    public const PROVIDER_MOONSHOT = 'moonshot';
    public const PROVIDER_DEEPSEEK = 'deepseek';
    public const PROVIDER_CLOSEAI = 'closeai';
    public const PROVIDER_GOOGLE = 'google';
    public const PROVIDER_MICROSOFT = 'microsoft';
    public const PROVIDER_HUAWEI = 'huawei';
    public const PROVIDER_XUNFEI = 'xunfei';
    public const PROVIDER_MINIMAX = 'minimax';
    public const PROVIDER_SENSETIME = 'sensetime';

    public const PROVIDER_TEXT = [
        self::PROVIDER_OPENAI => 'OpenAI',
        self::PROVIDER_ANTHROPIC => 'Anthropic',
        self::PROVIDER_BAIDU => '百度文心',
        self::PROVIDER_ALIBABA => '阿里云',
        self::PROVIDER_TENCENT => '腾讯云',
        self::PROVIDER_ZHIPU => '智谱AI',
        self::PROVIDER_MOONSHOT => 'Moonshot',
        self::PROVIDER_DEEPSEEK => 'DeepSeek',
        self::PROVIDER_CLOSEAI => 'CloseAI',
        self::PROVIDER_GOOGLE => 'Google',
        self::PROVIDER_MICROSOFT => 'Microsoft',
        self::PROVIDER_HUAWEI => '华为云',
        self::PROVIDER_XUNFEI => '科大讯飞',
        self::PROVIDER_MINIMAX => 'MiniMax',
        self::PROVIDER_SENSETIME => '商汤科技',
    ];




    /**
     * 获取完整的模型配置信息
     * @return array
     */
    public function getFullConfig(): array
    {
        return [
            'modelName' => $this->model_name,
            'modelDisplayName' => $this->model_display_name,
            'modelProvider' => $this->model_provider,
            'modelType' => $this->model_type,
            'apiKey' => $this->api_key,
            'apiSecret' => $this->api_secret,
            'baseUrl' => $this->base_url,
            'deploymentName' => $this->deployment_name,
            'region' => $this->region,
            'modelVersion' => $this->model_version,
            'maxTokens' => $this->max_tokens,
            'temperature' => $this->temperature,
            'customParams' => $this->custom_params ?? [],
        ];
    }

    /**
     * 创建新的模型配置
     * @param array $data
     * @return MerchantAiModelConfigModel|false
     */
    public static function createConfig(array $data)
    {
        $config = new self();
        $config->merchant_guid = $data['merchantGuid'];
        $config->model_name = $data['modelName'];
        $config->model_display_name = $data['modelDisplayName'];
        $config->model_provider = $data['modelProvider'];
        $config->model_type = $data['modelType'];
        $config->base_url = $data['baseUrl'] ?? '';
        $config->deployment_name = $data['deploymentName'] ?? '';
        $config->region = $data['region'] ?? '';
        $config->model_version = $data['modelVersion'] ?? '';
        $config->max_tokens = $data['maxTokens'] ?? 0;
        $config->temperature = $data['temperature'] ?? 0.0;
        $config->custom_params = $data['customParams'] ?? null;
        $config->status = $data['status'] ?? self::STATUS_ENABLED;
        $config->is_default = $data['isDefault'] ?? self::DEFAULT_NO;
        $config->sort_order = $data['sortOrder'] ?? 0;
        $config->remark = $data['remark'] ?? '';
        $config->api_key = $data['apiKey'] ?? '';
        $config->api_secret = $data['apiSecret'] ?? '';

        if ($config->save()) {
            return $config;
        }

        return false;
    }

    /**
     * 获取商户的模型配置列表
     * @param string $merchantGuid
     * @param array $filters
     * @return array
     */
    public static function getConfigList(string $merchantGuid, array $filters = []): array
    {
        $query = self::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('deleted_at', 0);

        // 按厂商筛选
        if (!empty($filters['modelProvider'])) {
            $query->where('model_provider', $filters['modelProvider']);
        }

        // 按类型筛选
        if (!empty($filters['modelType'])) {
            $query->where('model_type', $filters['modelType']);
        }

        // 按状态筛选
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->order('sort_order', 'desc')
            ->order('is_default', 'desc')
            ->order('create_time', 'desc')
            ->select()
            ->toArray();
    }
}
