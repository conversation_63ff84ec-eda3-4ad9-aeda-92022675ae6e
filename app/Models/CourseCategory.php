<?php

namespace App\Models;

class CourseCategory extends Base
{
    /**
     * @var string
     */
    protected $table = 'course_category';

    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            if (Course::query()->where('cate_id', $_it->id)->exists()) {
                throw new \Exception("分类下存在课程，无法删除");
            }
            return true;
        });
    }

    /**
     * 课程
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function course()
    {
        return $this->hasMany(Course::class, 'cate_id', 'id');
    }
}
