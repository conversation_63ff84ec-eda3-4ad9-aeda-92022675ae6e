<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\HasApiTokens;

class UserRoleCate extends Authenticatable
{

    public $table = "user_role_cates";
    protected $fillable = [
        'user_id',
        'cate_id',
        'type',
    ];

}
