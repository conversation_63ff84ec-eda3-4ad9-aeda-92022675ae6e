<?php
/*
 * @Author: 杨红兵
 * @Date: 2022-12-23 14:29:44
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-12-23 15:30:50
 */

namespace app\admin\logic\admin;

use app\admin\models\AdminRoleModel;
use app\constdir\PageConst;
use app\constdir\SysErrorCode;

class AdminRoleLogic
{
    /**
     * 创建角色信息
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 14:36:25
     *
     * @param array $params
     * @return array
     */
    public function create(array $params): array
    {
        /**
         * @var AdminRoleModel $role
         */
        $role = AdminRoleModel::getInstance()
            ->where('role_name', $params['roleName'])
            ->findOrEmpty();
        if (!$role->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '角色已存在');
        }
        $role = new AdminRoleModel();
        $role->roleName = $params['roleName'];
        $role->save();
        return [
            $role->sysId,
        ];
    }

    /**
     * 修改角色信息
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 14:37:38
     *
     * @param array $params
     * @return array
     */
    public function edit(array $params): array
    {
        /**
         * @var AdminRoleModel $role
         */
        $role = AdminRoleModel::getInstance()
            ->where('sys_id', $params['sysId'])
            ->findOrEmpty();
        if ($role->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '角色ID不存在');
        }
        $role->roleName = $params['roleName'];
        $role->save();
        return [
            $role->sysId,
        ];
    }

    /**
     * 删除角色
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 14:39:15
     *
     * @param array $params
     * @return array
     */
    public function del(array $params): array
    {
        /**
         * @var AdminRoleModel $role
         */
        $role = AdminRoleModel::getInstance()
            ->where('sys_id', $params['sysId'])
            ->findOrEmpty();
        if ($role->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '角色ID不存在');
        }
        $role->delete();
        return [];
    }

    /**
     * 获取角色ID数据
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 14:43:44
     *
     * @param array $params
     * @return array
     */
    public function get(array $params): array
    {
        /**
         * @var AdminRoleModel $role
         */
        $role = AdminRoleModel::getInstance()
            ->where('sys_id', $params['sysId'])
            ->findOrEmpty();
        if ($role->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '角色ID不存在');
        }
        return $role->toArray();
    }

    /**
     * 获取角色列表
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 14:41:02
     *
     * @param array $params
     * @return array
     */
    public function list(array $params): array
    {
        $roleModel = AdminRoleModel::getInstance();
        if (!empty($params['roleName'])) {
            $roleModel = $roleModel->where('role_name', 'like', '%' . $params['roleName'] . '%');
        }
        $queryRet = $roleModel
            ->paginate($params['pageSize'] ?? PageConst::PAGE_ROWS)
            ->toArray();
        if (empty($queryRet)) {
            return empty_list();
        }
        return $queryRet;
    }
}
