<?php
/**
 * 权限菜单控制器
 */

namespace app\admin\controller\admin;

use app\admin\AdminBaseController;
use app\admin\logic\admin\PermissionLogic;
use app\admin\request\admin\PermissionRequest;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Permission extends AdminBaseController
{
    protected $middleware = [
        'admin_token' => [],
        'must_login' => [],
        'signature' => [],
        'bind_merchant_admin' => [],
    ];

    /**
     * 获取权限菜单列表
     * @param PermissionRequest $request
     * @param PermissionLogic $logic
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list(PermissionRequest $request, PermissionLogic $logic): array
    {
        return $logic->list($request->param());
    }

    /**
     * 获取权限菜单详情
     * @param PermissionRequest $request
     * @param PermissionLogic $logic
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \app\libraries\exception\ApiException
     */
    public function get(PermissionRequest $request, PermissionLogic $logic): array
    {
        return $logic->get($request->param());
    }

    /**
     * 创建权限菜单
     * @param PermissionRequest $request
     * @param PermissionLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function create(PermissionRequest $request, PermissionLogic $logic): array
    {
        return $logic->create($request->param());
    }

    /**
     * 编辑权限菜单
     * @param PermissionRequest $request
     * @param PermissionLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function edit(PermissionRequest $request, PermissionLogic $logic): array
    {
        return $logic->edit($request->param());
    }

    /**
     * 删除权限菜单
     * @param PermissionRequest $request
     * @param PermissionLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function delete(PermissionRequest $request, PermissionLogic $logic): array
    {
        return $logic->delete($request->param());
    }
}
