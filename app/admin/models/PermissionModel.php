<?php
/**
 * 权限菜单模型
 */

namespace app\admin\models;

use app\libraries\models\BaseModel;
use app\libraries\utils\traits\Signleton;
use think\model\concern\SoftDelete;

/**
 * 权限菜单表
 *
 * @property int $sysId 主键ID
 * @property string $belongTo 归属: admin后台权限 merchant商家权限
 * @property string $name 名称
 * @property string $intro 描述
 * @property string $action 权限控制器@方法,如:controller@action
 * @property int $parentId 上级ID
 * @property string $type 类型: menu菜单 permission权限
 * @property int $isShow 菜单是否显示: 0隐藏 1显示
 * @property string $router 菜单路由
 * @property string $icon 菜单图标
 * @property int $sort 排序
 * @property int $isOpenMenu 是否打开菜单: 0否 1是
 * @property int $createTime 创建时间
 * @property int $updateTime 更新时间
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 */
class PermissionModel extends BaseModel
{
    use Signleton;
    use SoftDelete;

    /**
     * @var string
     */
    protected $name = 'permission';

    /**
     * 删除时间字段
     * @var string
     */
    protected $deleteTime = 'deleted_at';

    // 类型常量
    public const TYPE_MENU = 'menu'; // 菜单
    public const TYPE_PERMISSION = 'permission'; // 权限

    // 归属常量
    public const BELONG_TO_ADMIN = 'admin'; // 总后台
    public const BELONG_TO_MERCHANT = 'merchant'; // 商户后台

    // 显示状态常量
    public const SHOW_TRUE = 1; // 显示
    public const SHOW_FALSE = 0; // 隐藏

    /**
     * 是否存在action权限
     * @param string $action
     * @param string $belongTo
     * @return int
     */
    public static function getStatusByAction(string $action, string $belongTo = self::BELONG_TO_ADMIN): int
    {
        return self::getInstance()
            ->where('belong_to', $belongTo)
            ->where('type', self::TYPE_PERMISSION)
            ->where('action', $action)
            ->limit(1)
            ->count();
    }
}
