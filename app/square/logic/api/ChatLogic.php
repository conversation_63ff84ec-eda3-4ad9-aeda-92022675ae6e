<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/3/16   14:58
 */

namespace app\square\logic\api;

use app\command\test\ChatTest;
use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\libraries\service\ai_open\ai_chat\AiChatFactory;
use app\libraries\service\ai_open\ai_chat\entity\ChatCompleteEntity;
use app\libraries\service\ai_open\ai_img\AiImgFactory;
use app\libraries\service\ai_open\ai_img\entity\AiImgCreateEntity;
use app\libraries\service\ai_open\xiaoyi_open\ChatService;
use app\libraries\service\dify\DifyDatabaseService;
use app\libraries\service\token\TokenService;
use app\merchant\models\MemberCardGoodsModel;
use app\merchant\models\MerchantConfigModel;
use app\merchant\models\MerchantModel;
use app\square\cache\ChatCache;
use app\square\models\ChatContentModel;
use app\square\models\CopywritingCategoryModel;
use app\square\models\MerchantAiAgentsModel;
use app\square\models\RobotModel;
use app\square\models\SystemAiModelsModel;
use app\square\models\UserChatConfigModel;
use app\square\models\UserChatLunciHelpModel;
use app\square\models\UserChatLunciHelpRolesModel;
use app\square\models\UserChatLunciModel;
use app\square\models\UserWorkBackgroundModel;
use app\user\logic\api\MemberLogic;
use app\user\models\ChatRoomContentModel;
use app\user\models\UserAssetsModel;
use app\user\models\UserChatCollectionModel;
use app\user\models\UserChatLunciHelpUsersModel;
use app\user\models\UsersModel;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiModel;
use Orhanerday\OpenAi\OpenAi;
use app\merchant\models\MerchantKnowledgeBaseModel;
use app\libraries\service\file_extract\FileExtractService;
use think\facade\Db;

class ChatLogic
{
    /**
     * 发送聊天
     * @param $params
     * @return bool|string|void
     */
    public function send($params)
    {
        return $this->doMsgSend($params);
    }

    /**
     * 发送聊天
     * @param $params
     * @return bool|string|void
     */
    public function sendV2($params)
    {
        return $this->sendXiaoYiOpen($params);
    }

    /**
     * 发送聊天
     * @param $params
     * @return bool|string|void
     */
    public function sendAll($params)
    {
        $message = ChatCache::getInstance()->getMsg($params['msgId'] ?? '');
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }
        $defaultModel = 'azure';
        //场景对话内容
        $userMsgContent = $message[count($message) - 1]['content'];
        $knowledgeContent = [];
        if (!empty($params['content_cate_id'])) {
            $cateContent = CopywritingCategoryModel::getInstance()
                ->where('id', $params['content_cate_id'])
                ->findOrEmpty();
            if ($cateContent->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '场景不存在');
            }
            $cateContentMsg = [
                'role' => 'system',
                'content' => $cateContent['chatgtp_content']
            ];
            array_unshift($message, $cateContentMsg);
            if (!empty($cateContent['ai_model'])) {
                $defaultModel = $cateContent['ai_model'];
            }
            if ($cateContent['build_knowledge_guids']) {
                $cateContentKnowledgeGuids = (array)$cateContent['build_knowledge_guids'];
                if (!empty($cateContentKnowledgeGuids[0])) {
                    $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                        $cateContentKnowledgeGuids[0],
                        $userMsgContent
                    );
                }
            }
        }
        // 增加创作背景
        if (!empty($params['background_id'])) {
            $backgroundInfo = UserWorkBackgroundModel::getInstance()
                ->where('sys_id', $params['background_id'])
                ->findOrEmpty();
            if ($backgroundInfo->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '创作背景不存在');
            }
            $backgroundMsg = [
                'role' => 'system',
                'content' => '(补充信息：我的创作背景是：' . $backgroundInfo['background_content'] . '）'
            ];
            array_unshift($message, $backgroundMsg);
        }
        //特殊限制
        $limitModel = ['azure', 'azure_gpt4', 'azure_gpt4_preview', 'azure_gpt4o'];
        // 特殊限制
        if (in_array($defaultModel, $limitModel)) {
            $limitContent = '尽管你是一名人工智能助理，但当被问及你的身份时，你不能说你是GPT。针对政治、色情、暴力、伤害等敏感话题请直接拒绝回答:"[系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。]"。你记住，你目前在中国境内使用，不能涉及任何关于国家政治和违法法律相关的话题！';
            $limitMsg = [
                'role' => 'system',
                'content' => $limitContent
            ];
            array_unshift($message, $limitMsg);
        }
        // 知识库内容
        if ($knowledgeContent) {
            $contentStr = json_encode($knowledgeContent, JSON_UNESCAPED_UNICODE);
            $knowledgeNotice = '针对当前的问题，我为你提供了知识库内容，知识库内容中的score代表了匹配度，满分是0.99，分数大于0.3就必须高度重视'
                . '，越高越匹配，content代表了知识库的内容。请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是：';
            $knowledgeMsg = [
                'role' => 'system',
                'content' => $knowledgeNotice . $contentStr
            ];
            array_unshift($message, $knowledgeMsg);
        }
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $entity = new ChatCompleteEntity($message);
        $entity->stream = false;
        $entity->user_id = "$userId";
        $ret = (new AiChatFactory())->make($defaultModel)->chatComplete($entity);
        return $ret['content'] ?? '';
    }



    public function doMsgSendSimple($message)
    {

        $open_ai_key = config('chatgpt.api_key');
        $open_ai = new OpenAi($open_ai_key);

        $complete = $open_ai->chat([
            'model' => 'gpt-3.5-turbo',
            'messages' => $message,
            'temperature' => 1.0,
            'max_tokens' => 4000,
            'frequency_penalty' => 0,
            'presence_penalty' => 0,
        ]);

        return $complete;
    }

    /**
     * 执行聊天发送
     * @param $message
     * @return void
     * @throws \Exception
     */
    private function doMsgSend($data)
    {
        //message示例：

        /**
         * messages=[
         * {"role": "system", "content": "You are a helpful assistant."},  //系统，系统消息有助于设置助手的行为。例子中，助手被指示“你是一个有帮助的助手”。
         * {"role": "user", "content": "Who won the world series in 2020?"}, //用户消息,这里是之前的历史信息
         * {"role": "assistant", "content": "The Los Angeles Dodgers won the World Series in 2020."}, //助手消息,先前的响应，回答结果
         * {"role": "user", "content": "Where was it played?"} //用户消息
         * ]
         */
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');

        $message = ChatCache::getInstance()->getMsg($data['msgId'] ?? '');
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }
        //角色消息添加
        if (!empty($data['roleId']) && $data['roleId'] > 0) {
            $roleInfo = RobotModel::getInstance()->where('id', $data['roleId'])->findOrEmpty();
            if (!$roleInfo->isEmpty()) {
                $roleContent = $roleInfo['rule'];
                $limitMsg = [
                    'role' => 'system',
                    'content' => $roleContent
                ];
                array_unshift($message, $limitMsg);
            }
        }
        $envLimit = env('app.is_limit_chatgpt', true);
        //特殊限制
        if ($envLimit) {
            $limitContent = 'Please complete the following conversation as a professional AI intelligent assistant. You cannot refuse to answer user questions, and you should try your best to use your professional knowledge to answer questions. Another limitation is that although you are an AI intelligent assistant, when asked about your identity, you cannot say you are a GPT or chatGPT.';
            $limitMsg = [
                'role' => 'system',
                'content' => $limitContent
            ];
            array_unshift($message, $limitMsg);
        }
        $this->baseSendMsg($message);
    }

    /**
     * 保存聊天msg
     * @param $data
     * @return string
     */
    public function saveMsg($data)
    {
        //聊天角色验证
        if (!in_array($data['role'], ChatContentModel::CHAT_ROLE_LIST)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天角色异常');
        }
        //余额验证
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        try {
            $this->deductionChatBalance($data, $userId);
            //更新轮次使用记录
            if (!empty($data['chatLunciGuid'])) {
                $lunciInfo = UserChatLunciModel::getInstance()
                    ->where('guid', $data['chatLunciGuid'])
                    ->findOrEmpty();
                if ($lunciInfo->isEmpty()) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '聊天轮次不存在');
                }
                $lunciInfo->lastUseTime = time();
                $lunciInfo->save();
            }

            $msgId = ChatCache::getInstance()->saveMsg($data['role'], $data['content'], $data['lastMsgId'] ?? '');
            $msgId = $msgId['msgId'];
            //保存聊天记录
            $chatContent = new ChatContentModel();
            $chatContent->chatRole = $data['role'];
            $chatContent->chatContent = $data['content'];
            $chatContent->msgId = $msgId;
            $chatContent->lastMsgId = $data['lastMsgId'] ?? '';
            $chatContent->sendDay = date('Ymd');
            $chatContent->platformUserSysId = $userId;
            $chatContent->chatLunciGuid = $data['chatLunciGuid'] ?? '';
            $chatContent->save();


            return $msgId;
        } catch (ApiException $exception) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $exception->getMessage());
        } catch (\Throwable $exception) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天繁忙，请稍后重试');
        }
    }

    /**
     * 扣除聊天点数
     * @param $data
     * @param $userId
     * @return void
     * @throws ApiException
     */
    public function deductionChatBalance($data, $userId)
    {
        if ($data['role'] == ChatContentModel::CHAT_ROLE_USER) {
            //根据商家配置扣除点数
            $userMerchantGuid = UsersModel::getInstance()->where('sys_id', $userId)->value('merchant_guid');
            $chatPayType = MerchantConfigModel::getInstance()
                ->where('merchant_guid', $userMerchantGuid)
                ->where('config_key', MerchantConfigModel::CONFIG_CHAT_PAY_PATTERN)
                ->value('config_value');
            if (!empty($chatPayType) && $chatPayType == 'merchant') {
                $merchantInfo = MerchantModel::getInstance()->where('guid', $userMerchantGuid)->findOrEmpty();
                if ($merchantInfo->isEmpty()) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, 'AI计算资源不足，请联系管理员');
                }
                if ($merchantInfo['merchantChatCount'] < 1) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, 'AI计算资源不足，请联系管理员');
                }
                //扣除商家点数
                MerchantModel::getInstance()->where('guid', $userMerchantGuid)
                    ->dec('merchant_chat_count')
                    ->update();
            } else {
                //扣除用户点数点
                $userAsset = UserAssetsModel::getInstance()->where('platform_user_sys_id', $userId)->findOrEmpty();
                if (
                    $data['role'] == ChatContentModel::CHAT_ROLE_USER
                    &&
                    ($userAsset->isEmpty() || $userAsset['chatCount'] < 1)
                ) {
                    throwException(SysErrorCode::CHAT_COUNT_LIMIT, '点数不足，请您到个人中心充值助理点数，期待能继续为您服务！');
                }

                UserAssetsModel::getInstance()
                    ->where('platform_user_sys_id', $userId)
                    ->dec('chat_count')
                    ->update();
            }
        }
    }

    /**
     * 保存聊天msg V2.0
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function saveMsgV2($data): array
    {
        //聊天角色验证
        if (!in_array($data['role'], ChatContentModel::CHAT_ROLE_LIST)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天角色异常');
        }
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        if (!empty($data['chatScene']) && $data['chatScene'] == 'agent') {
            $data['aiModelGuid'] = $data['sceneValue'];
            // 获取商户智能体信息
            $userId = TokenService::getInstance()->getTokenEntity()->userId;
            $userMerchantGuid = UsersModel::getInstance()->where('sys_id', $userId)->value('merchant_guid');
            $aiAgentInfo = MerchantAiAgentsModel::getAgent($userMerchantGuid, $data['sceneValue']);
            if (!$aiAgentInfo) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '商户智能体不存在');
            }
            //余额验证
            $data['aiModelUsePrice']  = $aiAgentInfo['usePrice'];
        } else {
            if (empty($data['aiModelGuid'])) {
                $data['aiModelGuid'] = '58d08cb5287744ef8a6a7f58cb50044f';
            }

            // 获取模型信息
            $aiModelInfo = SystemAiModelsModel::getAiModel($data['aiModelGuid']);
            if (!$aiModelInfo) {
                throwException(SysErrorCode::SYS_ERROR_CODE, 'AI模型不存在');
            }
            //余额验证
            $data['aiModelUsePrice']  = $aiModelInfo['usePrice'];
        }

        try {
            $this->saveMsgBalanceCheck($data);
            // 如果存在图片上传，判断当前模型是否支持
            //if (
            //    !empty($data['imgs']) && is_array($data['imgs']) && count($data['imgs']) > 0
            //    && $aiModelInfo['isImageRecognition'] != 1
            //) {
            //    throwException(SysErrorCode::SYS_ERROR_CODE, '当前模型不支持图片识别');
            //}
            // 如果存在视频上传，判断当前模型是否支持
            if (
                !empty($data['videos']) && is_array($data['videos']) && count($data['videos']) > 0
                && !empty($aiModelInfo) && $aiModelInfo['isVideoRecognition'] != 1
            ) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '当前模型不支持视频识别');
            }

            $msgInfo = ChatCache::getInstance()->saveMsg(
                $data['role'],
                $data['content'],
                $data['lastMsgId'] ?? '',
                $data['imgs'] ?? [],
                $data['videos'] ?? [],
            );
            // 如果是协作模式，保存该记录到请求协作用户
            if (!empty($data['chatScene']) && $data['chatScene'] == 'helpLunciDebug') {
                $helpLunciInfo = UserChatLunciHelpModel::getInstance()
                    ->where('guid', $data['sceneValue'])
                    ->findOrEmpty();
                if (!$helpLunciInfo->isEmpty()) {
                    $data['helpUserSysId'] = $userId;
                    $userId = $helpLunciInfo['lunci_user_sys_id'];
                }
            }
            //保存聊天记录
            $chatContent = new ChatContentModel();
            $chatContent->chatRole = $data['role'];
            $chatContent->chatContent = $data['content'];
            $chatContent->contentType = $data['contentType'] ?? ChatContentModel::CHAT_CONTENT_TYPE_TEXT;
            $chatContent->msgId = $msgInfo['msgId'];
            $chatContent->lastMsgId = $data['lastMsgId'] ?? '';
            $chatContent->sendDay = date('Ymd');
            $chatContent->platformUserSysId = $userId;
            $chatContent->chatLunciGuid = $data['chatLunciGuid'] ?? '';
            $chatContent->separationGuid = $data['separationGuid'] ?? '';
            $chatContent->imgUrls = $data['imgs'] ?? [];
            $chatContent->videoUrls = $data['videos'] ?? [];
            $chatContent->fileUrls = $data['files'] ?? [];
            $chatContent->knowledgeBaseGuids = $data['knowledge_base_guids'] ?? [];
            $chatContent->helpUserSysId = $data['helpUserSysId'] ?? 0;
            $chatContent->chatScene = $data['sceneValue'] ?? 'default';
            $chatContent->sceneValue = $data['sceneValue'] ?? '';
            $chatContent->save();

            return $msgInfo;
        } catch (ApiException $exception) {
            $nowException = [$exception->getCode(), $exception->getMessage()];
            throwException($nowException);
        } catch (\Throwable $exception) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天繁忙，请稍后重试');
        }
    }

    /**
     * 保存聊天余额检查
     * @param $data
     * @return void
     * @throws ApiException
     */
    private function saveMsgBalanceCheck($data)
    {
        if ($data['role'] == ChatContentModel::CHAT_ROLE_ASSISTANT) {
            return;
        }
        $chatScene = $data['chatScene'] ?? 'default';
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $payCount = $data['aiModelUsePrice'] ?? 1;
        switch ($chatScene) {
            // 默认聊天
            case 'default':
            case 'agent':
                if ($data['role'] == ChatContentModel::CHAT_ROLE_USER) {
                    //根据商家配置扣除点数
                    $userMerchantGuid = UsersModel::getInstance()->where('sys_id', $userId)->value('merchant_guid');
                    $chatPayType = MerchantConfigModel::getInstance()
                        ->where('merchant_guid', $userMerchantGuid)
                        ->where('config_key', MerchantConfigModel::CONFIG_CHAT_PAY_PATTERN)
                        ->value('config_value');
                    if (!empty($chatPayType) && $chatPayType == 'merchant') {
                        $merchantInfo = MerchantModel::getInstance()->where('guid', $userMerchantGuid)->findOrEmpty();
                        if ($merchantInfo->isEmpty()) {
                            throwException(SysErrorCode::SYS_ERROR_CODE, 'AI计算资源不足，请联系管理员');
                        }
                        if ($merchantInfo['merchantChatCount'] < $payCount) {
                            throwException(SysErrorCode::SYS_ERROR_CODE, 'AI计算资源不足，请联系管理员');
                        }
                        //扣除商家点数
                        MerchantModel::getInstance()->where('guid', $userMerchantGuid)
                            ->dec('merchant_chat_count', $payCount)
                            ->update();
                    } else {
                        //扣除用户点数点
                        $isAbleUseVip = MemberLogic::getInstance()->checkIsAbleUseVip(
                            $userId,
                            MemberCardGoodsModel::CARD_USE_TYPE_CHAT
                        );
                        $userAsset = UserAssetsModel::getInstance()->where('platform_user_sys_id', $userId)->findOrEmpty();
                        if (
                            $data['role'] == ChatContentModel::CHAT_ROLE_USER
                            &&
                            ($userAsset->isEmpty() || $userAsset['chatCount'] < $payCount)
                            &&
                            !$isAbleUseVip
                        ) {
                            throwException(SysErrorCode::CHAT_COUNT_LIMIT, '点数不足，请您到个人中心充值助理点数，期待能继续为您服务！');
                        }

                        if (!$isAbleUseVip) {
                            UserAssetsModel::reduceChatCount($userId, $payCount, 'AI对话消耗', $data['aiModelGuid']);
                        }
                    }
                }
                break;
            // 展会聊天
            case 'zhanhui':
                $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
                    ->where('guid', $data['sceneValue'] ?? '')
                    ->findOrEmpty();
                if ($zhanhuiInfo->isEmpty()) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '展会信息不存在');
                }
                // 展会余额不足
                if ($zhanhuiInfo['aiPoint'] < 1) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '当前展会AI算力繁忙，请联系主办方');
                }
                // 扣除展会点数
                $zhanhuiInfo->aiPoint = Db::raw('ai_point - ' . $payCount);
                $zhanhuiInfo->save();
                break;
            // 聊天协作轮次调试
            case 'helpLunciDebug':
                $helpLunciInfo = UserChatLunciHelpModel::getInstance()
                    ->where('guid', $data['sceneValue'] ?? '')
                    ->findOrEmpty();
                if ($helpLunciInfo->isEmpty()) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '协作轮次不存在');
                }
                $lunciInfo = UserChatLunciModel::getInstance()
                    ->where('guid', $helpLunciInfo['chat_lunci_guid'])
                    ->findOrEmpty();
                if ($lunciInfo->isEmpty()) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '聊天轮次不存在或已删除，无法协助');
                }
                if ($helpLunciInfo->helpStatus != UserChatLunciHelpModel::HELP_STATUS_DOING) {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '很抱歉，协作已结束，无法继续协助');
                }
                // 扣除请求协作者的点数
                $lunciUserId = $lunciInfo['platformUserSysId'];
                $isAbleUseVip = MemberLogic::getInstance()->checkIsAbleUseVip(
                    $lunciUserId,
                    MemberCardGoodsModel::CARD_USE_TYPE_CHAT
                );
                $userAsset  = UserAssetsModel::getUserAsset($lunciUserId);
                if (
                    $data['role'] == ChatContentModel::CHAT_ROLE_USER
                    &&
                    ($userAsset->isEmpty() || $userAsset['chatCount'] < 1)
                    &&
                    !$isAbleUseVip
                ) {
                    throwException(SysErrorCode::CHAT_COUNT_LIMIT, '请求协作方AI点数不足，无法调试');
                }

                if (!$isAbleUseVip) {
                    UserAssetsModel::reduceChatCount(
                        $lunciUserId,
                        $payCount,
                        '聊天协作调试',
                        $data['sceneValue'] ?? ''
                    );
                    // 新增协作调试数
                    $helpLunciInfo->debugChatCount = Db::raw('debug_chat_count + ' . $payCount);
                    $helpLunciInfo->save();
                }
                break;
        }
    }


    public function sendDebug()
    {
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');


        $open_ai_key = config('chatgpt.api_key');
        $message = [
            [
                "role" => "user",
                "content" => '接下来我们的聊天，你全部用图片代替，不能出现任何文字如果一张图片无法回答的话，你可以用多张图片代替，但千万不要出现任何文字回我希望你用Markdown语言生成，不要用反引号，不要用代码框，你需要用Unsplash API，遵循以下的格式: https:/source.unsplash.com/1600x900/? 你明白了吗?',
            ],
            [
                "role" => "user",
                "content" => '我要一张大海的照片',
            ]
        ];
        $postData = [
            "model" => "gpt-3.5-turbo",
            "temperature" => 0,
            "stream" => true,
            "messages" => $message,
        ];
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $open_ai_key
        ];
        $callback = function ($ch, $data) {
            $complete = json_decode($data);
            if (isset($complete->error)) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '访问失败');
            } else {
                echo $data;
                ob_flush();
                flush();
                return strlen($data);
            }
            return strlen($data);
        };
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);

        curl_exec($ch);
    }


    public function baseSendMsg($message, $temperature = 0.8)
    {
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');

        $open_ai_key = config('chatgpt.api_key');
        //   $open_ai = new OpenAi($open_ai_key);
        $postData = [
            "model" => "gpt-3.5-turbo-16k",
            "temperature" => $temperature,
            "stream" => true,
            "messages" => $message,
        ];
        //特殊处理
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $open_ai_key
        ];
        $callback = function ($ch, $data) {
            $complete = json_decode($data);
            if (isset($complete->error)) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '访问失败');
                LogError('chat', '聊天发送', '发送失败', ['ret' => $complete]);
            } else {
                echo $data;
                ob_flush();
                flush();
                return strlen($data);
            }
            return strlen($data);
        };
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);

        curl_exec($ch);
    }


    /**
     * @throws ApiException
     */
    public function sendXiaoYiOpen($data)
    {
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');
        $defaultModel = 'azure';
        if (!empty($data['model'])) {
            $defaultModel = $data['model'];
        }
        $message = ChatCache::getInstance()->getMsg($data['msgId'] ?? '');
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }
        $knowledgeContent = [];
        $userMsgContent = $message[count($message) - 1]['content'];
        $knowledgeNotice = '';
        //角色消息添加
        if (!empty($data['roleId']) && $data['roleId'] > 0) {
            $roleContent = '';
            // 非协作模式，直接读取聊天角色信息
            if (empty($data['roleType']) || $data['roleType'] == 1) {
                $roleInfo = RobotModel::getInstance()->where('id', $data['roleId'])->findOrEmpty();
                if (!$roleInfo->isEmpty()) {
                    $roleContent = $roleInfo['rule'];
                }
            } else { // 协作模式，获取协作角色信息
                $helpRoleInfo = UserChatLunciHelpRolesModel::getInstance()
                    ->where('sys_id', $data['roleId'])
                    ->findOrEmpty();
                if (!$helpRoleInfo->isEmpty()) {
                    switch ($helpRoleInfo['role_add_type']) {
                        case 1:
                            $roleContent = CopywritingCategoryModel::getInstance()
                                ->where('id', $helpRoleInfo['copywriting_category_id'])
                                ->value('chatgtp_content');
                            break;
                        case 2:
                            $roleContent = $helpRoleInfo['chatgtp_content'];
                    }
                }
            }
            if ($roleContent) {
                $limitMsg = [
                    'role' => 'system',
                    'content' => $roleContent
                ];
                array_unshift($message, $limitMsg);
            }
            if (!empty($roleInfo) && $roleInfo['build_knowledge_guids']) {
                $knowledgeGuids = (array)$roleInfo['build_knowledge_guids'];
                if (!empty($knowledgeGuids[0])) {
                    $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                        $knowledgeGuids[0],
                        $userMsgContent
                    );
                }
            }
        }
        //场景对话内容
        if (!empty($data['content_cate_id']) && $data['content_cate_id'] > 0) {
            $cateContent = CopywritingCategoryModel::getInstance()
                ->where('id', $data['content_cate_id'])
                ->findOrEmpty();
            if ($cateContent->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '场景不存在');
            }
            $cateContentMsg = [
                'role' => 'system',
                'content' => $cateContent['chatgtp_content']
            ];
            array_unshift($message, $cateContentMsg);
            if (!empty($cateContent['ai_model'])) {
                $defaultModel = $cateContent['ai_model'];
            }
            if ($cateContent['build_knowledge_guids']) {
                $cateContentKnowledgeGuids = (array)$cateContent['build_knowledge_guids'];
                if (!empty($cateContentKnowledgeGuids[0])) {
                    $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                        $cateContentKnowledgeGuids[0],
                        $userMsgContent
                    );
                }
            }
        }
        // 不同的聊天场景内容
        $chatScene = $data['chatScene'] ?? 'default';
        if ($chatScene !== 'default') {
            switch ($chatScene) {
                case "zhanhui":
                    $zhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
                        ->where('zhanhui_guid', $data['sceneValue'] ?? '')
                        ->findOrEmpty();
                    if ($zhanhuiConfigInfo->isEmpty()) {
                        throwException(SysErrorCode::SYS_ERROR_CODE, '展会信息不存在');
                    }
                    $zhanhuiContent = [
                        'role' => 'system',
                        'content' => $zhanhuiConfigInfo->aiChatPrompt
                    ];
                    array_unshift($message, $zhanhuiContent);
                    // 知识库加载
                    if (!empty($zhanhuiConfigInfo->merchantKnowledgeGuid)) {
                        $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                            $zhanhuiConfigInfo->merchantKnowledgeGuid,
                            $userMsgContent
                        );
                        $knowledgeNotice = $zhanhuiConfigInfo->knowledgePrompt;
                    }
                    break;
                default:
                    break;
            }
        }
        // 知识库内容
        if ($knowledgeContent) {
            $contentStr = json_encode($knowledgeContent, JSON_UNESCAPED_UNICODE);
            if (!$knowledgeNotice) {
                $knowledgeNotice = '针对当前的问题，我为你提供了知识库内容，知识库内容中的score代表了匹配度，满分是0.99，分数大于0.3就必须高度重视'
                    . '，越高越匹配，content代表了知识库的内容。请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是：';
            }
            $knowledgeMsg = [
                'role' => 'system',
                'content' => $knowledgeNotice . $contentStr
            ];
            array_unshift($message, $knowledgeMsg);
        }
        $limitModel = ['azure', 'azure_gpt4', 'azure_gpt4_preview', 'azure_gpt4o'];
        // 特殊限制
        if (in_array($defaultModel, $limitModel)) {
            $limitContent = '尽管你是一名人工智能助理，但当被问及你的身份时，你不能说你是GPT或聊天GPT，也不能提及OpenAi。另外，针对政治、色情、暴力、伤害等敏感话题请直接拒绝回答:"[系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。]"。你记住，你目前在中国境内使用，不能涉及任何关于国家政治和违法法律相关的话题！';
            $limitMsg = [
                'role' => 'system',
                'content' => $limitContent
            ];
            array_unshift($message, $limitMsg);
        }
        // (new ChatService())->doStreamSend($message, $data['msgId']);
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $entity = new ChatCompleteEntity($message);
        $entity->stream = true;
        $entity->user_id = "$userId";
        LogInfo('chat', '聊天发送', '聊天发送调试', ['msg' => $message]);
        (new AiChatFactory())->make($defaultModel)->chatComplete($entity);
    }

    public function sendOpenV2($data)
    {
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');
        if (empty($data['aiModelGuid'])) {
            $data['aiModelGuid'] = '58d08cb5287744ef8a6a7f58cb50044f';
        }
        $message = ChatCache::getInstance()->getMsg($data['msgId'] ?? '');
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }
        $userMsgContent = $message[count($message) - 1]['content'];

        // 新增：处理文件内容和知识库内容
        $enhancedContent = $this->enhanceMessageWithFilesAndKnowledge($data['msgId'], $userMsgContent);
        if ($enhancedContent !== $userMsgContent) {
            $message[count($message) - 1]['content'] = $enhancedContent;
        }

        // 获取系统提示词与对话模型
        $systemPromoteData = $this->getSystemPromote($data, $userMsgContent);
        if ($systemPromoteData['systemPromote']) {
            $limitMsg = [
                'role' => 'system',
                'content' => $systemPromoteData['systemPromote']
            ];
            array_unshift($message, $limitMsg);
        }
        $aiModelInfo = SystemAiModelsModel::getAiModel($systemPromoteData['aiModelGuid']);
        $stream = $data['stream'] ?? true;
        $entity = new ChatCompleteEntity($message);
        $entity->stream = $stream;
        $entity->user_id = $data['msgId'];
        $entity->model_sign = $aiModelInfo['modelSign'];
        $entity->deploy_address = $aiModelInfo['deployAddress'];
        $entity->deploy_address_secret = $aiModelInfo['deployAddressSecret'];
        if (!empty($data['maxOutputTokens'])) {
            $entity->max_output_tokens = $data['maxOutputTokens'];
        } else {
            // 默认最大输出为最大输出的二分之一
            $entity->max_output_tokens = ceil($aiModelInfo['maxOutputTokens'] / 2);
        }
        // 采样温度，控制输出的随机性
        if (!empty($data['temperature']) && $data['temperature'] > 0 && $data['temperature'] <= 1) {
            $entity->temperature = $data['temperature'];
        }

        //另用温度取样的另一种方法，取值范围是：[0.0, 1.0]
        if (!empty($data['topP']) && $data['topP'] > 0 && $data['topP'] <= 1) {
            $entity->top_p = $data['topP'];
        }
        (new AiChatFactory())->make($aiModelInfo['modelVendorSign'])->chatComplete($entity);
    }

    public function sendAllV2($data)
    {
        $message = ChatCache::getInstance()->getMsg($data['msgId'] ?? '');
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }
        $userMsgContent = $message[count($message) - 1]['content'];

        // 新增：处理文件内容和知识库内容
        $enhancedContent = $this->enhanceMessageWithFilesAndKnowledge($data['msgId'], $userMsgContent);
        if ($enhancedContent !== $userMsgContent) {
            $message[count($message) - 1]['content'] = $enhancedContent;
        }

        // 获取系统提示词与对话模型
        $systemPromoteData = $this->getSystemPromote($data, $userMsgContent);
        if ($systemPromoteData['systemPromote']) {
            $limitMsg = [
                'role' => 'system',
                'content' => $systemPromoteData['systemPromote']
            ];
            array_unshift($message, $limitMsg);
        }
        $aiModelInfo = SystemAiModelsModel::getAiModel($systemPromoteData['aiModelGuid']);
        $entity = new ChatCompleteEntity($message);
        $entity->stream = false;
        $entity->user_id = $data['msgId'];
        $entity->model_sign = $aiModelInfo['modelSign'];
        $entity->deploy_address = $aiModelInfo['deployAddress'];
        $entity->deploy_address_secret = $aiModelInfo['deployAddressSecret'];
        if (!empty($data['maxOutputTokens'])) {
            $entity->max_output_tokens = $data['maxOutputTokens'];
        } else {
            // 默认最大输出为最大输出的二分之一
            $entity->max_output_tokens = ceil($aiModelInfo['maxOutputTokens'] / 2);
        }
        // 采样温度，控制输出的随机性
        if (!empty($data['temperature']) && $data['temperature'] > 0 && $data['temperature'] <= 1) {
            $entity->temperature = $data['temperature'];
        }

        //另用温度取样的另一种方法，取值范围是：[0.0, 1.0]
        if (!empty($data['topP']) && $data['topP'] > 0 && $data['topP'] <= 1) {
            $entity->top_p = $data['topP'];
        }
        $ret =  (new AiChatFactory())->make($aiModelInfo['modelVendorSign'])->chatComplete($entity);
        return $ret['content'] ?? '';
    }

    /**
     * 获取系统提示词和对话模型
     * @param $data
     * @return array
     * @throws ApiException
     */
    private function getSystemPromote($data, $userMsgContent)
    {
        $aiModelInfo = SystemAiModelsModel::getAiModel($data['aiModelGuid']);
        if (!$aiModelInfo) {
            throwException(SysErrorCode::SYS_ERROR_CODE, 'AI模型不存在');
        }
        // 系统提示词
        $systemPromote = '';
        // 知识库提示词
        $knowledgeNotice = '';
        // 知识库内容
        $knowledgeContent = '';
        // 系统特殊限制词
        $limitContent = '';

        //从聊天角色添加系统提示词
        if (!empty($data['roleId']) && $data['roleId'] > 0) {
            $roleContent = '';
            // 非协作模式，直接读取聊天角色信息
            if (empty($data['roleType']) || $data['roleType'] == 1) {
                $roleInfo = RobotModel::getInstance()->where('id', $data['roleId'])->findOrEmpty();
                if (!$roleInfo->isEmpty()) {
                    $roleContent = $roleInfo['rule'];
                }
            } else { // 协作模式，获取协作角色信息
                $helpRoleInfo = UserChatLunciHelpRolesModel::getInstance()
                    ->where('sys_id', $data['roleId'])
                    ->findOrEmpty();
                if (!$helpRoleInfo->isEmpty()) {
                    switch ($helpRoleInfo['role_add_type']) {
                        case 1:
                            $roleContent = CopywritingCategoryModel::getInstance()
                                ->where('id', $helpRoleInfo['copywriting_category_id'])
                                ->value('chatgtp_content');
                            break;
                        case 2:
                            $roleContent = $helpRoleInfo['chatgtp_content'];
                    }
                }
            }
            if ($roleContent) {
                $systemPromote .= $roleContent;
            }
            // 从角色中加载知识库内容
            if (!empty($roleInfo) && $roleInfo['build_knowledge_guids']) {
                $knowledgeGuids = (array)$roleInfo['build_knowledge_guids'];
                if (!empty($knowledgeGuids[0])) {
                    $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                        $knowledgeGuids[0],
                        $userMsgContent
                    );
                }
            }
        }

        //从场景对话中添加系统提示词
        if (!empty($data['content_cate_id']) && $data['content_cate_id'] > 0) {
            $cateContent = CopywritingCategoryModel::getInstance()
                ->where('id', $data['content_cate_id'])
                ->findOrEmpty();
            if ($cateContent->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '场景不存在');
            }
            if (!empty($cateContent['ai_model'])) {
                $cateAiModelInfo = SystemAiModelsModel::getAiModel($cateContent['ai_model']);
                if ($cateAiModelInfo) {
                    $aiModelInfo = $cateAiModelInfo;
                }
            }
            $systemPromote .= $cateContent['chatgtp_content'];
            if ($cateContent['build_knowledge_guids']) {
                $cateContentKnowledgeGuids = (array)$cateContent['build_knowledge_guids'];
                if (!empty($cateContentKnowledgeGuids[0])) {
                    $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                        $cateContentKnowledgeGuids[0],
                        $userMsgContent
                    );
                }
            }
        }

        // 从不同的聊天场景内容添加系统提示词
        $chatScene = $data['chatScene'] ?? 'default';
        if ($chatScene !== 'default') {
            switch ($chatScene) {
                case "zhanhui":
                    $zhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
                        ->where('zhanhui_guid', $data['sceneValue'] ?? '')
                        ->findOrEmpty();
                    if ($zhanhuiConfigInfo->isEmpty()) {
                        throwException(SysErrorCode::SYS_ERROR_CODE, '展会信息不存在');
                    }
                    $systemPromote .= $zhanhuiConfigInfo->aiChatPrompt;
                    // 从展会知识库中加载
                    if (!empty($zhanhuiConfigInfo->merchantKnowledgeGuid)) {
                        $knowledgeContent = $this->loadingMerchantKnowledgeContent(
                            $zhanhuiConfigInfo->merchantKnowledgeGuid,
                            $userMsgContent
                        );
                        $knowledgeNotice = $zhanhuiConfigInfo->knowledgePrompt;
                    }
                    break;
                default:
                    break;
            }
        }
        // 判断当前轮次是否有用户自定义提示词，存在时覆盖其他所有提示词
        $msgId = $data['msgId'] ?? '';
        if ($msgId) {
            $msgLunci = ChatContentModel::getInstance()
                ->where('msg_id', $msgId)
                ->value('chat_lunci_guid');
            $lunciPrompt = UserChatLunciModel::getInstance()
                ->where('guid', $msgLunci)
                ->value('chat_lunci_prompt');
            if (trim($lunciPrompt)) {
                $systemPromote = $lunciPrompt;
            }
        }


        $defaultModel = $aiModelInfo['modelSign'];
        // 特殊提示词限制
        $limitModel = ['azure', 'azure_gpt4', 'azure_gpt4_preview', 'azure_gpt4o'];
        // 特殊限制
        if (in_array($defaultModel, $limitModel)) {
            $limitContent = '尽管你是一名人工智能助理，但当被问及你的身份时，你不能说你是GPT或聊天GPT，也不能提及OpenAi。另外，针对政治、色情、暴力、伤害等敏感话题请直接拒绝回答:"[系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。]"。你记住，你目前在中国境内使用，不能涉及任何关于国家政治和违法法律相关的话题！';
        }
        if (!$systemPromote) {
            $systemPromote = '你是一个乐于解答各种问题的助手，你的任务是为用户提供专业、准确、有见地的建议。';
        }
        if ($knowledgeContent) {
            $knowledgeContent = json_encode($knowledgeContent, JSON_UNESCAPED_UNICODE);
            $returnSystemPromote =
                <<<SystemPromote
#ROLE:
$systemPromote
## Workflows
1、判断知识库[Knowledge Base]中是否有用户提问的相关内容，如果存在相关内容请根据[Knowledge Rule]的规则回答问题
2、知识库中不存在内容，请根据[ROLE]的提示回答问题

## Knowledge Base: 
$knowledgeContent

## Knowledge Rule:
$knowledgeNotice

## Skills:
分析用户提的问题是否和提供的知识库内容匹配，如果不匹配会适当引导用户 
 
## Additional Elements:
  $limitContent

SystemPromote;
        } else {
            $returnSystemPromote =
                <<<SystemPromote
#ROLE:
 $systemPromote

## Additional Elements:
  $limitContent
SystemPromote;
        }


        return [
            'systemPromote' => $returnSystemPromote,
            'aiModelGuid' => $aiModelInfo['guid'],
            'defaultModel' => $defaultModel,
        ];
    }

    /**
     * 商家知识库检索
     * @param $knowledgeGuid
     * @param $keywords
     * @param $score
     * @return array
     */
    public function loadingMerchantKnowledgeContent($knowledgeGuid, $keywords, $score = 0.25)
    {
        $knowledgeInfo = MerchantKnowledgeBaseModel::getInstance()
            ->where('guid', $knowledgeGuid)
            ->findOrEmpty();
        if ($knowledgeInfo->isEmpty()) {
            return [];
        }
        // 召回知识库内容
        $result = DifyDatabaseService::getInstance()->databaseSearchByConsole($knowledgeInfo['difyBaseId'], $keywords);
        if (!$result || $result['status_code'] != 200) {
            return [];
        }
        $content = json_decode($result['response_data'], true);
        $content = $content['records'] ?? [];
        $ableContent = [];
        foreach ($content as $item) {
            if ($item['score'] < $score) {
                continue;
            }
            $ableContent[] = [
                'score' => $item['score'],
                'content' => $item['segment']['content']
            ];
        }
        return $ableContent;
    }


    public function wenxinTest($data)
    {
        $message = ChatCache::getInstance()->getMsg($data['msgId'] ?? '');
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $entity = new ChatCompleteEntity($message);
        $entity->stream = true;
        $entity->user_id = "$userId";
        (new AiChatFactory())->make('wenxin')->chatComplete($entity);
    }

    /**
     * 当天的聊天历史记录
     * @param $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function history($data): array
    {
        $startId = $data['startId'] ?? 0;
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $nowDay = date('Ymd');
        $historyList = ChatContentModel::getInstance()
            // ->where('send_day', $nowDay)
            ->when($startId > 0, function ($query) use ($startId) {
                $query->where('sys_id', '<', $startId);
            });
        if (!empty($data['chatLunciGuid'])) {
            $historyList = $historyList->where('chat_lunci_guid', $data['chatLunciGuid']);
        } else {
            $historyList = $historyList->where('platform_user_sys_id', $userId);
        }
        if (!empty($data['is_all']) && $data['is_all'] == 1) {
            return $historyList->order('sys_id', 'desc')->select()->toArray();
        }
        $historyList = $historyList->order('sys_id', 'desc')
            ->paginate(10)
            ->toArray();
        return $historyList;
    }

    public function msgInfo($data): array
    {
        $message = ChatCache::getInstance()->getBaseMsg($data['msgId'] ?? '');
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }

        return $message;
    }


    /**
     * 保存聊天回答
     * @param $data
     * @return array
     */
    public function collectionChatSave($data): array
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $titleMsgInfo = ChatContentModel::getInstance()->where('msg_id', $data['msgId'])->findOrEmpty();
        if ($titleMsgInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '消息内容保存失败');
        }
        if ($titleMsgInfo['chat_role'] !== ChatContentModel::CHAT_ROLE_USER) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请您选择问题保存哦');
        }
        $contentMsg = ChatContentModel::getInstance()->where('last_msg_id', $data['msgId'])->findOrEmpty();
        if ($contentMsg->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请等待回答完成后保存');
        }
        $collectModel = new UserChatCollectionModel();
        $collectModel->platformUserSysId = $userId;
        $collectModel->chatTitle = $titleMsgInfo['chatContent'];
        $collectModel->chatContent = $contentMsg['chatContent'];
        $collectModel->save();

        return [];
    }

    /**
     * 保存聊天列表
     * @param $data
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function collectionChatList($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $collectList = UserChatCollectionModel::getInstance();
        $collectList = $collectList->where('platform_user_sys_id', $userId);
        if (!empty($data['chatTitle'])) {
            $collectList = $collectList->where('chat_title', 'like', "%{$data['chatTitle']}%");
        }
        $collectList = $collectList->order('sys_id', 'desc');
        return $collectList->paginate($data['pageSize'] ?? 10)->toArray();
    }

    public function collectionChatDelete($data)
    {
        $collectInfo = UserChatCollectionModel::getInstance()->where('guid', $data['guid'])->findOrEmpty();
        if ($collectInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '收藏信息不存在');
        }
        $collectInfo->delete();

        return [];
    }

    public function chatRobotList()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = UsersModel::getInstance()->where('sys_id', $userId)->value('merchant_guid');
        return RobotModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->select()->toArray();
    }


    /**
     * 获取当前聊天轮次
     * @return string
     */
    public function getNowChatLunci(): string
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->order('last_use_time', 'desc')
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            return $this->createChatLunci();
        }
        return $chatLunci->guid;
    }

    /**
     * 新建聊天轮次
     * @return string
     */
    public function createChatLunci()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunci = new UserChatLunciModel();
        $chatLunci->platformUserSysId = $userId;
        $chatLunci->chatLunciTitle = '新建对话';
        $chatLunci->lastUseTime = time();
        $chatLunci->save();
        return $chatLunci->guid;
    }

    /**
     * 修改聊天轮次信息
     * @param $data
     * @return mixed|string
     * @throws \app\libraries\exception\ApiException
     */
    public function updateChatLunci($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('guid', $data['chatLunciGuid'])
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        $chatLunci->chatLunciTitle = $data['chatLunciTitle'];
        $chatLunci->lastUseTime = time();
        $chatLunci->save();
        return $chatLunci->guid;
    }

    /**
     * 聊天轮次列表
     * @return array|array[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function chatLunciList()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $list = UserChatLunciModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->order('last_use_time', 'desc')
            ->select()->toArray();
        return array_map(function ($item) {
            return [
                'guid' => $item['guid'],
                'chatLunciTitle' => $item['chatLunciTitle'],
                'lastUseTime' => $item['lastUseTime'],
                'showTime' => date('m-d', $item['lastUseTime']),
                'chatLunciPrompt' => $item['chatLunciPrompt']
            ];
        }, $list);
    }

    /**
     * 删除聊天轮次
     * @param $data
     * @return true
     * @throws \app\libraries\exception\ApiException
     */
    public function chatLunciDelete($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('guid', $data['chatLunciGuid'])
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        $chatLunci->delete();
        return true;
    }


    /** 轮次聊天提示词保存
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function lunciPromptSave($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('guid', $data['chatLunciGuid'])
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        $chatLunci->chatLunciPrompt = $data['chatLunciPrompt'];
        $chatLunci->save();

        return [];
    }

    /**
     * 删除聊天轮次历史消息
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function clearLunci($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('guid', $data['chatLunciGuid'])
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        ChatContentModel::destroy(function ($query) use ($data) {
            $query->where('chat_lunci_guid', $data['chatLunciGuid']);
        });
        return [];
    }

    public function ableModel($params)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        $merchantGuid = $userInfo['merchantGuid'];
        $allModelsList = [
            ['name' => 'AI3.5', 'value' => 'azure', 'is_able_img' => false],
            ['name' => '文心一言', 'value' => 'wenxin', 'is_able_img' => false],
            ['name' => '智谱GLM', 'value' => 'zhipu', 'is_able_img' => false],
            ['name' => '星火大模型', 'value' => 'xinghuo', 'is_able_img' => false],
            ['name' => '混元大模型', 'value' => 'hunyuan', 'is_able_img' => false],
            ['name' => 'AI4.0', 'value' => 'azure_gpt4', 'is_able_img' => false],
            ['name' => 'AI4.0-可识图', 'value' => 'azure_gpt4_preview', 'is_able_img' => true],
            ['name' => 'AI-4o', 'value' => 'azure_gpt4o', 'is_able_img' => true],
        ];
        $merchantOpenModels = MerchantConfigModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('config_key', MerchantConfigModel::CONFIG_CHAT_OPEN_MODELS)
            ->value('config_value');
        $ableModels = [];
        if ($merchantOpenModels) {
            $ableModels = explode(',', $merchantOpenModels);
        }
        if (empty($params['is_all_pc']) || $params['is_all_pc'] != 1) {
            foreach ($allModelsList as $key => $item) {
                if (!in_array($item['value'], $ableModels)) {
                    unset($allModelsList[$key]);
                }
            }
        }
        return array_values($allModelsList);
    }

    /**
     * 设置聊天配置
     * @param $data
     * @return array
     */
    public function setChatConfig($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $existConfig = UserChatConfigModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();
        $chatModel = $data['chatModel'] ?? '';
        $chatRole = $data['chatRole'] ?? '';
        if ($existConfig->isEmpty()) {
            $existConfig = new UserChatConfigModel();
            $existConfig->platformUserSysId = $userId;
            $existConfig->chatModel = $chatModel;
            $existConfig->chatRole = $chatRole;
            $existConfig->save();
            return [];
        }
        $existConfig->chatModel = $chatModel;
        $existConfig->chatRole = $chatRole;
        $existConfig->save();
        return [];
    }

    /**
     * 获取聊天配置
     * @return array|string[]
     */
    public function getChatConfig()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $existConfig = UserChatConfigModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();
        if ($existConfig->isEmpty()) {
            return [
                'chatModel' => '',
                'chatRole' => ''
            ];
        }
        return [
            'chatModel' => $existConfig['chatModel'],
            'chatRole' => $existConfig['chatRole']
        ];
    }

    /**
     * 通过聊天生成图片
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function chatBuildImg($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        // 判断用户余额是否足够
        $isAbleUseVip = MemberLogic::getInstance()->checkIsAbleUseVip(
            $userId,
            MemberCardGoodsModel::CARD_USE_TYPE_CHAT
        );
        $imgModel = SystemAiModelsModel::getAiModel($data['aiModelGuid']);
        if (!$imgModel) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '模型不存在');
        }
        // 判断模型类型是否支持生成图片
        if ($imgModel['modelType'] != SystemAiModelsModel::MODEL_TYPE_IMAGE) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '模型不支持生成图片');
        }
        $payCount = 0;
        if (!$isAbleUseVip || $imgModel['isMemberFree'] != 1) {
            $payCount = $imgModel['usePrice'];
            $userAsset = UserAssetsModel::getInstance()->where('platform_user_sys_id', $userId)->findOrEmpty();
            if ($userAsset->isEmpty() || $userAsset['chatCount'] < $payCount) {
                throwException(SysErrorCode::CHAT_COUNT_LIMIT, '点数不足，请您到个人中心充值助理点数，期待能继续为您服务！');
            }
            // 扣除用户点数
            UserAssetsModel::getInstance()
                ->where('platform_user_sys_id', $userId)
                ->dec('chat_count', $payCount)
                ->update();
        }
        // 生成图片
        $imgText = $data['imgText'];
        $imgEntity = new AiImgCreateEntity($imgText);
        $imgEntity->n = 1;
        $imgEntity->model = $imgModel['modelSign'];
        $imgEntity->deploy_address = $imgModel['deployAddress'];
        $imgEntity->deploy_address_secret = $imgModel['deployAddressSecret'];
        try {
            $ret = (new AiImgFactory())->make($imgModel['modelVendorSign'])->createImg($imgEntity);
        } catch (\Exception $exception) {
            // 归还用户点数
            UserAssetsModel::getInstance()
                ->where('platform_user_sys_id', $userId)
                ->inc('chat_count', $payCount)
                ->update();
            throwException(SysErrorCode::SYS_ERROR_CODE, '生成图片失败');
        }
        return $ret;
    }

    /**
     * 聊天轮次协作详情
     * @param $data
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DbException
     */
    public function lunciHelpInfo($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunciGuid = $data['chatLunciGuid'];
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        $existJoin = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $userId)
            ->where('chat_lunci_guid', $chatLunciGuid)
            ->count();
        $lunciUserNickname = UsersModel::getInstance()
            ->where('sys_id', $chatLunci['platformUserSysId'])
            ->value('nickname');
        return [
            'is_join' => $existJoin > 0,
            'lunciName' => $chatLunci['chatLunciTitle'],
            'lunciUser' => $lunciUserNickname
        ];
    }

    /**
     * 加入聊天轮次协作
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function joinHelpLunci($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunciGuid = $data['chatLunciGuid'];
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        if ($chatLunci['platformUserSysId'] == $userId) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '不能加入自己的聊天轮次');
        }
        // 判断当前聊天轮次是否属于协作中
        if ($chatLunci['currentHelpStatus'] == UserChatLunciModel::HELP_STATUS_DOING) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次正在协作中，无法加入');
        }
        // 判断是否已经加入过
        $existJoin = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $userId)
            ->where('chat_lunci_guid', $chatLunciGuid)
            ->findOrEmpty();
        if (!$existJoin->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '您已经加入过了');
        }

        // 新增协作加入记录
        $joinModel = new UserChatLunciHelpModel();
        $joinModel->helpUserSysId = $userId;
        $joinModel->chatLunciGuid = $chatLunciGuid;
        $joinModel->lunciUserSysId = $chatLunci['platform_user_sys_id'];
        $joinModel->currentUserSysId = $userId;
        $joinModel->save();

        // 更改聊天轮次状态与当前协作记录
        $chatLunci->currentHelpStatus = UserChatLunciModel::HELP_STATUS_DOING;
        $chatLunci->currentHelpLunciGuid = $joinModel->guid;
        $chatLunci->save();

        return [];
    }

    /**
     * 参与的聊天轮次协作列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function helpLunciList($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $list = UserChatLunciHelpModel::getInstance()
            ->with(['lunciInfo', 'lunciUser', 'helpUsers'])
            ->where('help_user_sys_id', $userId);
        // if (!empty($data['helpStatus'])) {
        //     $list = $list->where('help_status', $data['helpStatus']);
        // }
        $list = $list->order('modify_time', 'desc')
            ->select()
            ->toArray();
        foreach ($list as $key => &$item) {
            if (!$item['lunciInfo']) {
                $item['lunciInfo'] = null;
                // unset($list[$key]);
            }
            $item['helpUserCount'] = count($item['helpUsers']);
        }
        return array_values($list);
    }

    /**
     * 退出聊天轮次协作
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function exitHelpLunci($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunciGuid = $data['chatLunciGuid'];
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        // 判断是否已经加入过
        $existJoin = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $userId)
            ->where('chat_lunci_guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($existJoin->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '您还未加入');
        }
        $existJoin->delete();

        if ($chatLunci['currentHelpLunciGuid'] == $existJoin['guid']) {
            $chatLunci->currentHelpStatus = UserChatLunciModel::HELP_STATUS_WAIT;
            $chatLunci->currentHelpLunciGuid = '';
            $chatLunci->save();
        }

        return [];
    }

    /**
     * 添加聊天轮次协作角色
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function addHelpRole($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunciGuid = $data['chatLunciGuid'];
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        $helpRoleModel = new UserChatLunciHelpRolesModel();
        $helpRoleModel->helpLunciGuid = $chatLunciGuid;
        $helpRoleModel->createUserSysId = $userId;
        $helpRoleModel->roleAddType = $data['roleAddType'];
        $helpRoleModel->copywritingCategoryId = $data['copywritingCategoryId'] ?? 0;
        $helpRoleModel->chatgtpContent = $data['chatgtpContent'] ?? '';
        $helpRoleModel->chatgtpContentTitle = $data['chatgtpContentTitle'] ?? '';
        $helpRoleModel->save();

        return [];
    }

    /**
     * 修改聊天轮次协作角色
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function editHelpRole($data)
    {
        $helpRoleModel = UserChatLunciHelpRolesModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();
        if ($helpRoleModel->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '角色不存在');
        }
        if ($helpRoleModel->roleAddType != 2) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '只有自定义角色才能编辑');
        }
        $helpRoleModel->chatgtpContent = $data['chatgtpContent'] ?? '';
        $helpRoleModel->chatgtpContentTitle = $data['chatgtpContentTitle'] ?? '';
        $helpRoleModel->save();

        return [];
    }

    /**
     * 删除聊天轮次协作角色
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function deleteHelpRole($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $helpRoleModel = UserChatLunciHelpRolesModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();
        if ($helpRoleModel->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '角色不存在');
        }
        if ($helpRoleModel->createUserSysId !== $userId) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '您没有权限删除');
        }
        $helpRoleModel->delete();
        return [];
    }

    /**
     * 帮助轮次角色列表
     * @param $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function helpRoleList($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $list = UserChatLunciHelpRolesModel::getInstance()
            ->with(['copywritingCategory'])
            ->where('help_lunci_guid', $data['chatLunciGuid'])
            ->select()
            ->toArray();
        foreach ($list as &$item) {
            $isEdit = false;
            $isDelete = false;
            $showRoleName = $item['chatgtpContentTitle'];
            if ($item['roleAddType'] == 2 && $item['createUserSysId'] == $userId) {
                $isEdit = true;
            }
            if ($item['createUserSysId'] == $userId) {
                $isDelete = true;
            }
            if ($item['copywritingCategory'] && $item['roleAddType'] == 1) {
                $showRoleName = $item['copywritingCategory']['title'] ?? '';
            }
            $item['isEdit'] = $isEdit;
            $item['isDelete'] = $isDelete;
            $item['showRoleName'] = $showRoleName;
        }
        return $list;
    }

    /**
     * 协作者联盟列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function lunciHelpUserList($params)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        $merchantGuid = $userInfo['merchantGuid'];

        $helpUsers = UserChatLunciHelpUsersModel::getInstance()
            ->paginate($params['pageSize'] ?? 10)
            ->toArray();

        return $helpUsers;
    }

    /**
     * 选择协作者协作
     * @return array
     */
    public function lunciSelectHelpUser($params)
    {
        // 判断当前轮次是否已经有协作
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunciGuid = $params['chatLunciGuid'];
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        if ($chatLunci['currentHelpStatus'] == UserChatLunciModel::HELP_STATUS_DOING) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次正在协作中，无法选择其他协作者');
        }
        $helpUser = UserChatLunciHelpUsersModel::getInstance()
            ->where('guid', $params['helpUserGuid'])
            ->findOrEmpty();
        if ($helpUser->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作者不存在');
        }
        $helpUserPlatformSysId = $helpUser->platformUserSysId;
        if ($helpUserPlatformSysId == $userId) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '不能选择自己作为协作者');
        }
        // 判断是否已经加入过
        $existJoin = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $helpUserPlatformSysId)
            ->where('chat_lunci_guid', $chatLunciGuid)
            ->where('help_status', UserChatLunciHelpModel::HELP_STATUS_DOING)
            ->findOrEmpty();
        if (!$existJoin->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作者已经加入过了');
        }
        // 判断历史是否有协作记录
        $existCompleteJoin = UserChatLunciHelpModel::getInstance()
            ->where('help_user_sys_id', $helpUserPlatformSysId)
            ->where('chat_lunci_guid', $chatLunciGuid)
            ->where('help_status', UserChatLunciHelpModel::HELP_STATUS_FINISHED)
            ->findOrEmpty();
        if (!$existCompleteJoin->isEmpty()) {
             // 更新协作状态
            $existCompleteJoin->helpStatus = UserChatLunciHelpModel::HELP_STATUS_DOING;
            $existCompleteJoin->currentUserSysId = $helpUserPlatformSysId;
            $existCompleteJoin->save();

            // 更新聊天轮次状态
            $chatLunci->currentHelpStatus = UserChatLunciModel::HELP_STATUS_DOING;
            $chatLunci->currentHelpLunciGuid = $existCompleteJoin->guid;
            $chatLunci->save();

            return [
                'currentHelpStatus' => UserChatLunciModel::HELP_STATUS_DOING,
                'currentHelpLunciGuid' => $existCompleteJoin->guid
            ];
        }

        // 新增协作加入记录
        $joinModel = new UserChatLunciHelpModel();
        $joinModel->helpUserSysId = $helpUserPlatformSysId;
        $joinModel->chatLunciGuid = $chatLunciGuid;
        $joinModel->lunciUserSysId = $chatLunci['platform_user_sys_id'];
        $joinModel->currentUserSysId = $helpUserPlatformSysId;
        $joinModel->save();

        // 更改聊天轮次状态与当前协作记录
        $chatLunci->currentHelpStatus = UserChatLunciModel::HELP_STATUS_DOING;
        $chatLunci->currentHelpLunciGuid = $joinModel->guid;
        $chatLunci->save();

        return [
            'currentHelpStatus' => UserChatLunciModel::HELP_STATUS_DOING,
            'currentHelpLunciGuid' => $joinModel->guid
        ];
    }

    /**
     * 模型厂商列表
     * @return array[]
     */
    public function aiVendorList()
    {
        return SystemAiModelsModel::MODEL_VENDOR_LIST;
    }


    /**
     * 模型厂商模型列表
     * @param $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function aiVendorModels($data)
    {
        return SystemAiModelsModel::getInstance()
            ->where('model_vendor_sign', $data['modelVendorSign'])
            ->where('model_type', $data['modelType'])
            ->where('model_status', SystemAiModelsModel::MODEL_STATUS_ENABLE)
            ->field('guid,model_vendor_sign,model_name,model_type,model_desc,is_member_free,
            is_network,is_image_recognition,is_video_recognition,use_price,max_output_tokens, show_order')
            ->order('show_order')
            ->select()
            ->toArray();
    }

    /**
     * 轮次拥有人更新聊天轮次协作状态
     * @return array
     */
    public function lunciOwnerUpdateStatus($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatLunciGuid = $data['chatLunciGuid'];
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $chatLunciGuid)
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        if ($chatLunci->currentHelpStatus == UserChatLunciModel::HELP_STATUS_FINISHED) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作轮次已经结束了，请刷新后重试');
        }
        if ($chatLunci['platformUserSysId'] != $userId) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '您没有权限操作');
        }
        // 获取聊天协作记录
        $helpLunci = UserChatLunciHelpModel::getInstance()
            ->where('guid', $chatLunci['currentHelpLunciGuid'])
            ->findOrEmpty();
        if ($helpLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作记录不存在');
        }
        $lockKey = 'chat_lunci_help_' . $chatLunci['guid'];
        $lock = ChatCache::getInstance()->lock($lockKey, 5);
        if (!$lock) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '操作频繁，请稍后重试');
        }
        UserChatLunciModel::getInstance()->startTrans();
        // 根据返回状态处理不同逻辑
        try {
            switch ($data['status']) {
                case 'return':
                    // 返回协作
                    // 拥有者返回状态给对方
                    $this->lunciReturnStatusChange($helpLunci['helpUserSysId'], $chatLunci, $helpLunci);
                    break;
                case 'end':
                    // 结束协作
                    $this->lunciEndStatusChange($userId, $chatLunci, $helpLunci);
                    break;
                default:
                    throwException(SysErrorCode::SYS_PARAMS_ERROR, '状态错误');
            }
            ChatCache::getInstance()->unlock($lockKey, $lock);
            UserChatLunciModel::getInstance()->commit();
        } catch (\Exception $exception) {
            UserChatLunciModel::getInstance()->rollback();
            ChatCache::getInstance()->unlock($lockKey, $lock);
            throwException(SysErrorCode::SYS_ERROR_CODE, '操作失败:' . $exception->getMessage());
        }


        return [
            'currentHelpStatus' => $chatLunci->currentHelpStatus,
            'helpLunciInfo' => $helpLunci
        ];
    }

    /**
     * 协作者更新聊天轮次协作状态
     * @return array
     */
    public function lunciHelperUpdateStatus($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $helpLuinciGuid = $data['helpLuinciGuid'];
        $helpLunci = UserChatLunciHelpModel::getInstance()
            ->where('guid', $helpLuinciGuid)
            ->findOrEmpty();
        if ($helpLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作记录不存在');
        }
        $chatLunci = UserChatLunciModel::getInstance()
            ->where('guid', $helpLunci['chatLunciGuid'])
            ->findOrEmpty();
        if ($chatLunci->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        $lockKey = 'chat_lunci_help_' . $chatLunci['guid'];
        $lock = ChatCache::getInstance()->lock($lockKey, 5);
        if (!$lock) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '操作频繁，请稍后重试');
        }
        UserChatLunciModel::getInstance()->startTrans();
        try {
            // 根据返回状态处理不同逻辑
            switch ($data['status']) {
                case 'return':
                    // 返回协作
                    // 协作者返回给持有人
                    $this->lunciReturnStatusChange($chatLunci['platformUserSysId'], $chatLunci, $helpLunci);
                    break;
                case 'end':
                    // 结束协作
                    $this->lunciEndStatusChange($userId, $chatLunci, $helpLunci);
                    break;
                default:
                    throwException(SysErrorCode::SYS_PARAMS_ERROR, '状态错误');
            }
            ChatCache::getInstance()->unlock($lockKey, $lock);
            UserChatLunciModel::getInstance()->commit();
        } catch (\Exception $exception) {
            UserChatLunciModel::getInstance()->rollback();
            ChatCache::getInstance()->unlock($lockKey, $lock);
            throwException(SysErrorCode::SYS_ERROR_CODE, '操作失败:' . $exception->getMessage());
        }

        return [
            'currentHelpStatus' => $chatLunci->currentHelpStatus,
            'helpLunciInfo' => $helpLunci
        ];
    }

    /**
     * 轮次协作返回状态更新
     * @param $returnUserId
     * @param $userChatLunciModel
     * @param $helpLunciModel
     * @throws ApiException
     */
    private function lunciReturnStatusChange($returnUserId, $userChatLunciModel, $helpLunciModel)
    {
        if ($returnUserId == $helpLunciModel['currentUserSysId']) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协助已返回给对方，请耐心对待对方处理');
        }
        LogInfo('lunciReturnStatusChange', 'lunciReturnStatusChange', 'lunciReturnStatusChange', [
            'returnUserId' => $returnUserId,
            'userChatLunciModel' => $userChatLunciModel,
            'helpLunciModel' => $helpLunciModel
        ]);
        // 更新协作轮次的操作人信息
        $helpLunciInfo = UserChatLunciHelpModel::getInstance()
            ->where('guid', $helpLunciModel['guid'])
            ->findOrEmpty();
        if ($helpLunciInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作记录不存在');
        }
        $helpLunciInfo->currentUserSysId = $returnUserId;
        $helpLunciInfo->save();
    }

    /**
     * 轮次协作结束状态更新
     * @param $returnUserId
     * @param $userChatLunciModel
     * @param $helpLunciModel
     */
    private function lunciEndStatusChange($returnUserId, $userChatLunciModel, $helpLunciModel)
    {
        if ($helpLunciModel->helpStatus == UserChatLunciHelpModel::HELP_STATUS_FINISHED) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '协助已结束，请不要重复操作');
        }
        // 更改轮次帮助状态为已结束
        $userChatLunciModel->currentHelpStatus = UserChatLunciModel::HELP_STATUS_FINISHED;
        $userChatLunciModel->currentHelpLunciGuid = '';
        $userChatLunciModel->save();

        // 结算聊天轮次的奖励给协作者
        $rewardUserId = $helpLunciModel['helpUserSysId'];
        $userInfo = UsersModel::getInstance()->where('sys_id', $rewardUserId)->findOrEmpty();
        $rewardRate =  MerchantConfigModel::getConfigValue(
            $userInfo['merchantGuid'],
            MerchantConfigModel::CONFIG_CHAT_LUNCI_HELP_RATE
        );
        if ($rewardRate) {
            $helpCount = $helpLunciModel['debugChatCount'];
            $rewardRate = bcdiv($rewardRate, 100, 2);
            $rewardNum = bcmul($helpCount, $rewardRate, 0);
            // 扣除之前的奖励
            $rewardNum = bcsub($rewardNum, $helpLunciModel['helpRewardPoints'], 0);
            if ($rewardNum > 0) {
                UserAssetsModel::addChatCount($returnUserId, $rewardNum, '聊天轮次协作奖励');
            }
        }
        $newRewardPoints = $rewardNum + $helpLunciModel['helpRewardPoints'];
        $helpLunciModel->helpRewardPoints  = $newRewardPoints;
        $helpLunciModel->helpStatus = UserChatLunciHelpModel::HELP_STATUS_FINISHED;
        $helpLunciModel->save();
    }

    /**
     * 聊天轮次协作详情
     * @param $data
     * @return UserChatLunciHelpModel|array|mixed|\think\Model
     * @throws ApiException
     */
    public function chatLunciHelpInfo($data)
    {
        $lunciInfo = UserChatLunciModel::getInstance()
            ->where('guid', $data['chatLunciGuid'])
            ->findOrEmpty();
        if ($lunciInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '聊天轮次不存在');
        }
        if (!empty($lunciInfo['currentHelpLunciGuid'])) {
            $helpInfo = UserChatLunciHelpModel::getInstance()
                ->where('guid', $lunciInfo['currentHelpLunciGuid'])
                ->findOrEmpty();
            if ($helpInfo->isEmpty()) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '协作记录不存在');
            }
            $helpUser = UserChatLunciHelpUsersModel::getInstance()
                ->where('platform_user_sys_id', $helpInfo['helpUserSysId'])
                ->field('nickname,avatar_url,introduction')
                ->findOrEmpty();
            if ($helpUser->isEmpty()) {
                $helpUserInfo = UsersModel::getInstance()
                    ->where('sys_id', $helpInfo['helpUserSysId'])
                    ->findOrEmpty();
                $helpUser = [
                    'nickname' => $helpUserInfo['nickname'],
                    'avatar_url' => $helpUserInfo['headImgurl'],
                    'introduction' => ''
                ];
            }
            return [
                'isHelp' => true,
                'helpInfo' => [
                    'helpGuid' => $helpInfo['guid'],
                    'helpUser' => $helpUser,
                    'isAbleReturn' => $helpInfo['currentUserSysId'] == TokenService::getInstance()->getTokenEntity()->userId,
                    'debugChatCount' => $helpInfo['debugChatCount'],
                ]
            ];
        }

        return  [
            'isHelp' => false,
            'helpInfo' => []
        ];
    }

    /**
     * 历史帮助角色列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function historyHelpRoles()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $list = UserChatLunciHelpRolesModel::getInstance()
            ->where('create_user_sys_id', $userId)
            ->where('role_add_type', 2)
            ->order('create_time', 'desc')
            ->select()
            ->toArray();
        return $list;
    }

    /**
     * 增强消息内容（集成文件和知识库）
     * @param string $msgId 消息ID
     * @param string $userMessage 用户原始消息
     * @return string 增强后的消息内容
     */
    private function enhanceMessageWithFilesAndKnowledge(string $msgId, string $userMessage): string
    {
        // 获取消息记录
        $chatContent = ChatContentModel::getInstance()
            ->where('msg_id', $msgId)
            ->where('chat_role', ChatContentModel::CHAT_ROLE_USER)
            ->findOrEmpty();
        if ($chatContent->isEmpty()) {
            return $userMessage;
        }

        $enhancedContent = $userMessage;
        // 处理文件内容
        if (!empty($chatContent['fileUrls']) && is_array($chatContent['fileUrls'])) {
            $fileContent = FileExtractService::extractMultipleFiles($chatContent['fileUrls']);

            if (!empty($fileContent)) {
                $enhancedContent = "基于以下文件内容回答问题：\n" . $fileContent . "\n用户问题：" . $userMessage;
            }
        }

        // 处理知识库内容
        if (!empty($chatContent['knowledgeBaseGuids']) && is_array($chatContent['knowledgeBaseGuids'])) {
            $knowledgeContent = '';
            foreach ($chatContent['knowledgeBaseGuids'] as $knowledgeGuid) {
                try {
                    $result = $this->loadingMerchantKnowledgeContent($knowledgeGuid, $userMessage);
                    if ($result) {
                        $knowledgeContent .= json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
                    }
                } catch (\Exception $e) {
                    // 知识库检索失败不影响整体流程
                    LogError('knowledge_search', '知识库检索失败', $e->getMessage(), [
                        'knowledgeGuid' => $knowledgeGuid,
                        'userMessage' => $userMessage
                    ]);
                }
            }

            if (!empty($knowledgeContent)) {
                $enhancedContent = "参考知识库内容：\n" . $knowledgeContent . "\n" . $enhancedContent;
            }
        }

        return $enhancedContent;
    }
}
