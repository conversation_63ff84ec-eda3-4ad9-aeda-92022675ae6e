<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/5/4   13:45
 */

namespace app\square\request\admin;

use app\Request;

class ChatGoodsRequest extends Request
{
    protected array $msgs = [];
    protected function getRule(): array
    {
        return  [
            'orders' => [
            ],
            'queryPay' => [
                'orderNo' => ['require']
            ],
            'systemAiModelCreate' => [
                'modelVendorSign' => ['require'],
                'modelName' => ['require'],
                'modelSign' => ['require'],
                'modelType' => ['require'],
                'modelDesc' => ['require'],
                'modelStatus' => ['require'],
                'isMemberFree' => ['require'],
                'isNetwork' => ['require'],
                'isImageRecognition' => ['require'],
                'isVideoRecognition' => ['require'],
                'usePrice' => ['require'],
                'showOrder' => ['require'],
                'maxOutputTokens' => ['require'],
                'maxContextWindow' => ['require'],
            ],
            'systemAiModelUpdate' => [
                'guid' => ['require'],
                'modelVendorSign' => ['require'],
                'modelName' => ['require'],
                'modelSign' => ['require'],
                'modelType' => ['require'],
                'modelDesc' => ['require'],
                'modelStatus' => ['require'],
                'isMemberFree' => ['require'],
                'isNetwork' => ['require'],
                'isImageRecognition' => ['require'],
                'isVideoRecognition' => ['require'],
                'usePrice' => ['require'],
            ],

            // 商户AI智能体
            'merchantAiAgentCreate' => [
                'agentVendorSign' => ['require'],
                'agentName' => ['require'],
                'agentSecretToken' => ['require'],
                'agentType' => ['require'],
                'agentDesc' => ['require'],
                'agentStatus' => ['require'],
                'isMemberFree' => ['require'],
                'usePrice' => ['require'],
                'showOrder' => ['require'],
            ],
            'merchantAiAgentUpdate' => [
                'guid' => ['require'],
                'agentVendorSign' => ['require'],
                'agentName' => ['require'],
                'agentSecretToken' => ['require'],
                'agentType' => ['require'],
                'agentDesc' => ['require'],
                'agentStatus' => ['require'],
                'isMemberFree' => ['require'],
                'usePrice' => ['require'],
            ],
        ];
    }
}
