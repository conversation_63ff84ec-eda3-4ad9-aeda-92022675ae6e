<?php

declare(strict_types=1);

namespace app\square\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $chatRole 聊天角色：user-用户；assistant-机器人
 * @property string $contentType 内容类型
 * @property string $chatContent 对话内容
 * @property string $msgId 对话msgId
 * @property string $lastMsgId 上次兑换msgId
 * @property int $sendDay 发送日期，格式20230326
 * @property int $platformUserSysId 用户id
 * @property string $chatLunciGuid 聊天轮次guid
 * @property int $separationGuid 分身guid
 * @property array $imgUrls 上传的图片列表
 * @property string $videoUrls 上传的视频列表
 * @property string $fileUrl 上传的文件URL
 * @property string $knowledgeBaseGuid 选择的知识库GUID
 * @property string $chatScene 聊天场景:defalut-默认场景；zhanhui-展会场景
 * @property string $sceneValue 聊天场景值
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @property int $helpUserSysId
 * @mixin BaseModel
 */
class ChatContentModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'chat_content';

    protected static bool $isGuid = true;

    public const CHAT_ROLE_USER = 'user';  //用户角色
    public const CHAT_ROLE_ASSISTANT = 'assistant'; //机器人角色

    public const CHAT_ROLE_LIST = [
        self::CHAT_ROLE_USER,
        self::CHAT_ROLE_ASSISTANT
    ];

    protected $json = [
        'img_urls',
        'video_urls'
    ];

    // 聊天内容类型
    public const CHAT_CONTENT_TYPE_TEXT = 'text'; // 文字
    public const CHAT_CONTENT_TYPE_IMAGE = 'image'; // 图片
    public const CHAT_CONTENT_TYPE_VOICE = 'voice'; // 语音
    public const CHAT_CONTENT_TYPE_VIDEO = 'video'; // 视频
    public const CHAT_CONTENT_TYPE_FILE = 'file'; // 文件
    public const CHAT_CONTENT_TYPE_IMG_TEXT = 'richText'; //富文本

    /**
     *  支持的图片识别模型
     * @var string
     */
    public const ABLE_IMG_MODELS = [
       'azure_gpt4_preview',
        'azure_gpt4o',
    ];
}
