<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/4/6   23:25
 */

namespace app\index\logic;

use app\libraries\exception\ApiException;
use app\libraries\response\Text;
use app\libraries\utils\CosService;
use app\libraries\utils\Util;
use app\square\logic\api\ChatGoodsLogic;
use app\user\cache\UserCache;
use app\user\logic\api\ChannelLogic;
use app\user\models\UserImageOrderModel;
use app\user\models\UserWorksRecordModel;
use app\zhanhui\logic\api\OrderLogic;
use app\useragent\logic\api\AiAgentSquareLogic;
use app\useragent\models\UserAiAgentPurchaseModel;
use Monolog\Handler\IFTTTHandler;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Response;
use app\user\logic\api\MemberLogic;

class CallbackLogic
{
    /**
     * 数字人认证优化
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws ApiException
     */
    public function channelNotify($data)
    {

        $xml = simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        //执行订单查询操作
        $queryLogic = new ChannelLogic();
        $ret = $queryLogic->queryPayChannel(['orderNo' => $xml->out_trade_no]);
        if ($ret['isPay']) {
            return Response::create(
                json_encode(['code' => 'SUCCESS', 'message' => '成功']),
                Text::class,
                200
            );
        }
        return Response::create(
            json_encode(['code' => 'fail', 'message' => '处理失败请重试']),
            Text::class,
            200
        );
    }

    public function chatGoodsNotify($data)
    {
        $xml = simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        LogInfo('payNotify', '聊天点数回调', '进入回调', ['ret' => $xml]);
        //执行订单查询操作
        $queryLogic = new ChatGoodsLogic();
        $ret = $queryLogic->buyQuery(['orderNo' => $xml->out_trade_no]);
        if ($ret['isPay']) {
            return Response::create(
                json_encode(['code' => 'SUCCESS', 'message' => '成功']),
                Text::class,
                200
            );
        } else {
            LogError('payNotify', '聊天点数回调', '回调失败', ['ret' => $ret]);
        }
        return Response::create(
            json_encode(['code' => 'fail', 'message' => '处理失败请重试']),
            Text::class,
            200
        );
    }

    /**
     * 支付宝回调异步处理
     * @param $data
     * @return Response
     * @throws ApiException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function chatGoodsAlipayNotify($data)
    {
        LogInfo('payNotify', '聊天点数回调', '进入回调', ['ret' => $data]);
        //执行订单查询操作
        $queryLogic = new ChatGoodsLogic();
        $ret = $queryLogic->buyQuery(['orderNo' => $data['out_trade_no']]);
        LogInfo('payNotify', '聊天点数回调', '查询结果', ['ret' => $ret]);
        if ($ret['isPay']) {
            return Text::create('success');
        } else {
            LogError('payNotify', '聊天点数回调', '回调失败', ['ret' => $ret]);
        }
        return Text::create('fail');
    }

    public function alipayChannelNotify($data)
    {
        LogInfo('payNotify', '数字人认证点数回调', '进入回调', ['ret' => $data]);
        //执行订单查询操作
        $queryLogic = new ChannelLogic();
        $ret = $queryLogic->queryPayChannel(['orderNo' => $data['out_trade_no']]);
        if ($ret['isPay']) {
            return Text::create('success');
        }
        LogInfo('payNotify', '聊天点数回调', '查询结果', ['ret' => $ret]);
        return Text::create('fail');
    }

    /**
     * 会员卡购买回调
     * @param $data
     * @return Response
     * @throws ApiException
     * @throws DbException
     */
    public function memberNotify($data)
    {
        $queryLogic = new MemberLogic();
        $ret = $queryLogic->memberCardBuyQuery($data);
        if ($ret['isPay']) {
             return Text::create('success');
        }
        return Text::create('fail');
    }

    /**
     * 展会购买回调
     * @param $data
     * @return Response
     * @throws ApiException
     * @throws DbException
     */
    public function zhanhuiBuyNotify($data)
    {
        $queryLogic = new OrderLogic();
        $ret = $queryLogic->queryOrder($data);
        if ($ret['isPay']) {
            return Text::create('success');
        }
        return Text::create('fail');
    }

    /**
     * AI智能体购买支付回调
     * @param $data
     * @return Response
     */
    public function aiAgentPurchaseNotify($data)
    {
        try {
            LogInfo('aiAgentPurchaseNotify', 'AI智能体购买支付回调', '开始处理回调', ['orderNo' => $data['orderNo']]);

            // 直接处理支付回调，不需要用户登录验证
            $this->handleAiAgentPaymentCallback($data['orderNo']);

            LogInfo('aiAgentPurchaseNotify', 'AI智能体购买支付回调', '支付成功', [
                'orderNo' => $data['orderNo']
            ]);
            return Text::create('success');

        } catch (\Exception $e) {
            LogError('aiAgentPurchaseNotify', 'AI智能体购买支付回调', '处理异常', [
                'orderNo' => $data['orderNo'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Text::create('fail');
        }
    }

    /**
     * 处理AI智能体购买支付回调
     * @param string $orderNo
     * @throws \Exception
     */
    private function handleAiAgentPaymentCallback(string $orderNo): void
    {
        // 查询订单信息（不需要用户验证）
        $order = UserAiAgentPurchaseModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在：' . $orderNo);
        }

        // 如果已经是已支付状态，直接返回
        if ($order->orderStatus == UserAiAgentPurchaseModel::ORDER_STATUS_PAID) {
            LogInfo('aiAgentPurchaseCallback', '订单已支付', '订单状态已是已支付', [
                'orderNo' => $orderNo,
                'orderStatus' => $order->orderStatus
            ]);
            return;
        }

        // 查询微信支付状态
        $payService = new \app\libraries\service\wxpay\WxpayService($order->merchantGuid);
        $payResult = $payService->queryPayInfo($orderNo);

        if (!$payResult['isPay']) {
            throw new \Exception('微信支付状态查询失败或未支付：' . $orderNo);
        }

        // 处理支付成功逻辑
        $squareLogic = new AiAgentSquareLogic();
        $squareLogic->handlePaymentSuccessCallback($order);

        LogInfo('aiAgentPurchaseCallback', '支付回调处理完成', '订单支付成功处理完成', [
            'orderNo' => $orderNo,
            'platformUserSysId' => $order->platformUserSysId,
            'agentGuid' => $order->agentGuid,
            'payAmount' => $order->payAmount
        ]);
    }

    /**
     * AI智能体会员购买支付回调
     * @param $data
     * @return Response
     */
    public function aiAgentMembershipNotify($data)
    {
        try {
            LogInfo('aiAgentMembershipNotify', 'AI智能体会员购买支付回调', '开始处理回调', ['orderNo' => $data['orderNo']]);

            // 直接处理支付回调，不需要用户登录验证
            $this->handleAiAgentMembershipPaymentCallback($data['orderNo']);

            LogInfo('aiAgentMembershipNotify', 'AI智能体会员购买支付回调', '支付成功', [
                'orderNo' => $data['orderNo']
            ]);
            return Text::create('success');

        } catch (\Exception $e) {
            LogError('aiAgentMembershipNotify', 'AI智能体会员购买支付回调', '处理异常', [
                'orderNo' => $data['orderNo'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Text::create('fail');
        }
    }

    /**
     * 处理AI智能体会员购买支付回调
     * @param string $orderNo
     * @throws \Exception
     */
    private function handleAiAgentMembershipPaymentCallback(string $orderNo): void
    {
        // 查询订单信息（不需要用户验证）
        $order = \app\useragent\models\UserAiAgentMembershipOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('会员订单不存在：' . $orderNo);
        }

        // 如果已经是已支付状态，直接返回
        if ($order->orderStatus == \app\useragent\models\UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID) {
            LogInfo('aiAgentMembershipCallback', '订单已支付', '订单状态已是已支付', [
                'orderNo' => $orderNo,
                'orderStatus' => $order->orderStatus
            ]);
            return;
        }

        // 查询微信支付状态
        $payService = new \app\libraries\service\wxpay\WxpayService($order->merchantGuid);
        $payResult = $payService->queryPayInfo($orderNo);

        if (!$payResult['isPay']) {
            throw new \Exception('微信支付状态查询失败或未支付：' . $orderNo);
        }

        // 处理支付成功逻辑
        $membershipLogic = new \app\useragent\logic\api\AiAgentMembershipLogic();
        $membershipLogic->handlePaymentSuccessCallback($order);

        LogInfo('aiAgentMembershipCallback', '支付回调处理完成', '会员订单支付成功处理完成', [
            'orderNo' => $orderNo,
            'platformUserSysId' => $order->platformUserSysId,
            'packageGuid' => $order->packageGuid,
            'payAmount' => $order->payAmount
        ]);
    }

    /**
     * AI智能体创作者订阅支付回调
     * @param $data
     * @return Response
     */
    public function aiAgentCreatorNotify($data)
    {
        try {
            LogInfo('aiAgentCreatorNotify', 'AI智能体创作者订阅支付回调', '开始处理回调', ['orderNo' => $data['orderNo']]);

            // 直接处理支付回调，不需要用户登录验证
            $this->handleAiAgentCreatorPaymentCallback($data['orderNo']);

            LogInfo('aiAgentCreatorNotify', 'AI智能体创作者订阅支付回调', '支付成功', [
                'orderNo' => $data['orderNo']
            ]);
            return Text::create('success');

        } catch (\Exception $e) {
            LogError('aiAgentCreatorNotify', 'AI智能体创作者订阅支付回调', '处理异常', [
                'orderNo' => $data['orderNo'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Text::create('fail');
        }
    }

    /**
     * AI智能体分类订阅支付回调
     * @param $data
     * @return Response
     */
    public function aiAgentCategoryNotify($data)
    {
        try {
            LogInfo('aiAgentCategoryNotify', 'AI智能体分类订阅支付回调', '开始处理回调', ['orderNo' => $data['orderNo']]);

            // 直接处理支付回调，不需要用户登录验证
            $this->handleAiAgentCategoryPaymentCallback($data['orderNo']);

            LogInfo('aiAgentCategoryNotify', 'AI智能体分类订阅支付回调', '支付成功', [
                'orderNo' => $data['orderNo']
            ]);
            return Text::create('success');

        } catch (\Exception $e) {
            LogError('aiAgentCategoryNotify', 'AI智能体分类订阅支付回调', '处理异常', [
                'orderNo' => $data['orderNo'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Text::create('fail');
        }
    }

    /**
     * 处理AI智能体创作者订阅支付回调
     * @param string $orderNo
     * @throws \Exception
     */
    private function handleAiAgentCreatorPaymentCallback(string $orderNo): void
    {
        // 查询订单信息（不需要用户验证）
        $order = \app\useragent\models\UserAiAgentCreatorOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('创作者订阅订单不存在：' . $orderNo);
        }

        // 如果已经是已支付状态，直接返回
        if ($order->orderStatus == \app\useragent\models\UserAiAgentCreatorOrderModel::ORDER_STATUS_PAID) {
            LogInfo('aiAgentCreatorCallback', '订单已支付', '订单状态已是已支付', [
                'orderNo' => $orderNo,
                'orderStatus' => $order->orderStatus
            ]);
            return;
        }

        // 查询微信支付状态
        $payService = new \app\libraries\service\wxpay\WxpayService($order->merchantGuid);
        $payResult = $payService->queryPayInfo($orderNo);

        if (!$payResult['isPay']) {
            throw new \Exception('微信支付状态查询失败或未支付：' . $orderNo);
        }

        // 处理支付成功逻辑
        $creatorLogic = new \app\useragent\logic\api\AiAgentCreatorSubscriptionLogic();
        $creatorLogic->handlePaymentSuccessCallback($order);

        LogInfo('aiAgentCreatorCallback', '支付回调处理完成', '创作者订阅订单支付成功处理完成', [
            'orderNo' => $orderNo,
            'platformUserSysId' => $order->platformUserSysId,
            'creatorGuid' => $order->creatorGuid,
            'payAmount' => $order->payAmount
        ]);
    }

    /**
     * 处理AI智能体分类订阅支付回调
     * @param string $orderNo
     * @throws \Exception
     */
    private function handleAiAgentCategoryPaymentCallback(string $orderNo): void
    {
        // 查询订单信息（不需要用户验证）
        $order = \app\useragent\models\UserAiAgentCategoryOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('分类订阅订单不存在：' . $orderNo);
        }

        // 如果已经是已支付状态，直接返回
        if ($order->orderStatus == \app\useragent\models\UserAiAgentCategoryOrderModel::ORDER_STATUS_PAID) {
            LogInfo('aiAgentCategoryCallback', '订单已支付', '订单状态已是已支付', [
                'orderNo' => $orderNo,
                'orderStatus' => $order->orderStatus
            ]);
            return;
        }

        // 查询微信支付状态
        $payService = new \app\libraries\service\wxpay\WxpayService($order->merchantGuid);
        $payResult = $payService->queryPayInfo($orderNo);

        if (!$payResult['isPay']) {
            throw new \Exception('微信支付状态查询失败或未支付：' . $orderNo);
        }

        // 处理支付成功逻辑
        $categoryLogic = new \app\useragent\logic\api\AiAgentCategorySubscriptionLogic();
        $categoryLogic->handlePaymentSuccessCallback($order);

        LogInfo('aiAgentCategoryCallback', '支付回调处理完成', '分类订阅订单支付成功处理完成', [
            'orderNo' => $orderNo,
            'platformUserSysId' => $order->platformUserSysId,
            'categoryGuid' => $order->categoryGuid,
            'payAmount' => $order->payAmount
        ]);
    }

    public function mjImgNotify($data)
    {
        LogInfo('mjImgNotify', 'mj绘画回调', '接受通知参数', ['data' => $data]);
        $msgId = $data['id'] ?? null;
        $msgStatus = $data['status'] ?? null;
        if (!$msgId || !$msgStatus) {
            return Text::create('fail');
        }

        $imgOrder = UserImageOrderModel::getInstance()->where('msg_unique_id', $msgId)->findOrEmpty();
        if ($imgOrder->isEmpty()) {
            return Text::create('fail');
        }
        if ($imgOrder['order_status'] !== UserImageOrderModel::STATUS_DOING) {
            return Text::create('success');
        }
        if ($data['status'] !== 'SUCCESS') {
            // 判断进度是否大于80% 如果大于80%则休眠3秒后，主动查询一次
            $progress = $data['progress'] ?? "0%";
            $progress = intval(str_replace('%', '', $progress));
            if ($progress > 80) {
                sleep(3);
                $this->checkMidjourneyStatus($msgId);
            }
            return Text::create('doing');
        }
        // 这里开始操作可以加锁
        $msgLockKey = 'midjourney_msg_lock_' . $msgId;
        $lock = UserCache::getInstance()->lock($msgLockKey, 30);
        if (!$lock) {
            return Text::create('fail');
        }
        // 储存图片到远端
        $cosImgs =  CosService::upload($data['imageUrl'], 'image/png', 'image', '.png');
        $imgUrl = $cosImgs['data'] ?? $data['imageUrl'];
        $imgUrl = [$imgUrl];
        // 更新订单状态
        $imgOrder->orderStatus = UserImageOrderModel::STATUS_SUCCESS;
        $imgOrder->imageResult = $imgUrl;
        // 更新mj图像需要变换的参数
        if (!$imgOrder->imgChangeInfo) {
            $imgChangeInfo = [
                'use_U' => [],
                'use_V' => [],
                'able_U' => [
                    'components' => [
                        [
                            'label' => 'U1',
                            'custom_id' => 'U-1',
                        ],
                        [
                            'label' => 'U2',
                            'custom_id' => 'U-2',
                        ],
                        [
                            'label' => 'U3',
                            'custom_id' => 'U-3',
                        ],
                        [
                            'label' => 'U4',
                            'custom_id' => 'U-4',
                        ],
                    ]
                ],
                'able_V' => [
                    'components' => [
                        [
                            'label' => 'V1',
                            'custom_id' => 'V-1',
                        ],
                        [
                            'label' => 'V2',
                            'custom_id' => 'V-2',
                        ],
                        [
                            'label' => 'V3',
                            'custom_id' => 'V-3',
                        ],
                        [
                            'label' => 'V4',
                            'custom_id' => 'V-4',
                        ],
                    ]
                ]
            ];
            $imgOrder->imgChangeInfo = json_encode($imgChangeInfo, JSON_UNESCAPED_UNICODE);
        }
        $imgOrder->save();
        //修改工作状态为成功
        $recordInfo = UserWorksRecordModel::getInstance()
            ->where('work_type', UserWorksRecordModel::WORK_TYPE_IMAGE)
            ->where('order_no', $imgOrder['orderNo'])
            ->findOrEmpty();
        if (!$recordInfo->isEmpty()) {
            $recordInfo->workStatus = UserWorksRecordModel::STATUS_SUCCESS;
            $recordInfo->workResult = json_encode($imgUrl, JSON_UNESCAPED_UNICODE);
            $recordInfo->save();
        }
        UserCache::getInstance()->unlock($msgLockKey, $lock);
        return Text::create('success');
    }

    private function checkMidjourneyStatus($msgUniqueId)
    {
        try {
            $midjourneyUrl = config('chatgpt.midjourney_url') . "/mj/task/{$msgUniqueId}/fetch";
            $headers  = [
                'Content-Type: application/json',
            ];
            // 发起get请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_URL, $midjourneyUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 1200); // 设置超时限制防止死循环
            $result =  curl_exec($ch);

            $result = json_decode($result, true);
            LogInfo('midjourney_query', 'midjourney脚本查询', 'midjourney查询任务查询结果', ['result' => $result]);
            if ($result) {
                (new CallbackLogic())->mjImgNotify($result);
            }
        } catch (\Throwable $exception) {
            LogError(
                'userWork-ai-image',
                '查询midjourney状态',
                '查询midjourney状态-异常-' . $exception->getMessage(),
                [
                    'e' => Util::normalizeException($exception),
                ]
            );
        }
    }
}
