<?php

/**
 * @author: xuz<PERSON>gyang
 * @Time: 2024/9/3   23:44
 */

namespace app\zhanhui\request\api;

use app\Request;

class IndexRequest extends Request
{
    protected function getRule(): array
    {
        return  [
            'detail' => [
                'guid' => 'require'
            ],
            'faqList' => [
                'zhanhuiGuid' => 'require',
                'position' => 'require'
            ],
            'bannerLists' => [
                'zhanhuiGuid' => 'require',
                'bannerType' => 'require'
            ],
            'messageBoardCreate' => [
                'zhanhuiGuid' => 'require',
                'chatContent' => 'require'
            ],
            'messageBoardLists' => [
                'zhanhuiGuid' => 'require'
            ],
            'adminMessageBoardChatLists' => [
                'messageBoardGuid' => 'require'
            ],
            'adminMessageBoardChatReply' => [
                'messageBoardGuid' => 'require',
                'chatContent' => 'require'
            ],
        ];
    }

    protected array $msgs = [
        'guid' => '展会标识不能为空',
        'zhanhuiGuid' => '展会标识不能为空',
        'position' => 'FAQ位置不能为空'
    ];
}
