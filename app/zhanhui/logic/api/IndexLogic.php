<?php

/**
 * @author: xuz<PERSON>gyang
 * @Time: 2024/9/3   23:30
 */

namespace app\zhanhui\logic\api;

use app\constdir\SysErrorCode;
use app\constdir\SysTime;
use app\libraries\service\token\TokenService;
use app\merchant\models\MerchantConfigModel;
use app\user\models\UsersModel;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\zhanhui\models\MerchantZhanhuiBannerModel;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiFaqModel;
use app\zhanhui\models\MerchantZhanhuiMessageBoardChatModel;
use app\zhanhui\models\MerchantZhanhuiMessageBoardModel;
use app\zhanhui\models\MerchantZhanhuiModel;

class IndexLogic
{
    /**
     * 展会列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists()
    {
        // 获取用户所属的商户merchantid
        $merchantGuid = $this->getMerchantId();
        $query = MerchantZhanhuiModel::getInstance()
            ->where('status', MerchantZhanhuiModel::STATUS_NORMAL)
            ->where('merchant_guid', $merchantGuid);
        $query->order('show_order');
        $lists = $query->select();
        // 获取该用户所拥有的展会管理员列表
        $adminZhanhuiGuids = [];
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        if ($platformUserId) {
            $adminZhanhuiGuids = MerchantZhanhuiAdminUserModel::getInstance()
                ->where('platform_user_sys_id', $platformUserId)
                ->column('zhanhui_guid');
        }
        // 处理展示数据
        foreach ($lists as $key => $value) {
            // 如果截止时间跨年，显示年份
            //$endTime = $value['endTime'];
            //if (time() > $endTime && $value['endIsShow'] == 0) {
            //    unset($lists[$key]);
            //}
            //if (date("Y", $value['startTime']) != date("Y", $endTime)) {
            //     $lists[$key]['endTime'] = date("Y年m月d日", $endTime);
            //} else {
            //     $lists[$key]['endTime'] = date("m月d日", $endTime);
            //}
            $lists[$key]['startTime'] = date("Y/m/d", $value['startTime']);
            $lists[$key]['endTime'] = date("Y/m/d", $value['endTime']);
            $lists[$key]['createTime'] = date(SysTime::COMMAND_LOG_FORMAT, $value['createTime']);
            if (in_array($value['guid'], $adminZhanhuiGuids)) {
                $lists[$key]['isAdmin'] = 1;
            } else {
                $lists[$key]['isAdmin'] = 0;
            }
        }
        return $lists->toArray();
    }

    /**
     * 展会分页列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function pageLists()
    {
        // 获取用户所属的商户merchantid
        $merchantGuid = $this->getMerchantId();
        $query = MerchantZhanhuiModel::getInstance()
            ->where('status', MerchantZhanhuiModel::STATUS_NORMAL)
            ->where('merchant_guid', $merchantGuid);
        $query->order('show_order');
        $lists = $query
            ->paginate($data['pageSize'] ?? 10)
            ->toArray();
        // 获取该用户所拥有的展会管理员列表
        $adminZhanhuiGuids = [];
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        if ($platformUserId) {
            $adminZhanhuiGuids = MerchantZhanhuiAdminUserModel::getInstance()
                ->where('platform_user_sys_id', $platformUserId)
                ->column('zhanhui_guid');
        }
        // 处理展示数据
        foreach ($lists['data'] as $key => &$value) {
            $value['startTime'] = date("Y/m/d", $value['startTime']);
            $value['endTime'] = date("Y/m/d", $value['endTime']);
            $value['createTime'] = date(SysTime::COMMAND_LOG_FORMAT, $value['createTime']);
            if (in_array($value['guid'], $adminZhanhuiGuids)) {
                $value['isAdmin'] = 1;
            } else {
                $value['isAdmin'] = 0;
            }
        }
        return $lists;
    }

    /**
     * 展会详情
     * @return array
     */
    public function detail($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['guid'])
            ->field('guid, name, start_time, end_time, end_is_show, status')
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::ZHANHUI_NOT_EXIST);
        }
        $zhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $zhanhuiInfo->guid)
            ->findOrEmpty();
        if ($zhanhuiConfigInfo->isEmpty()) {
            throwException(SysErrorCode::ZHANHUI_NOT_EXIST);
        }
        // 判断展会是否已经结束或已关闭
        if (
            $zhanhuiInfo['status'] == MerchantZhanhuiModel::STATUS_DISABLE
            || ($zhanhuiInfo['endTime'] < time() && $zhanhuiInfo['endIsShow'] == 0)
        ) {
            throwException(SysErrorCode::ZHANHUI_NOT_EXIST);
        }
        $zhanhuiInfo['startTime'] = date("Y-m-d", $zhanhuiInfo['startTime']);
        $zhanhuiInfo['endTime'] = date("Y-m-d", $zhanhuiInfo['endTime']);
        $zhanhuiInfo['welcomeText'] = $zhanhuiConfigInfo['welcomeText'];
        $zhanhuiInfo['isRequirePhone'] = $zhanhuiConfigInfo['isRequirePhone'];
        $zhanhuiInfo['topicBannerTitle'] = $zhanhuiConfigInfo['topicBannerTitle'];
        $zhanhuiInfo['guestBannerTitle'] = $zhanhuiConfigInfo['guestBannerTitle'];
        $zhanhuiInfo['recommendCompanyTitle'] = $zhanhuiConfigInfo['recommendCompanyTitle'];
        $zhanhuiInfo['aboutZhanhuiTitle'] = $zhanhuiConfigInfo['aboutZhanhuiTitle'];
        return $zhanhuiInfo->toArray();
    }

    /**
     * 展会FAQ列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function faqLists($params)
    {
        $faqList = MerchantZhanhuiFaqModel::getInstance()
            ->field('guid, question, ai_chat_question, answer_type, position, icon_type, sort')
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->where('position', $params['position'])
            ->order('sort', 'asc')
            ->select();

        return $faqList->toArray();
    }

    /**
     * 展会Banner列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function bannerLists($params)
    {
        $bannerList = MerchantZhanhuiBannerModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->where('banner_type', $params['bannerType'])
            ->order('sort', 'asc')
            ->select();

        return $bannerList->toArray();
    }

    /**
     * FAQ详情
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function faqDetail($params)
    {
        $faqInfo = MerchantZhanhuiFaqModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($faqInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, 'FAQ不存在');
        }
        return $faqInfo->toArray();
    }

    private function getMerchantId()
    {
        // 优先判断URL是否存在merchantGuid
        if (request()->get('merchantGuid')) {
            return request()->get('merchantGuid');
        }
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        return UsersModel::getInstance()->where('sys_id', $userId)->value('merchant_guid');
    }

    public function showConfigs($params)
    {
        $merchatnGuid = $params['merchantGuid'] ?? '';
        // 获取商家展会名称和默认分享图
        $zhanhuiName = 'AI商协通';
        $configName = MerchantConfigModel::getConfigValue($merchatnGuid, MerchantConfigModel::CONFIG_ZHANHUI_NAME);
        if ($configName) {
            $zhanhuiName = $configName;
        }
        $shareImg = '';
        $configImg = MerchantConfigModel::getConfigValue($merchatnGuid, MerchantConfigModel::CONFIG_ZHANHUI_DEFAULT_SHARE_IMAGE);
        if ($configImg) {
            $shareImg = $configImg;
        }
        return [
            'zhanhuiName' => $zhanhuiName,
            'shareImg' => $shareImg,
        ];
    }

    /**
     * 留言板创建留言
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function messageBoardCreate($params)
    {
        $zhanhuiGuid = $params['zhanhuiGuid'];
        $chatContent = $params['chatContent'];
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        $platformUser = UsersModel::getInstance()->where('sys_id', $platformUserId)->find();
        if (!$platformUser) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()->where('guid', $zhanhuiGuid)->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        // 判断该用户是否有留言板信息
        $existUserBoard = MerchantZhanhuiMessageBoardModel::getInstance()
            ->where('merchant_guid', $zhanhuiInfo->merchantGuid)
            ->where('platform_user_sys_id', $platformUserId)
            ->where('zhanhui_guid', $zhanhuiGuid)
            ->findOrEmpty();
        if ($existUserBoard->isEmpty()) {
            // 创建留言板
            $boardModel = new MerchantZhanhuiMessageBoardModel();
            $boardModel->merchantGuid = $zhanhuiInfo->merchantGuid;
            $boardModel->platformUserSysId = $platformUserId;
            $boardModel->zhanhuiGuid = $zhanhuiGuid;
            $boardModel->content = $chatContent;
            $boardModel->createTime = time();
            $boardModel->save();

            $boardGuid = $boardModel->guid;

            $existUserBoard = $boardModel;
        } else {
            $boardGuid = $existUserBoard->guid;
        }


        // 创建展会消息
        $chatModel = new MerchantZhanhuiMessageBoardChatModel();
        $chatModel->merchantGuid = $zhanhuiInfo->merchantGuid;
        $chatModel->platformUserSysId = $platformUserId;
        $chatModel->zhanhuiGuid = $zhanhuiGuid;
        $chatModel->messageBoardGuid = $boardGuid;
        $chatModel->chatContent = $chatContent;
        $chatModel->replyType = MerchantZhanhuiMessageBoardChatModel::REPLY_TYPE_USER;
        $chatModel->sendDay = date('Ymd');
        $chatModel->createTime = time();
        $chatModel->save();

        // 更新最后一次留言时间
        $existUserBoard->update_time = time();
        $existUserBoard->save();

        return [];
    }

    /**
     * 获取留言板对话列表
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function messageBoardLists($params)
    {
        $zhanhuiGuid = $params['zhanhuiGuid'];
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        $platformUser = UsersModel::getInstance()->where('sys_id', $platformUserId)->find();
        if (!$platformUser) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()->where('guid', $zhanhuiGuid)->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        // 判断该用户是否有留言板信息
        $existUserBoard = MerchantZhanhuiMessageBoardModel::getInstance()
            ->where('merchant_guid', $zhanhuiInfo->merchantGuid)
            ->where('platform_user_sys_id', $platformUserId)
            ->where('zhanhui_guid', $zhanhuiGuid)
            ->findOrEmpty();
        if ($existUserBoard->isEmpty()) {
            return [];
        }
        // 获取留言板对话列表
        $chatList = MerchantZhanhuiMessageBoardChatModel::getInstance()
            ->where('merchant_guid', $zhanhuiInfo->merchantGuid)
            ->where('platform_user_sys_id', $platformUserId)
            ->where('zhanhui_guid', $zhanhuiGuid)
            ->where('message_board_guid', $existUserBoard->guid)
            ->order('create_time', 'asc')
            ->select();

        return $chatList->toArray();
    }

    /**
     * 展会管理员获取留言列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function adminMessageBoardLists($params)
    {
        // 获取该用户管理的展会列表
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        $zhanhuiGuids = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('platform_user_sys_id', $platformUserId)
            ->column('zhanhui_guid');
        if (empty($zhanhuiGuids)) {
            return [];
        }
        // 获取展会留言板对话列表
        $pageSize = $params['pageSize'] ?? 10;
        $chatList = MerchantZhanhuiMessageBoardModel::getInstance()
            ->where('admin_is_delete', 0)
            ->where('zhanhui_guid', 'in', $zhanhuiGuids)
            ->order('update_time', 'desc')
            ->paginate($pageSize);
        return $chatList->toArray();
    }

    /**
     * 展会管理员获取留言板对话列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function adminMessageBoardChatLists($params)
    {
        $msgList = MerchantZhanhuiMessageBoardChatModel::getInstance()
            ->where('message_board_guid', $params['messageBoardGuid'])
            ->order('create_time', 'asc')
            ->select();

        return $msgList->toArray();
    }

    /**
     * 展会管理员回复留言
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function adminMessageBoardChatReply($params)
    {
        // 获取该留言板信息
        $messageBoardInfo = MerchantZhanhuiMessageBoardModel::getInstance()
            ->where('guid', $params['messageBoardGuid'])
            ->findOrEmpty();
        if ($messageBoardInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '留言板不存在');
        }
        // 新增管理员回复内容
        $chatModel = new MerchantZhanhuiMessageBoardChatModel();
        $chatModel->merchantGuid = $messageBoardInfo->merchantGuid;
        $chatModel->platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $chatModel->zhanhuiGuid = $messageBoardInfo->zhanhuiGuid;
        $chatModel->messageBoardGuid = $messageBoardInfo->guid;
        $chatModel->chatContent = $params['chatContent'];
        $chatModel->replyType = MerchantZhanhuiMessageBoardChatModel::REPLY_TYPE_MERCHANT;
        $chatModel->sendDay = date('Ymd');
        $chatModel->createTime = time();
        $chatModel->save();

        return [];
    }

    /**
     * 展会管理员编辑留言板信息
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function adminUpdataMessageBoard($params)
    {
        $chatModel = MerchantZhanhuiMessageBoardModel::getInstance()
            ->where('guid', $params['messageBoardGuid'])
            ->findOrEmpty();
        if ($chatModel->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '留言板不存在');
        }
        $chatModel->adminIsDelete = $params['adminIsDelete'] ?? 0;
        $chatModel->note = $params['note'] ?? '';
        $chatModel->save();

        return [];
    }

    /**
     * 展会管理员获取留言板信息
     * @param $params
     * @return MerchantZhanhuiFaqModel|array|mixed|\think\Model
     */
    public function queryFaq($params)
    {
        $existFap = MerchantZhanhuiFaqModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->where('question', 'like', '%' . $params['question'] . '%')
            ->findOrEmpty();
        if ($existFap->isEmpty()) {
            return new \stdClass();
        }
        return $existFap;
    }

    /**
     * AI创建展会协议内容
     * @return array
     */
    public function aiAgreement()
    {
        $content = '<div style="padding: 20px; line-height: 1.8; font-size: 14px; color: #333;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">商协通AI内容生成服务协议</h2>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">一、服务说明</h3>
            <p><strong>1.1</strong> 用户通过上传企业资料（包括但不限于文本、图片、视频、商业文档等），授权本平台使用AI技术自动生成小程序主页内容。</p>
            <p><strong>1.2</strong> 生成的主页内容基于用户提交的资料进行智能分析、重组及优化，最终呈现形式由系统算法决定。</p>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">二、用户授权内容</h3>
            <h4 style="color: #2980b9;">2.1 数据存储授权</h4>
            <p>用户确认：上传的所有资料将被存储于本平台服务器，用于实现AI生成、服务优化及后续功能调用。</p>

            <h4 style="color: #2980b9;">2.2 内容处理授权</h4>
            <p>用户授予本平台非独占性、全球范围、免版税的许可，允许：</p>
            <ul style="margin-left: 20px;">
                <li>对上传资料进行解析、结构化处理及机器学习训练；</li>
                <li>调用资料内容生成、修改并展示主页信息；</li>
                <li>在用户账号内长期保留生成内容以提供持续性服务。</li>
            </ul>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">三、知识产权声明</h3>
            <p><strong>3.1</strong> 用户保证上传资料不侵犯第三方权利，且拥有必要授权。</p>
            <p><strong>3.2</strong> <strong>原始资料权属：</strong>用户保留上传资料的所有知识产权。</p>
            <p><strong>3.3</strong> <strong>生成内容权属：</strong></p>
            <p style="margin-left: 20px;">AI生成的主页设计（版式/结构/功能框架）知识产权归我司所有；</p>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">四、数据安全与隐私</h3>
            <p><strong>4.1</strong> 平台将采取行业标准加密技术保护用户数据，但排除以下情形：</p>
            <ul style="margin-left: 20px;">
                <li>用户主动公开分享的内容；</li>
                <li>因法律法规要求或司法程序需要披露的数据。</li>
            </ul>
            <p><strong>4.2</strong> 用户数据不会用于与主页生成无关的广告推广，具体隐私政策详见《商协通隐私条款》。</p>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">五、免责条款</h3>
            <p><strong>5.1</strong> AI生成内容基于算法自动处理，平台不保证内容的绝对准确性、完整性或适用性，用户需自行核实并承担使用风险。</p>
            <p><strong>5.2</strong> 因用户上传违法、侵权资料导致的纠纷，由用户承担全部责任。</p>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">六、协议终止与数据清除</h3>
            <p><strong>6.1</strong> 用户可随时删除上传资料及生成主页，删除后平台将30日内清除系统备份。</p>
            <p><strong>6.2</strong> 用户注销账号时，本协议自动终止，数据按前述规则处理。</p>

            <h3 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">七、其他</h3>
            <p><strong>7.1</strong> 本协议是《商协通用户协议》的补充条款，冲突时以本协议为准。</p>
            <p><strong>7.2</strong> 平台保留修改本协议的权利，重大变更将通过站内通知或邮件告知用户。</p>
            <p><strong>7.3</strong> 继续使用服务视为接受修改后的协议。</p>

            <div style="margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-left: 4px solid #3498db; border-radius: 4px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">用户签署方式：</h3>
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="font-size: 16px; margin-right: 8px;">☐</span>
                    <span style="font-size: 14px; color: #2c3e50;">我已阅读并同意《商协通AI内容生成服务协议》，理解并接受全部条款。</span>
                </div>
            </div>
        </div>';

        return [
            'content' => $content
        ];
    }
}
