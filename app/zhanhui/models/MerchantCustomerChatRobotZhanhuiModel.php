<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $robotGuid 机器人guid
 * @property string $zhanhuiGuid 展会guid
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantCustomerChatRobotZhanhuiModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_customer_chat_robot_zhanhui';
    protected static bool $isGuid = true;

    /**
     * 展会信息
     * @return \think\model\relation\HasOne
     */
    public function zhanhui()
    {
        return $this->hasOne(MerchantZhanhuiModel::class, 'guid', 'zhanhui_guid');
    }
}
