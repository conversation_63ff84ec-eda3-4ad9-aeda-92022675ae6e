<?php

namespace app\zhanhui\models;

use think\model\concern\SoftDelete;
use app\libraries\models\BaseModel;
use app\libraries\utils\traits\Signleton;

/**
 * 商家客服聊天机器人对话记录模型
 * @property int $sysId 主键ID
 * @property string $guid 唯一标识
 * @property string $merchantGuid 商家GUID
 * @property string $robotGuid 机器人GUID
 * @property string $otherPlatformChatUuid 第三方平台聊天轮次UUID
 * @property string $chatTitle 聊天标题
 * @property array $chatContent 聊天内容
 * @property int $createTime 创建时间
 * @property int $updateTime 更新时间
 * @property int $deletedAt 删除时间
 */
class MerchantCustomerChatRobotRecordModel extends BaseModel
{
    use SoftDelete;
    use Signleton;

    protected static bool $isGuid = true;

    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'merchant_customer_chat_robot_record';

    /**
     * 主键名称
     * @var string
     */
    protected $pk = 'sys_id';

    /**
     * 软删除字段名
     * @var string
     */
    protected $deleteTime = 'deleted_at';

    /**
     * 自动类型转换
     * @var array
     */
    protected $type = [
        'sys_id' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'deleted_at' => 'integer',
    ];

    /**
     * JSON类型字段
     * @var array
     */
    protected $json = ['chat_content'];
    protected $jsonAssoc = true;

    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'update_time';

    /**
     * 字段定义
     */
    public static $fields = [
        'sys_id' => '主键ID',
        'guid' => '唯一标识',
        'merchant_guid' => '商家GUID',
        'robot_guid' => '机器人GUID',
        'other_platform_chat_uuid' => '第三方平台聊天轮次UUID',
        'chat_title' => '聊天标题',
        'chat_content' => '聊天内容',
        'create_time' => '创建时间',
        'update_time' => '更新时间',
        'deleted_at' => '删除时间'
    ];

    protected $table = 'merchant_customer_chat_robot_record';
} 