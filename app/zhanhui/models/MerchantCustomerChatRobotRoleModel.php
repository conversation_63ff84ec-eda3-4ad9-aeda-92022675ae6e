<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $robotGuid 机器人guid
 * @property string $roleName 角色名称
 * @property string $roleDesc 角色描述，说明当前角色用于什么场景
 * @property string $rolePrompt 角色AI提示词
 * @property string $knowledgeBindType 知识库绑定类型：default-默认展位知识库；custom-自定义
 * @property array $customKnowledgeGuids 自定义知识库列表
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantCustomerChatRobotRoleModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_customer_chat_robot_role';

    protected static bool $isGuid = true;

    protected $json = ['custom_knowledge_guids'];
}
