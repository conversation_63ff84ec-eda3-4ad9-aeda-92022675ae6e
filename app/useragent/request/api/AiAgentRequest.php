<?php

declare(strict_types=1);

namespace app\useragent\request\api;

use app\Request;

/**
 * 智能体API请求验证类
 */
class AiAgentRequest extends Request
{
    protected array $msgs = [
        'guid' => 'GUID必填',
        'merchantGuid' => '商户GUID必填',
        'agentName' => '智能体名称必填',
        'agentType' => '智能体类型必填',
        'categoryGuid' => '分类GUID必填',
        'promptContent' => '提示词必填',
        'welcomeMessage' => '开场白欢迎语长度超限',
        'commonQuestions' => '常见问题列表格式错误',
        'price' => '价格必须为数字',
        'agentDesc' => '智能体描述必填',
        'invitationCode' => '邀请码格式有误',
        'miniPath' => '小程序路径必填',
    ];

    protected function getRule(): array
    {
        return [
            'create' => [
                'merchantGuid' => ['require'],
                'agentName' => ['require', 'length:1,50'],
                'agentType' => ['require', 'in:1,2,3,4'], // 1-内部；2-dify；3-coze；4-阿里云百炼
                'categoryGuid' => ['require'],
                'promptContent' => ['length:1,3000'],
                'agentDesc' => ['length:0,500'],
                'agentAvatar' => ['length:0,255'],
                'welcomeMessage' => ['length:0,1000'],
                'commonQuestions' => ['array'],
                'isPaid' => ['in:0,1'],
                'price' => ['float', 'min:0'],
                'isPublic' => ['in:0,1'],
                'agentConfig' => ['array'],
                'knowledgeBaseIds' => ['array'],
            ],
            'myList' => [
                'merchantGuid' => ['require'],
            ],
            'myDetail' => [
                'guid' => ['require'],
            ],
            'update' => [
                'guid' => ['require'],
                'agentName' => ['require', 'length:1,50'],
                'agentType' => ['require', 'in:1,2,3,4'], // 1-内部；2-dify；3-coze；4-阿里云百炼
                'categoryGuid' => ['require'],
                'promptContent' => ['length:1,2000'],
                'agentDesc' => ['length:0,500'],
                'agentAvatar' => ['length:0,255'],
                'welcomeMessage' => ['length:0,1000'],
                'commonQuestions' => ['array'],
                'isPaid' => ['in:0,1'],
                'price' => ['float', 'min:0'],
                'isPublic' => ['in:0,1'],
                'agentConfig' => ['array'],
                'knowledgeBaseIds' => ['array'],
            ],
            'delete' => [
                'merchantGuid' => ['require'],
                'agentGuid' => ['require'],
            ],
            'generateAvatar' => [
                'merchantGuid' => ['require'],
                'agentName' => ['require', 'length:1,50'],
                'agentDesc' => ['require', 'length:1,500'],
            ],
            'agentDetail' => [
                'merchantGuid' => ['require'],
                // 'agentGuid' => ['require'],
            ],
            'bindInvitation' => [
                'merchantGuid' => ['require'],
                'invitationCode' => ['require', 'length:6,20'],
            ],
            'generateMiniCode' => [
                'merchantGuid' => ['require'],
                'miniPath' => ['require'],
                'pathQuery' => [],
            ],
        ];
    }
}
