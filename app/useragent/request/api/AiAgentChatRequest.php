<?php

declare(strict_types=1);

namespace app\useragent\request\api;

use app\Request;

/**
 * 智能体对话API请求验证类
 */
class AiAgentChatRequest extends Request
{
    protected array $msgs = [
        'merchantGuid' => '商户GUID必填',
        'sessionGuid' => '对话轮次GUID必填',
        'sessionTitle' => '对话标题必填且长度不能超过100字符',
        'role' => '聊天角色必填',
        'content' => '消息内容必填',
        'lastMsgId' => '上一条消息ID格式错误',
        'chatLunciGuid' => '聊天轮次GUID格式错误',
        'contentType' => '内容类型必须是text、image、voice、video或file',
        'imgs' => '图片列表必须是数组格式',
        'videos' => '视频列表必须是数组格式',
        'chatScene' => '聊天场景长度不能超过50字符',
        'sceneValue' => '场景值长度不能超过255字符',
        'msgId' => '消息ID必填',
        'aiModelGuid' => 'AI模型GUID格式错误',
        'topP' => '采样参数必须在0-1之间',
        'page' => '页码必须为正整数',
        'pageSize' => '每页数量必须为正整数',
        'messageGuid' => '消息GUID必填',
        'isTop' => '置顶状态值不正确',
    ];

    protected function getRule(): array
    {
        return [
            'saveMsg' => [
                'merchantGuid' => ['require'],
                'sessionGuid' => ['require', 'length:32'],
                'role' => ['require', 'in:user,assistant'],
                'content' => ['require'],
                'lastMsgId' => ['max:255'],
                'chatLunciGuid' => ['length:32'],
                //'contentType' => ['in:text,image,voice,video,file'],
                'imgs' => ['array'],
                'videos' => ['array'],
                'chatScene' => ['max:50'],
                'sceneValue' => ['max:255'],
            ],
            'sendOpen' => [
                'merchantGuid' => ['require'],
                'msgId' => ['require'],
                'aiModelGuid' => ['length:32'],
                'chatLunciGuid' => ['length:32'],
                'chatScene' => ['max:50'],
                'sceneValue' => ['max:255'],
                'topP' => ['float', 'between:0,1'],
            ],
            'sendAll' => [
                'merchantGuid' => ['require'],
                'msgId' => ['require'],
                'aiModelGuid' => ['length:32'],
                'chatLunciGuid' => ['length:32'],
                'chatScene' => ['max:50'],
                'sceneValue' => ['max:255'],
                'topP' => ['float', 'between:0,1'],
            ],
            'mySessionList' => [
                'merchantGuid' => ['require'],
                'agentGuid' => ['length:32'],
                'pageSize' => ['integer', 'between:1,100'],
            ],
            'messageHistory' => [
                'merchantGuid' => ['require'],
                'sessionGuid' => ['require'],
                'page' => ['integer', 'min:1'],
                'pageSize' => ['integer', 'between:1,100'],
                'isAll' => ['in:0,1'],
            ],
            'updateSessionTitle' => [
                'merchantGuid' => ['require'],
                'sessionGuid' => ['require'],
                'sessionTitle' => ['require', 'length:1,100'],
            ],
            'deleteSession' => [
                'merchantGuid' => ['require'],
                'sessionGuid' => ['require'],
            ],
            'collectMessage' => [
                'merchantGuid' => ['require'],
                'messageGuid' => ['require'],
            ],
            'uncollectMessage' => [
                'merchantGuid' => ['require'],
                'messageGuid' => ['require'],
            ],
            'deleteMessage' => [
                'merchantGuid' => ['require'],
                'messageGuid' => ['require'],
            ],
            'deleteAllMessages' => [
                'merchantGuid' => ['require'],
                'sessionGuid' => ['require'],
            ],
            'myCollectionList' => [
                'merchantGuid' => ['require'],
                'page' => ['integer', 'min:1'],
                'pageSize' => ['integer', 'between:1,100'],
                'agentGuid' => [],
            ],
            'setSessionTop' => [
                'merchantGuid' => ['require'],
                'sessionGuid' => ['require'],
                'isTop' => ['require', 'in:0,1'],
            ],
            'getUserPoints' => [
                'merchantGuid' => ['require'],
            ],
        ];
    }
}
