<?php

declare(strict_types=1);

namespace app\useragent\request\admin;

use app\Request;

/**
 * 智能体管理请求验证类
 */
class AiAgentRequest extends Request
{
    protected array $msgs = [
        'guid' => 'GUID必填',
        'merchantGuid' => '商户GUID必填',
        'auditStatus' => '审核状态值错误',
        'status' => '状态值错误',
        'price' => '价格必须为正整数',
        'isFeatured' => '是否为精品值错误',
    ];

    protected function getRule(): array
    {
        return [
            'index' => [
                'merchantGuid' => ['require'],
            ],
            'detail' => [
                'guid' => ['require'],
            ],
            'audit' => [
                'guid' => ['require'],
                'auditStatus' => ['require', 'in:2,3'], // 2-审核通过；3-审核拒绝
            ],
            'updateStatus' => [
                'guid' => ['require'],
                'status' => ['require', 'in:1,2'], // 1-正常；2-下架
            ],
            'delete' => [
                'guid' => ['require'],
            ],
            'update' => [
                'guid' => ['require'],
                'price' => ['integer', 'min:0'],
                'isFeatured' => ['in:0,1'],
            ],
        ];
    }
}
