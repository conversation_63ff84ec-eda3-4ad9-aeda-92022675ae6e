<?php

declare(strict_types=1);

namespace app\useragent\service;

use app\useragent\models\MerchantAiAgentSystemNoticeModel;

/**
 * 系统公告服务类
 * 用于自动生成系统公告
 */
class SystemNoticeService
{
    /**
     * 智能体审核通过事件
     * @param string $merchantGuid
     * @param string $agentName
     * @param string $creatorName
     * @return bool
     */
    public static function onAgentAuditPassed(string $merchantGuid, string $agentName, string $creatorName): bool
    {
        $title = '智能体审核通过';
        $content = "恭喜！智能体「{$agentName}」已通过审核，现在可以正常使用了。感谢 {$creatorName} 的精彩创作！";
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'agent_audit_passed',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_SYSTEM,
                'noticeIcon' => '✅',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_NO,
            ]
        );
    }

    /**
     * 新用户注册事件
     * @param string $merchantGuid
     * @param string $userName
     * @return bool
     */
    public static function onUserRegistered(string $merchantGuid, string $userName): bool
    {
        $title = '欢迎新用户';
        $content = "欢迎 {$userName} 加入我们的智能体平台！快来创建你的第一个智能体吧！";
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'user_registered',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_SYSTEM,
                'noticeIcon' => '🎉',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_NO,
            ]
        );
    }

    /**
     * 智能体创建成功事件
     * @param string $merchantGuid
     * @param string $agentName
     * @param string $creatorName
     * @return bool
     */
    public static function onAgentCreated(string $merchantGuid, string $agentName, string $creatorName): bool
    {
        $title = '新智能体创建';
        $content = "{$creatorName} 创建了新的智能体「{$agentName}」，正在等待审核中...";
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'agent_created',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_SYSTEM,
                'noticeIcon' => '🤖',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_NO,
            ]
        );
    }

    /**
     * 智能体购买成功事件
     * @param string $merchantGuid
     * @param string $agentName
     * @param string $buyerName
     * @param float $amount
     * @return bool
     */
    public static function onAgentPurchased(string $merchantGuid, string $agentName, string $buyerName, float $amount): bool
    {
        $title = '智能体购买成功';
        $content = "{$buyerName} 购买了智能体「{$agentName}」，支付金额 ￥{$amount}";
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'agent_purchased',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_SYSTEM,
                'noticeIcon' => '💰',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_YES,
            ]
        );
    }

    /**
     * 系统维护通知
     * @param string $merchantGuid
     * @param string $maintenanceTime
     * @param string $duration
     * @return bool
     */
    public static function onSystemMaintenance(string $merchantGuid, string $maintenanceTime, string $duration): bool
    {
        $title = '系统维护通知';
        $content = "系统将于 {$maintenanceTime} 进行维护升级，预计耗时 {$duration}，期间可能影响正常使用，请提前做好准备。";
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'system_maintenance',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_MAINTENANCE,
                'noticeLevel' => MerchantAiAgentSystemNoticeModel::NOTICE_LEVEL_IMPORTANT,
                'noticeIcon' => '🔧',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_YES,
                'isTop' => MerchantAiAgentSystemNoticeModel::TOP_YES,
            ]
        );
    }

    /**
     * 功能更新通知
     * @param string $merchantGuid
     * @param string $featureName
     * @param string $description
     * @return bool
     */
    public static function onFeatureUpdate(string $merchantGuid, string $featureName, string $description): bool
    {
        $title = "功能更新：{$featureName}";
        $content = $description;
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'feature_update',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_UPDATE,
                'noticeIcon' => '🚀',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_YES,
            ]
        );
    }

    /**
     * 活动开始通知
     * @param string $merchantGuid
     * @param string $activityName
     * @param string $description
     * @param string $activityUrl
     * @return bool
     */
    public static function onActivityStart(string $merchantGuid, string $activityName, string $description, string $activityUrl = ''): bool
    {
        $title = "活动开始：{$activityName}";
        $content = $description;
        
        $options = [
            'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_ACTIVITY,
            'noticeIcon' => '🎉',
            'isHot' => MerchantAiAgentSystemNoticeModel::HOT_YES,
            'isTop' => MerchantAiAgentSystemNoticeModel::TOP_YES,
        ];

        // 如果有活动链接，设置跳转
        if (!empty($activityUrl)) {
            $options['jumpType'] = MerchantAiAgentSystemNoticeModel::JUMP_TYPE_INTERNAL;
            $options['jumpUrl'] = $activityUrl;
        }
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'activity_start',
            $options
        );
    }

    /**
     * 热门智能体推荐
     * @param string $merchantGuid
     * @param array $hotAgents
     * @return bool
     */
    public static function onHotAgentsRecommend(string $merchantGuid, array $hotAgents): bool
    {
        $agentNames = array_column($hotAgents, 'agentName');
        $agentList = implode('、', array_slice($agentNames, 0, 3));
        
        $title = '热门智能体推荐';
        $content = "本周热门智能体：{$agentList} 等，快来体验吧！";
        
        return MerchantAiAgentSystemNoticeModel::autoCreateNotice(
            $merchantGuid,
            $title,
            $content,
            'hot_agents_recommend',
            [
                'noticeType' => MerchantAiAgentSystemNoticeModel::NOTICE_TYPE_SYSTEM,
                'noticeIcon' => '🔥',
                'isHot' => MerchantAiAgentSystemNoticeModel::HOT_YES,
            ]
        );
    }

    /**
     * 批量清理过期公告
     * @param string $merchantGuid
     * @return int 清理数量
     */
    public static function cleanExpiredNotices(string $merchantGuid): int
    {
        $currentTime = time();
        
        return MerchantAiAgentSystemNoticeModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('expire_time', '>', 0)
            ->where('expire_time', '<', $currentTime)
            ->where('status', MerchantAiAgentSystemNoticeModel::STATUS_NORMAL)
            ->update(['status' => MerchantAiAgentSystemNoticeModel::STATUS_HIDDEN]);
    }
}
