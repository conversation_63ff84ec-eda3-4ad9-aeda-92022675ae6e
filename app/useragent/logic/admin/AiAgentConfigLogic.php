<?php

declare(strict_types=1);

namespace app\useragent\logic\admin;

use app\useragent\models\MerchantAiAgentConfigModel;
use app\constdir\SysErrorCode;

/**
 * 平台配置管理业务逻辑类
 */
class AiAgentConfigLogic
{
    /**
     * 获取配置
     * @param array $params
     * @return array
     */
    public function index(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];

        // 获取所有配置（包含默认值）
        $allConfigs = MerchantAiAgentConfigModel::getMerchantConfigs($merchantGuid);

        // 转换为前端需要的格式（只返回值）
        $result = [];
        foreach ($allConfigs as $key => $config) {
            $result[$key] = $config['value'];
        }

        return $result;
    }

    /**
     * 更新配置
     * @param array $params
     * @return array
     */
    public function update(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $configs = $params['configs'];

        // 配置项定义
        $configDefinitions = [
            'platform_fee_rate' => ['type' => 'float', 'desc' => '平台手续费比例（百分比）'],
            'commission_rate' => ['type' => 'float', 'desc' => '邀请佣金比例（百分比）'],
            'auto_audit_enabled' => ['type' => 'bool', 'desc' => '智能体自动审批开关'],
            'daily_create_limit' => ['type' => 'int', 'desc' => '单人单日最大创建智能体个数'],
            'total_create_limit' => ['type' => 'int', 'desc' => '单人累计最大创建智能体个数'],
            'max_agent_price' => ['type' => 'float', 'desc' => '智能体最大收费金额（元）'],
            'daily_withdraw_limit' => ['type' => 'float', 'desc' => '单人单日最大提现金额（元）'],
            'single_withdraw_limit' => ['type' => 'float', 'desc' => '单笔提现金额上限（元）'],
            'withdraw_fee_rate' => ['type' => 'float', 'desc' => '提现手续费比例（百分比）'],
            'supported_ai_models' => ['type' => 'json', 'desc' => '支持的AI模型配置'],
            'shuziren_chanjing_key' => ['type' => 'string', 'desc' => '数字人蝉镜key配置'],
            'shuziren_chanjing_appid' => ['type' => 'string', 'desc' => '数字人蝉镜APPID配置'],
            'shuziren_points_per_second' => ['type' => 'int', 'desc' => '数字人每秒收费点数'],
        ];

        // 验证和保存配置
        foreach ($configs as $key => $value) {
            if (!isset($configDefinitions[$key])) {
                continue; // 跳过未定义的配置项
            }

            $definition = $configDefinitions[$key];

            // 数据验证
            if ($definition['type'] === 'float' && !is_numeric($value)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, "配置项 {$key} 必须为数字");
            }
            if ($definition['type'] === 'int' && !is_numeric($value)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, "配置项 {$key} 必须为整数");
            }
            if ($definition['type'] === 'bool' && !is_bool($value)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, "配置项 {$key} 必须为布尔值");
            }

            // 特殊验证
            if ($key === 'platform_fee_rate' && ($value < 0 || $value > 100)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '平台手续费比例必须在0-100之间');
            }
            if ($key === 'commission_rate' && ($value < 0 || $value > 100)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '邀请佣金比例必须在0-100之间');
            }
            if ($key === 'withdraw_fee_rate' && ($value < 0 || $value > 100)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '提现手续费比例必须在0-100之间');
            }
            if ($key === 'shuziren_chanjing_key' && !empty($value) && strlen($value) > 500) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '数字人蝉镜key长度不能超过500个字符');
            }
            if ($key === 'shuziren_chanjing_appid' && !empty($value) && strlen($value) > 100) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '数字人蝉镜APPID长度不能超过100个字符');
            }
            if ($key === 'shuziren_points_per_second' && ($value < 1 || $value > 10000)) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '数字人每秒收费点数必须在1-10000之间');
            }

            // 保存配置
            if (
                !MerchantAiAgentConfigModel::setConfig(
                    $merchantGuid,
                    $key,
                    $value,
                    $definition['desc'],
                    $definition['type']
                )
            ) {
                throwException(SysErrorCode::SYS_ERROR_CODE, "保存配置项 {$key} 失败");
            }
        }

        return [];
    }
}
