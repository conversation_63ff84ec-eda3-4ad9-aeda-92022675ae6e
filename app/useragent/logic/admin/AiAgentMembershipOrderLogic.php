<?php

declare(strict_types=1);

namespace app\useragent\logic\admin;

use app\constdir\SysErrorCode;
use app\useragent\models\UserAiAgentMembershipOrderModel;
use app\useragent\models\UserAiAgentMembershipPackageModel;
use app\useragent\models\UserAiAgentMembershipSubscriptionModel;
use app\useragent\models\UserAiAgentInvitationModel;
use app\useragent\models\UserAiAgentWalletModel;
use app\useragent\models\MerchantAiAgentConfigModel;
use app\user\models\UsersModel;
use think\facade\Db;

/**
 * 智能体会员订单管理业务逻辑类
 */
class AiAgentMembershipOrderLogic
{
    /**
     * 订单列表
     * @param array $params
     * @return array
     */
    public function index(array $params): array
    {
        $query = UserAiAgentMembershipOrderModel::getInstance()
            ->where('merchant_guid', $params['merchantGuid']);

        // 按订单状态筛选
        if (!empty($params['orderStatus'])) {
            $query->where('order_status', $params['orderStatus']);
        }

        // 按套餐类型筛选
        if (!empty($params['packageType'])) {
            $query->where('package_type', $params['packageType']);
        }

        // 按订单号搜索
        if (!empty($params['orderNo'])) {
            $query->where('order_no', 'like', '%' . $params['orderNo'] . '%');
        }

        // 按套餐名称搜索
        if (!empty($params['packageName'])) {
            $query->where('package_name', 'like', '%' . $params['packageName'] . '%');
        }

        // 按用户手机号搜索
        if (!empty($params['userMobile'])) {
            $userIds = UsersModel::getInstance()
                ->where('mobile', 'like', '%' . $params['userMobile'] . '%')
                ->column('sys_id');
            if (!empty($userIds)) {
                $query->whereIn('platform_user_sys_id', $userIds);
            } else {
                $query->where('platform_user_sys_id', 0);
            }
        }

        // 按时间范围筛选
        if (!empty($params['startTime'])) {
            $query->where('create_time', '>=', strtotime($params['startTime']));
        }
        if (!empty($params['endTime'])) {
            $query->where('create_time', '<=', strtotime($params['endTime'] . ' 23:59:59'));
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        $list = $query->with(['buyer', 'package', 'inviter'])
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page,
            ]);

        $data = $list->toArray();

        // 处理数据格式
        foreach ($data['data'] as &$item) {
            $item['orderStatusText'] = UserAiAgentMembershipOrderModel::ORDER_STATUS_TEXT[$item['orderStatus']] ?? '';
            $item['packageTypeText'] = UserAiAgentMembershipOrderModel::PACKAGE_TYPE_TEXT[$item['packageType']] ?? '';
            $item['payAmountYuan'] = fen_to_yuan($item['payAmount']);
            $item['originalAmountYuan'] = fen_to_yuan($item['originalAmount']);
            $item['platformFeeYuan'] = fen_to_yuan($item['platformFee']);
            $item['inviterCommissionYuan'] = fen_to_yuan($item['inviterCommission']);
            $item['createTimeText'] = date('Y-m-d H:i:s', $item['createTime']);
            $item['payTimeText'] = $item['payTime'] > 0 ? date('Y-m-d H:i:s', $item['payTime']) : '';
            $item['expireTimeText'] = $item['expireTime'] > 0 ? date('Y-m-d H:i:s', $item['expireTime']) : '';

            // 处理用户信息
            if (!empty($item['buyer'])) {
                $item['buyerNickname'] = $item['buyer']['nickname'] ?? '';
                $item['buyerMobile'] = $item['buyer']['mobile'] ?? '';
            }

            // 处理邀请人信息
            if (!empty($item['inviter'])) {
                $item['inviterNickname'] = $item['inviter']['nickname'] ?? '';
            }
        }

        return $data;
    }

    /**
     * 订单详情
     * @param array $params
     * @return array
     */
    public function detail(array $params): array
    {
        $orderGuid = $params['orderGuid'];
        $merchantGuid = $params['merchantGuid'];

        $order = UserAiAgentMembershipOrderModel::getInstance()
            ->where('guid', $orderGuid)
            ->where('merchant_guid', $merchantGuid)
            ->with(['buyer', 'package', 'inviter'])
            ->findOrEmpty();

        if ($order->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '订单不存在');
        }

        $data = $order->toArray();
        $data['orderStatusText'] = UserAiAgentMembershipOrderModel::ORDER_STATUS_TEXT[$data['orderStatus']] ?? '';
        $data['packageTypeText'] = UserAiAgentMembershipOrderModel::PACKAGE_TYPE_TEXT[$data['packageType']] ?? '';
        $data['payAmountYuan'] = fen_to_yuan($data['payAmount']);
        $data['originalAmountYuan'] = fen_to_yuan($data['originalAmount']);
        $data['platformFeeYuan'] = fen_to_yuan($data['platformFee']);
        $data['inviterCommissionYuan'] = fen_to_yuan($data['inviterCommission']);
        $data['createTimeText'] = date('Y-m-d H:i:s', $data['createTime']);
        $data['payTimeText'] = $data['payTime'] > 0 ? date('Y-m-d H:i:s', $data['payTime']) : '';
        $data['expireTimeText'] = $data['expireTime'] > 0 ? date('Y-m-d H:i:s', $data['expireTime']) : '';

        return $data;
    }

    /**
     * 订单统计
     * @param array $params
     * @return array
     */
    public function statistics(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $startTime = !empty($params['startTime']) ? strtotime($params['startTime']) : 0;
        $endTime = !empty($params['endTime']) ? strtotime($params['endTime'] . ' 23:59:59') : time();

        $query = UserAiAgentMembershipOrderModel::getInstance()
            ->where('merchant_guid', $merchantGuid);

        if ($startTime > 0) {
            $query->where('create_time', '>=', $startTime);
        }
        if ($endTime > 0) {
            $query->where('create_time', '<=', $endTime);
        }

        // 总订单统计
        $totalOrders = $query->count();
        $paidOrders = $query->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)->count();
        $totalAmount = $query->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)->sum('pay_amount');
        $platformFee = $query->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)->sum('platform_fee');
        $inviterCommission = $query->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)->sum('inviter_commission');

        // 按状态统计
        $statusStats = UserAiAgentMembershipOrderModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<=', $endTime)
            ->field('order_status, COUNT(*) as count, SUM(pay_amount) as amount')
            ->group('order_status')
            ->select()
            ->toArray();

        $statusData = [];
        foreach ($statusStats as $stat) {
            $statusData[] = [
                'status' => $stat['orderStatus'],
                'statusText' => UserAiAgentMembershipOrderModel::ORDER_STATUS_TEXT[$stat['orderStatus']] ?? '',
                'count' => $stat['count'],
                'amount' => $stat['amount'] / 100,
            ];
        }

        // 按套餐类型统计
        $packageTypeStats = UserAiAgentMembershipOrderModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<=', $endTime)
            ->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)
            ->field('package_type, COUNT(*) as count, SUM(pay_amount) as amount')
            ->group('package_type')
            ->select()
            ->toArray();

        $packageTypeData = [];
        foreach ($packageTypeStats as $stat) {
            $packageTypeData[] = [
                'packageType' => $stat['packageType'],
                'packageTypeText' => UserAiAgentMembershipOrderModel::PACKAGE_TYPE_TEXT[$stat['packageType']] ?? '',
                'count' => $stat['count'],
                'amount' => $stat['amount'] / 100,
            ];
        }

        // 按日期统计（最近30天）
        $dailyStats = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dayStart = strtotime($date);
            $dayEnd = strtotime($date . ' 23:59:59');

            $dayOrders = UserAiAgentMembershipOrderModel::getInstance()
                ->where('merchant_guid', $merchantGuid)
                ->where('create_time', '>=', $dayStart)
                ->where('create_time', '<=', $dayEnd)
                ->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)
                ->count();

            $dayAmount = UserAiAgentMembershipOrderModel::getInstance()
                ->where('merchant_guid', $merchantGuid)
                ->where('create_time', '>=', $dayStart)
                ->where('create_time', '<=', $dayEnd)
                ->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)
                ->sum('pay_amount');

            $dailyStats[] = [
                'date' => $date,
                'orders' => $dayOrders,
                'amount' => $dayAmount / 100,
            ];
        }

        return [
            'overview' => [
                'totalOrders' => $totalOrders,
                'paidOrders' => $paidOrders,
                'totalAmount' => $totalAmount / 100,
                'platformFee' => $platformFee / 100,
                'inviterCommission' => $inviterCommission / 100,
                'successRate' => $totalOrders > 0 ? round($paidOrders / $totalOrders * 100, 2) : 0,
            ],
            'statusStats' => $statusData,
            'packageTypeStats' => $packageTypeData,
            'dailyStats' => $dailyStats,
        ];
    }

    /**
     * 导出订单
     * @param array $params
     * @return array
     */
    public function export(array $params): array
    {
        // 这里可以实现订单导出功能
        // 暂时返回提示信息
        return [
            'message' => '导出功能待实现'
        ];
    }

    /**
     * 手动添加用户智能体会员
     * @param array $params
     * @return array
     */
    public function manualAdd(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $packageGuid = $params['packageGuid'];
        $actualPayAmount = yuan_to_fen($params['actualPayAmount']); // 实际支付金额，元转分

        // 支持通过用户GUID或昵称来识别用户
        $userGuid = $params['userGuid'] ?? '';
        $nickname = $params['nickname'] ?? '';

        try {
            // 1. 验证用户
            $user = $this->validateUser($userGuid, $nickname);

            // 2. 验证套餐
            $package = $this->validatePackage($packageGuid, $merchantGuid);

            // 3. 检查订阅状态
            $this->checkExistingSubscription($user['sysId'], $merchantGuid);

            // 4. 执行购买流程
            $result = $this->executePurchase($user, $package, $merchantGuid, $actualPayAmount);

            return [
                'success' => true,
                'message' => '会员添加成功',
                'data' => [
                    'orderNo' => $result['orderNo'],
                    'subscriptionGuid' => $result['subscriptionGuid'],
                    'userInfo' => [
                        'sysId' => $user['sysId'],
                        'nickname' => $user['nickname'],
                        'mobile' => $user['mobile'] ?? '',
                    ],
                    'packageInfo' => [
                        'packageName' => $package['packageName'],
                        'packageType' => $package['packageType'],
                        'durationDays' => $package['durationDays'],
                        'salePrice' => fen_to_yuan($package['salePrice']),
                        'rewardPoints' => $package['rewardPoints'],
                    ],
                    'expireTime' => $result['expireTime'],
                    'expireTimeText' => $result['expireTimeText'],
                    'inviterCommission' => $result['inviterCommission'] > 0 ? fen_to_yuan($result['inviterCommission']) : 0,
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 验证用户（支持GUID或昵称）
     * @param string $userGuid
     * @param string $nickname
     * @return array
     * @throws \Exception
     */
    private function validateUser(string $userGuid, string $nickname): array
    {
        if (empty($userGuid) && empty($nickname)) {
            throw new \Exception('用户GUID和昵称不能同时为空，请至少提供其中一个');
        }

        $query = UsersModel::getInstance();

        if (!empty($userGuid)) {
            // 优先使用GUID查询
            $user = $query->where('guid', $userGuid)->findOrEmpty();
            if ($user->isEmpty()) {
                throw new \Exception("用户GUID '{$userGuid}' 不存在");
            }
            return $user->toArray();
        } else {
            // 使用昵称查询
            $users = $query->where('nickname', $nickname)->select()->toArray();

            if (empty($users)) {
                throw new \Exception("用户昵称 '{$nickname}' 不存在");
            }

            if (count($users) > 1) {
                throw new \Exception("用户昵称 '{$nickname}' 存在多个用户，请使用用户GUID进行精确识别");
            }

            return $users[0];
        }
    }

    /**
     * 验证套餐
     * @param string $packageGuid
     * @param string $merchantGuid
     * @return array
     * @throws \Exception
     */
    private function validatePackage(string $packageGuid, string $merchantGuid): array
    {
        $package = UserAiAgentMembershipPackageModel::getPackageDetail($packageGuid, $merchantGuid);

        if (empty($package)) {
            throw new \Exception("套餐GUID '{$packageGuid}' 不存在或已下架");
        }

        return $package;
    }

    /**
     * 检查现有订阅
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @throws \Exception
     */
    private function checkExistingSubscription(int $platformUserSysId, string $merchantGuid): void
    {
        $existingSubscription = UserAiAgentMembershipSubscriptionModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('subscription_status', UserAiAgentMembershipSubscriptionModel::SUBSCRIPTION_STATUS_ACTIVE)
            ->findOrEmpty();

        if (!$existingSubscription->isEmpty()) {
            throw new \Exception("用户已有有效的会员订阅，到期时间: " . date('Y-m-d H:i:s', $existingSubscription->expireTime));
        }
    }

    /**
     * 执行购买流程
     * @param array $user
     * @param array $package
     * @param string $merchantGuid
     * @param int $actualPayAmount 实际支付金额，单位：分
     * @return array
     * @throws \Exception
     */
    private function executePurchase(array $user, array $package, string $merchantGuid, int $actualPayAmount = 0): array
    {
        $platformUserSysId = $user['sysId'];
        $packageGuid = $package['guid'];

        // 获取邀请人信息
        $inviterUserId = $this->getInviterUserId($platformUserSysId);

        // 计算分成 - 使用实际支付金额
        $payAmount = $actualPayAmount; // 直接使用实际支付金额，允许为0
        $originalAmount = $package['originalPrice'] > 0 ? $package['originalPrice'] : $package['salePrice'];

        // 从配置中获取分成比例
        $platformRate = MerchantAiAgentConfigModel::getPlatformFeeRate($merchantGuid);
        $inviterRate = MerchantAiAgentConfigModel::getMembershipCommissionRate($merchantGuid);

        // 当实际支付金额为0时，不计入分佣
        if ($actualPayAmount > 0) {
            $platformFee = intval($payAmount * $platformRate / 100);
            $inviterCommission = $inviterUserId > 0 ? intval($payAmount * $inviterRate / 100) : 0;
        } else {
            $platformFee = 0;
            $inviterCommission = 0;
        }

        // 开启事务
        Db::startTrans();
        try {
            // 创建订单（直接设置为已支付状态）
            $order = new UserAiAgentMembershipOrderModel();
            $order->merchantGuid = $merchantGuid;
            $order->platformUserSysId = $platformUserSysId;
            $order->packageGuid = $packageGuid;
            $order->packageName = $package['packageName'];
            $order->packageType = $package['packageType'];
            $order->durationDays = $package['durationDays'];
            $order->orderNo = UserAiAgentMembershipOrderModel::generateOrderNo();
            $order->orderStatus = UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID; // 直接设置为已支付
            $order->payAmount = $payAmount; // 使用实际支付金额
            $order->originalAmount = $originalAmount;
            $order->platformFee = $platformFee;
            $order->inviterCommission = $inviterCommission;
            $order->inviterUserId = $inviterUserId;
            $order->payTime = time(); // 设置支付时间
            $order->expireTime = time() + 1800; // 30分钟过期（虽然已支付）
            $order->createTime = time();
            $order->updateTime = time();
            $order->save();

            // 执行支付成功逻辑
            $subscriptionGuid = $this->handlePaymentSuccess($order);

            // 提交事务
            Db::commit();

            return [
                'orderNo' => $order->orderNo,
                'subscriptionGuid' => $subscriptionGuid,
                'expireTime' => time() + ($package['durationDays'] * 24 * 3600),
                'expireTimeText' => date('Y-m-d H:i:s', time() + ($package['durationDays'] * 24 * 3600)),
                'actualPayAmount' => $payAmount,
                'platformFee' => $platformFee,
                'inviterCommission' => $inviterCommission,
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 获取邀请人用户ID
     * @param int $platformUserSysId
     * @return int
     */
    private function getInviterUserId(int $platformUserSysId): int
    {
        $invitation = UserAiAgentInvitationModel::getInstance()
            ->where('invitee_user_id', $platformUserSysId)
            ->findOrEmpty();

        return $invitation->isEmpty() ? 0 : $invitation->inviterUserId;
    }

    /**
     * 处理支付成功逻辑
     * @param UserAiAgentMembershipOrderModel $order
     * @return string
     * @throws \Exception
     */
    private function handlePaymentSuccess(UserAiAgentMembershipOrderModel $order): string
    {
        // 创建或更新会员订阅
        $subscriptionGuid = $this->createOrUpdateMembershipSubscription($order);

        // 处理邀请人分佣
        if ($order->inviterUserId > 0 && $order->inviterCommission > 0) {
            $this->addUserBalance(
                $order->inviterUserId,
                $order->inviterCommission,
                '会员购买邀请佣金',
                $order->orderNo,
                $order->merchantGuid
            );
        }

        return $subscriptionGuid;
    }

    /**
     * 创建或更新会员订阅
     * @param UserAiAgentMembershipOrderModel $order
     * @return string
     * @throws \Exception
     */
    private function createOrUpdateMembershipSubscription(UserAiAgentMembershipOrderModel $order): string
    {
        // 获取套餐信息，包含赠送点数
        $package = UserAiAgentMembershipPackageModel::getInstance()
            ->where('guid', $order->packageGuid)
            ->findOrEmpty();

        if ($package->isEmpty()) {
            throw new \Exception('套餐信息不存在');
        }

        // 检查是否已有有效订阅
        $existingSubscription = UserAiAgentMembershipSubscriptionModel::getInstance()
            ->where('platform_user_sys_id', $order->platformUserSysId)
            ->where('merchant_guid', $order->merchantGuid)
            ->where('subscription_status', UserAiAgentMembershipSubscriptionModel::SUBSCRIPTION_STATUS_ACTIVE)
            ->findOrEmpty();

        $startTime = time();
        $expireTime = $startTime + ($order->durationDays * 24 * 3600);

        if (!$existingSubscription->isEmpty()) {
            // 延长现有订阅
            $existingSubscription->expireTime = max($existingSubscription->expireTime, $startTime) + ($order->durationDays * 24 * 3600);
            $existingSubscription->renewCount = $existingSubscription->renewCount + 1;
            $existingSubscription->lastRenewTime = $startTime;
            $existingSubscription->rewardPoints = $existingSubscription->rewardPoints + $package->rewardPoints;
            $existingSubscription->updateTime = $startTime;
            $existingSubscription->save();

            $subscriptionGuid = $existingSubscription->guid;
        } else {
            // 创建新订阅
            $subscription = new UserAiAgentMembershipSubscriptionModel();
            $subscription->platformUserSysId = $order->platformUserSysId;
            $subscription->merchantGuid = $order->merchantGuid;
            $subscription->packageGuid = $order->packageGuid;
            $subscription->packageName = $order->packageName;
            $subscription->packageType = $order->packageType;
            $subscription->orderGuid = $order->guid;
            $subscription->subscriptionStatus = UserAiAgentMembershipSubscriptionModel::SUBSCRIPTION_STATUS_ACTIVE;
            $subscription->startTime = $startTime;
            $subscription->expireTime = $expireTime;
            $subscription->autoRenew = 0;
            $subscription->renewCount = 0;
            $subscription->lastRenewTime = 0;
            $subscription->rewardPoints = $package->rewardPoints;
            $subscription->createTime = $startTime;
            $subscription->updateTime = $startTime;
            $subscription->save();

            $subscriptionGuid = $subscription->guid;
        }

        // 增加用户点数
        if ($package->rewardPoints > 0) {
            $this->addUserPoints($order->platformUserSysId, $package->rewardPoints);
        }

        return $subscriptionGuid;
    }

    /**
     * 增加用户余额
     * @param int $userId
     * @param int $amount
     * @param string $desc
     * @param string $orderNo
     * @param string $merchantGuid
     * @throws \Exception
     */
    private function addUserBalance(int $userId, int $amount, string $desc, string $orderNo, string $merchantGuid): void
    {
        if ($amount <= 0) {
            return;
        }

        // 直接使用模型的静态方法，已经处理了钱包创建和余额更新
        $success = UserAiAgentWalletModel::addIncome($userId, $merchantGuid, $amount, $orderNo, $desc);

        if (!$success) {
            throw new \Exception('更新用户余额失败');
        }
    }

    /**
     * 增加用户点数
     * @param int $platformUserSysId
     * @param int $points
     * @throws \Exception
     */
    private function addUserPoints(int $platformUserSysId, int $points): void
    {
        if ($points <= 0) {
            return;
        }

        // 查询或创建用户资产记录
        $userAssets = \app\user\models\UserAssetsModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($userAssets->isEmpty()) {
            // 创建新的用户资产记录
            $userAssets = new \app\user\models\UserAssetsModel();
            $userAssets->platformUserSysId = $platformUserSysId;
            $userAssets->chatCount = $points;
            $userAssets->createTime = time();
            $userAssets->updateTime = time();
        } else {
            // 更新现有记录
            $userAssets->chatCount = $userAssets->chatCount + $points;
            $userAssets->updateTime = time();
        }

        $userAssets->save();
    }

    /**
     * 查询订单支付状态
     * @param array $params
     * @return array
     */
    public function queryPaymentStatus(array $params): array
    {
        $orderNo = $params['orderNo'];

        try {
            // 直接复用CallbackLogic的支付回调逻辑
            $callbackLogic = new \app\index\logic\CallbackLogic();
            $result = $callbackLogic->aiAgentMembershipNotify(['orderNo' => $orderNo]);

            // 查询订单最新状态
            $order = UserAiAgentMembershipOrderModel::getInstance()
                ->where('order_no', $orderNo)
                ->findOrEmpty();

            if ($order->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '订单不存在');
            }

            // 根据处理结果返回状态
            $isPaid = ($result->getData() === 'success');

            return [
                'isPaid' => $isPaid,
                'orderStatus' => $order->orderStatus,
                'orderStatusText' => UserAiAgentMembershipOrderModel::ORDER_STATUS_TEXT[$order->orderStatus] ?? '未知状态',
                'payTime' => $order->payTime,
                'payTimeText' => $order->payTime ? date('Y-m-d H:i:s', $order->payTime) : '',
                'orderNo' => $order->orderNo,
                'orderGuid' => $order->guid,
                'packageName' => $order->packageName,
                'payAmount' => fen_to_yuan($order->payAmount),
            ];

        } catch (\Exception $e) {
            // 如果处理失败，查询订单当前状态
            $order = UserAiAgentMembershipOrderModel::getInstance()
                ->where('order_no', $orderNo)
                ->findOrEmpty();

            if ($order->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '订单不存在');
            }

            return [
                'isPaid' => false,
                'orderStatus' => $order->orderStatus,
                'orderStatusText' => UserAiAgentMembershipOrderModel::ORDER_STATUS_TEXT[$order->orderStatus] ?? '未知状态',
                'payTime' => $order->payTime,
                'payTimeText' => $order->payTime ? date('Y-m-d H:i:s', $order->payTime) : '',
                'orderNo' => $order->orderNo,
                'orderGuid' => $order->guid,
                'packageName' => $order->packageName,
                'payAmount' => fen_to_yuan($order->payAmount),
                'error' => $e->getMessage(),
            ];
        }
    }
}