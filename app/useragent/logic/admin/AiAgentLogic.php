<?php

declare(strict_types=1);

namespace app\useragent\logic\admin;

use app\useragent\models\UserAiAgentModel;
use app\useragent\models\MerchantAiAgentCategoryModel;
use app\useragent\models\UserAiAgentSubscriptionModel;
use app\useragent\models\UserAiAgentPurchaseModel;
use app\user\models\UsersModel;
use app\constdir\SysErrorCode;

/**
 * 智能体管理业务逻辑类
 */
class AiAgentLogic
{
    /**
     * 智能体列表
     * @param array $params
     * @return array
     */
    public function index(array $params): array
    {
        $query = UserAiAgentModel::getInstance()
            ->where('merchant_guid', $params['merchantGuid']);

        // 按分类筛选
        if (!empty($params['categoryGuid'])) {
            $query->where('category_guid', $params['categoryGuid']);
        }

        // 按智能体类型筛选
        if (!empty($params['agentType'])) {
            $query->where('agent_type', $params['agentType']);
        }

        // 按审核状态筛选
        if (!empty($params['auditStatus'])) {
            $query->where('audit_status', $params['auditStatus']);
        }

        // 按状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 按是否付费筛选
        if (isset($params['isPaid']) && $params['isPaid'] !== '') {
            $query->where('is_paid', $params['isPaid']);
        }

        // 按智能体名称搜索
        if (!empty($params['agentName'])) {
            $query->where('agent_name', 'like', '%' . $params['agentName'] . '%');
        }

        // 按创建者搜索
        if (!empty($params['creatorNickname'])) {
            $userIds = UsersModel::getInstance()
                ->where('nickname', 'like', '%' . $params['creatorNickname'] . '%')
                ->column('sys_id');
            if (!empty($userIds)) {
                $query->whereIn('platform_user_sys_id', $userIds);
            } else {
                // 如果没有找到用户，返回空结果
                $query->where('platform_user_sys_id', 0);
            }
        }

        $result = $query->with(['category', 'creator'])
            ->order('create_time', 'desc')
            ->paginate($params['pageSize'] ?? 10)
            ->toArray();

        // 处理返回数据
        foreach ($result['data'] as &$item) {
            $item['agentTypeText'] = UserAiAgentModel::AGENT_TYPE_TEXT[$item['agentType']] ?? '';
            $item['auditStatusText'] = UserAiAgentModel::AUDIT_STATUS_TEXT[$item['auditStatus']] ?? '';
            $item['statusText'] = UserAiAgentModel::STATUS_TEXT[$item['status']] ?? '';
            $item['isPaidText'] = $item['isPaid'] ? '付费' : '免费';
            $item['priceText'] = $item['isPaid'] ? '￥' . ($item['price'] / 100) : '免费';
            $item['createTime'] = date('Y-m-d H:i:s', $item['createTime']);
            $item['updateTime'] = date('Y-m-d H:i:s', $item['updateTime']);
            $item['price'] = fen_to_yuan($item['price']);

            // 统计订阅数量
            $item['subscribeCount'] = UserAiAgentSubscriptionModel::getInstance()
                ->where('agent_guid', $item['guid'])
                ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
                ->count();
        }

        return $result;
    }

    /**
     * 智能体详情
     * @param array $params
     * @return array
     */
    public function detail(array $params): array
    {
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->with(['category', 'creator'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在');
        }

        $data = $agent->toArray();
        $data['agentTypeText'] = UserAiAgentModel::AGENT_TYPE_TEXT[$data['agentType']] ?? '';
        $data['auditStatusText'] = UserAiAgentModel::AUDIT_STATUS_TEXT[$data['auditStatus']] ?? '';
        $data['statusText'] = UserAiAgentModel::STATUS_TEXT[$data['status']] ?? '';
        $data['isPaidText'] = $data['isPaid'] ? '付费' : '免费';
        $data['priceText'] = $data['isPaid'] ? '￥' . ($data['price'] / 100) : '免费';
        $data['price'] = fen_to_yuan($data['price']);
        $data['createTime'] = date('Y-m-d H:i:s', $data['createTime']);
        $data['updateTime'] = date('Y-m-d H:i:s', $data['updateTime']);
        $data['auditTime'] = $data['auditTime'] ? date('Y-m-d H:i:s', $data['auditTime']) : '';

        // 统计数据
        $data['subscribeCount'] = UserAiAgentSubscriptionModel::getInstance()
            ->where('agent_guid', $params['guid'])
            ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
            ->count();

        $data['purchaseCount'] = UserAiAgentPurchaseModel::getInstance()
            ->where('agent_guid', $params['guid'])
            ->where('order_status', UserAiAgentPurchaseModel::ORDER_STATUS_PAID)
            ->count();

        $data['totalIncome'] = UserAiAgentPurchaseModel::getInstance()
            ->where('agent_guid', $params['guid'])
            ->where('order_status', UserAiAgentPurchaseModel::ORDER_STATUS_PAID)
            ->sum('creator_income');

        return $data;
    }

    /**
     * 审核智能体
     * @param array $params
     * @return array
     */
    public function audit(array $params): array
    {
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在');
        }

        if ($agent->audit_status == UserAiAgentModel::AUDIT_STATUS_APPROVED) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '该智能体已审核，无法重复操作');
        }

        $agent->audit_status = $params['auditStatus'];
        $agent->audit_remark = $params['auditRemark'] ?? '';
        $agent->audit_time = time();

        if (!$agent->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '审核失败');
        }

        return [];
    }

    /**
     * 更新智能体状态
     * @param array $params
     * @return array
     */
    public function updateStatus(array $params): array
    {
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在');
        }

        $agent->status = $params['status'];

        if (!$agent->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '更新状态失败');
        }

        return [];
    }

    /**
     * 删除智能体
     * @param array $params
     * @return array
     */
    public function delete(array $params): array
    {
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在');
        }

        // 检查是否有用户订阅
        $subscribeCount = UserAiAgentSubscriptionModel::getInstance()
            ->where('agent_guid', $params['guid'])
            ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
            ->count();

        if ($subscribeCount > 0) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '该智能体还有用户订阅，无法删除');
        }

        if (!$agent->delete()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '删除失败');
        }

        return [];
    }

    /**
     * 修改智能体
     * @param array $params
     * @return array
     */
    public function update(array $params): array
    {
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在');
        }

        // 更新价格
        if (isset($params['price'])) {
            $agent->price = yuan_to_fen($params['price']);
        }

        // 更新是否为精品
        if (isset($params['isFeatured'])) {
            $agent->isFeatured = (int)$params['isFeatured'];
        }

        if (!$agent->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '修改失败');
        }

        return [];
    }
}