<?php

declare(strict_types=1);

namespace app\useragent\logic\api;

use app\libraries\service\ai_workflow\tools\ZhipuTool;
use app\user\models\UsersModel;
use app\useragent\models\UserAiAgentModel;
use app\useragent\models\MerchantAiAgentCategoryModel;
use app\useragent\models\MerchantAiAgentConfigModel;
use app\useragent\models\UserAiAgentSubscriptionModel;
use app\useragent\models\UserAiAgentInvitationModel;
use app\useragent\models\UserAiAgentMembershipOrderModel;
use app\useragent\models\UserAiAgentChatSessionModel;
use app\libraries\service\token\TokenService;
use app\user\service\WechatService;
use app\libraries\service\ai_workflow\tools\AzureAiTool;
use app\libraries\service\ai_workflow\prompt\PromptBuildService;
use app\libraries\service\ai_open\ai_chat\AiChatFactory;
use app\libraries\service\ai_open\ai_chat\entity\ChatCompleteEntity;
use app\constdir\SysErrorCode;
use think\facade\Db;

/**
 * 智能体API业务逻辑类
 */
class AiAgentLogic
{
    /**
     * 创建智能体
     * @param array $params
     * @return array
     */
    public function create(array $params): array
    {
        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        $userInfo = UsersModel::getInstance()->where('sys_id', $platformUserSysId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $merchantGuid = $userInfo['merchantGuid'];

        // 验证分类是否存在
        $category = MerchantAiAgentCategoryModel::getInstance()
            ->where('guid', $params['categoryGuid'])
            ->where('merchant_guid', $merchantGuid)
            ->where('status', MerchantAiAgentCategoryModel::STATUS_ENABLED)
            ->findOrEmpty();

        if ($category->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '分类不存在或已禁用');
        }

        // 检查用户创建限制
        $this->checkCreateLimit($platformUserSysId, $merchantGuid);

        // 验证价格设置
        $isPaid = $params['isPaid'] ?? 0;
        $price = 0;
        if ($isPaid) {
            if (empty($params['price']) || $params['price'] <= 0) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '付费智能体必须设置价格');
            }

            $price = (int)($params['price'] * 100); // 转换为分
            $maxPrice = MerchantAiAgentConfigModel::getMaxAgentPrice($merchantGuid);
            $maxPrice = yuan_to_fen($maxPrice);
            if ($price > $maxPrice) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '智能体价格超过限制');
            }
        }

        // 创建智能体
        $agent = new UserAiAgentModel();
        $agent->merchant_guid = $merchantGuid;
        $agent->platform_user_sys_id = $platformUserSysId;
        $agent->category_guid = $params['categoryGuid'];
        $agent->agent_name = $params['agentName'];
        $agent->agent_desc = $params['agentDesc'] ?? '';
        $agent->agent_avatar = $params['agentAvatar'] ?? '';
        $agent->agent_type = $params['agentType'];
        $agent->agent_config = $params['agentConfig'] ?? [];
        $agent->prompt_content = $params['promptContent'] ?? '';
        $agent->welcome_message = $params['welcomeMessage'] ?? '';
        $agent->common_questions = $params['commonQuestions'] ?? [];
        $agent->knowledge_base_ids = $params['knowledgeBaseIds'] ?? [];
        $agent->is_paid = $isPaid;
        $agent->price = $price;
        $agent->is_public = $params['isPublic'] ?? 1;

        // 根据平台配置决定审核状态
        $autoAudit = MerchantAiAgentConfigModel::isAutoAuditEnabled($merchantGuid);
        $agent->audit_status = $autoAudit ? UserAiAgentModel::AUDIT_STATUS_APPROVED : UserAiAgentModel::AUDIT_STATUS_PENDING;
        $agent->audit_time = $autoAudit ? time() : 0;

        $agent->status = UserAiAgentModel::STATUS_NORMAL;
        $agent->view_count = 0;
        $agent->subscribe_count = 0;
        $agent->trialChatCount = $params['trial_chat_count'] ?? 3;

        if (!$agent->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '创建智能体失败');
        }

        return [
            'agentGuid' => $agent->guid,
            'auditStatus' => $agent->audit_status,
            'auditStatusText' => UserAiAgentModel::AUDIT_STATUS_TEXT[$agent->audit_status] ?? '',
            'message' => $autoAudit ? '智能体创建成功，已自动审核通过' : '智能体创建成功，等待审核'
        ];
    }

    /**
     * 检查用户创建限制
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @return void
     */
    private function checkCreateLimit(int $platformUserSysId, string $merchantGuid): void
    {
        return;
        // 检查单日创建限制
        $dailyLimit = MerchantAiAgentConfigModel::getDailyCreateLimit($merchantGuid);
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        $todayCount = UserAiAgentModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('create_time', '>=', $todayStart)
            ->where('create_time', '<=', $todayEnd)
            ->count();

        if ($todayCount >= $dailyLimit) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, "今日创建智能体数量已达上限（{$dailyLimit}个）");
        }

        // 检查累计创建限制
        $totalLimit = MerchantAiAgentConfigModel::getTotalCreateLimit($merchantGuid);
        $totalCount = UserAiAgentModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->count();

        if ($totalCount >= $totalLimit) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, "累计创建智能体数量已达上限（{$totalLimit}个）");
        }
    }

    /**
     * 我的智能体列表
     * @param array $params
     * @return array
     */
    public function myList(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        $query = UserAiAgentModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId);

        // 按审核状态筛选
        if (!empty($params['auditStatus'])) {
            $query->where('audit_status', $params['auditStatus']);
        }

        // 按状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 按是否付费筛选
        if (isset($params['isPaid']) && $params['isPaid'] !== '') {
            $query->where('is_paid', $params['isPaid']);
        }

        // 按智能体名称搜索
        if (!empty($params['agentName'])) {
            $query->where('agent_name', 'like', '%' . $params['agentName'] . '%');
        }

        $result = $query->with(['category'])
            ->order('create_time', 'desc')
            //->paginate($params['pageSize'] ?? 10)
            ->paginate(100)
            ->toArray();

        // 处理返回数据
        foreach ($result['data'] as &$item) {
            $item['agentTypeText'] = UserAiAgentModel::AGENT_TYPE_TEXT[$item['agentType']] ?? '';
            $item['auditStatusText'] = UserAiAgentModel::AUDIT_STATUS_TEXT[$item['auditStatus']] ?? '';
            $item['statusText'] = UserAiAgentModel::STATUS_TEXT[$item['status']] ?? '';
            $item['isPaidText'] = $item['isPaid'] ? '付费' : '免费';
            $item['priceText'] = $item['isPaid'] ? '￥' . ($item['price'] / 100) : '免费';
            $item['price'] = fen_to_yuan($item['price']);
            $item['isPublicText'] = $item['isPublic'] ? '公开' : '私有';
            $item['createTime'] = date('Y-m-d H:i:s', $item['createTime']);
            $item['updateTime'] = date('Y-m-d H:i:s', $item['updateTime']);
            $item['auditTime'] = $item['auditTime'] ? date('Y-m-d H:i:s', $item['auditTime']) : '';

            // 统计订阅数量（只有公开的智能体才有订阅）
            if ($item['isPublic']) {
                $item['subscribeCount'] = UserAiAgentSubscriptionModel::getInstance()
                    ->where('agent_guid', $item['guid'])
                    ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
                    ->count();
            } else {
                $item['subscribeCount'] = 0;
            }
        }

        return $result;
    }

    /**
     * 我的智能体详情
     * @param array $params
     * @return array
     */
    public function myDetail(array $params): array
    {
        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->where('platform_user_sys_id', $platformUserSysId)
            ->with(['category'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在或无权限访问');
        }

        $data = $agent->toArray();
        $data['agentTypeText'] = UserAiAgentModel::AGENT_TYPE_TEXT[$data['agentType']] ?? '';
        $data['auditStatusText'] = UserAiAgentModel::AUDIT_STATUS_TEXT[$data['auditStatus']] ?? '';
        $data['statusText'] = UserAiAgentModel::STATUS_TEXT[$data['status']] ?? '';
        $data['isPaidText'] = $data['isPaid'] ? '付费' : '免费';
        $data['priceText'] = $data['isPaid'] ? '￥' . ($data['price'] / 100) : '免费';
        $data['isPublicText'] = $data['isPublic'] ? '公开' : '私有';
        $data['createTime'] = date('Y-m-d H:i:s', $data['createTime']);
        $data['updateTime'] = date('Y-m-d H:i:s', $data['updateTime']);
        $data['auditTime'] = $data['auditTime'] ? date('Y-m-d H:i:s', $data['auditTime']) : '';
        $data['price'] = fen_to_yuan($data['price']);

        // 统计数据
        if ($data['isPublic']) {
            $data['subscribeCount'] = UserAiAgentSubscriptionModel::getInstance()
                ->where('agent_guid', $params['guid'])
                ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
                ->count();
        } else {
            $data['subscribeCount'] = 0;
        }

        return $data;
    }

    /**
     * 编辑修改我的智能体
     * @param array $params
     * @return array
     */
    public function update(array $params): array
    {
        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $params['guid'])
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在或无权限访问');
        }

        // 验证分类是否存在
        $category = MerchantAiAgentCategoryModel::getInstance()
            ->where('guid', $params['categoryGuid'])
            ->where('merchant_guid', $agent->merchant_guid)
            ->where('status', MerchantAiAgentCategoryModel::STATUS_ENABLED)
            ->findOrEmpty();

        if ($category->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '分类不存在或已禁用');
        }

        // 验证价格设置
        $isPaid = $params['isPaid'] ?? $agent->is_paid;
        $price = $agent->price;
        if ($isPaid) {
            if (empty($params['price']) || $params['price'] <= 0) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '付费智能体必须设置价格');
            }

            $price = (int)($params['price'] * 100); // 转换为分
            $maxPrice = MerchantAiAgentConfigModel::getMaxAgentPrice($agent->merchant_guid);
            if ($price > $maxPrice) {
                throwException(SysErrorCode::SYS_PARAMS_ERROR, '智能体价格超过限制');
            }
        } else {
            $price = 0; // 免费智能体价格为0
        }

        // 检查是否需要重新审核
        $needReaudit = $this->checkNeedReaudit($agent, $params);

        // 更新智能体信息
        $agent->category_guid = $params['categoryGuid'];
        $agent->agent_name = $params['agentName'];
        $agent->agent_desc = $params['agentDesc'] ?? '';
        $agent->agent_avatar = $params['agentAvatar'] ?? '';
        $agent->agent_type = $params['agentType'];
        $agent->agent_config = $params['agentConfig'] ?? [];
        $agent->prompt_content = $params['promptContent'] ?? '';
        $agent->welcome_message = $params['welcomeMessage'] ?? '';
        $agent->common_questions = $params['commonQuestions'] ?? [];
        $agent->knowledge_base_ids = $params['knowledgeBaseIds'] ?? [];
        $agent->is_paid = $isPaid;
        $agent->price = $price;
        $agent->is_public = $params['isPublic'] ?? $agent->is_public;
        $agent->trialChatCount = $params['trial_chat_count'] ?? 3;

        // 如果修改了关键信息，需要重新审核
        if ($needReaudit) {
            $agent->audit_status = UserAiAgentModel::AUDIT_STATUS_PENDING;
            $agent->audit_time = 0;
            $agent->audit_remark = '';
        }

        if (!$agent->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '更新智能体失败');
        }

        return [
            'agentGuid' => $agent->guid,
            'auditStatus' => $agent->audit_status,
            'auditStatusText' => UserAiAgentModel::AUDIT_STATUS_TEXT[$agent->audit_status] ?? '',
            'message' => $needReaudit ? '智能体更新成功，需要重新审核' : '智能体更新成功'
        ];
    }

    /**
     * 删除我的智能体
     * @param array $params
     * @return array
     */
    public function delete(array $params): array
    {
        $agentGuid = $params['agentGuid'];
        $merchantGuid = $params['merchantGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 查询智能体信息，验证所有权
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $agentGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '智能体不存在或您没有权限删除');
        }

        // 检查智能体是否有关联的订阅记录
        $subscriptionCount = UserAiAgentSubscriptionModel::getInstance()
            ->where('agent_guid', $agentGuid)
            ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
            ->count();

        if ($subscriptionCount > 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '该智能体已有用户订阅，无法删除');
        }

        // 检查智能体是否有关联的聊天记录
        $chatSessionCount = UserAiAgentChatSessionModel::getInstance()
            ->where('agent_guid', $agentGuid)
            ->count();

        if ($chatSessionCount > 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '该智能体已有聊天记录，无法删除');
        }

        // 执行软删除
        $agent->deletedAt = time();
        $agent->updateTime = time();

        if (!$agent->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '删除智能体失败');
        }

        return [
            'agentGuid' => $agentGuid,
            'message' => '智能体删除成功'
        ];
    }

    /**
     * 检查是否需要重新审核
     * @param UserAiAgentModel $agent
     * @param array $params
     * @return bool
     */
    private function checkNeedReaudit(UserAiAgentModel $agent, array $params): bool
    {
        // 如果当前状态不是审核通过，则不需要重新审核
        if ($agent->audit_status != UserAiAgentModel::AUDIT_STATUS_APPROVED) {
            return false;
        }

        // 检查关键字段是否有变化
        $keyFields = [
            'agent_name' => $params['agentName'],
            'prompt_content' => $params['promptContent'],
            'agent_type' => $params['agentType'],
            'is_paid' => $params['isPaid'] ?? $agent->is_paid,
            'welcome_message' => $params['welcomeMessage'] ?? '',
        ];

        foreach ($keyFields as $field => $newValue) {
            if ($agent->$field != $newValue) {
                return true; // 关键字段有变化，需要重新审核
            }
        }

        // 检查价格变化（付费智能体）
        if ($agent->is_paid && isset($params['price'])) {
            $newPrice = (int)($params['price'] * 100);
            if ($agent->price != $newPrice) {
                return true;
            }
        }

        // 检查常见问题变化
        if (isset($params['commonQuestions'])) {
            $newQuestions = json_encode($params['commonQuestions']);
            $oldQuestions = json_encode($agent->common_questions ?? []);
            if ($newQuestions != $oldQuestions) {
                return true;
            }
        }

        return false; // 没有关键变化，不需要重新审核
    }

    /**
     * AI生成智能体头像
     * @param array $params
     * @return array
     */
    public function generateAvatar(array $params): array
    {
        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 获取用户信息
        $userInfo = UsersModel::getInstance()->where('sys_id', $platformUserSysId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 验证必填参数
        $agentName = $params['agentName'] ?? '';
        $agentDesc = $params['agentDesc'] ?? '';

        if (empty($agentName)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '请提供智能体名称');
        }
        if (empty($agentDesc)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '请提供智能体描述');
        }

        // 记录开始生成日志
        LogInfo('ai_agent_avatar', 'AI头像生成开始', '开始为智能体生成头像', [
            'userId' => $platformUserSysId,
            'agentName' => $agentName,
            'agentDesc' => $agentDesc
        ]);

        try {
            // 1. 使用PromptBuildService生成头像设计提示词
            $promptService = PromptBuildService::getInstance();
            $designPrompt = $promptService->aiAgentAvatarDesign($agentName, $agentDesc);

            // 2. 使用AzureAiTool的dall3ImgBuild生成头像
            $imageUrl = AzureAiTool::getInstance()->dall3ImgBuild($designPrompt, '1024x1024');

            // 记录成功日志
            LogInfo('ai_agent_avatar', 'AI头像生成成功', '智能体头像生成完成', [
                'userId' => $platformUserSysId,
                'agentName' => $agentName,
                'imageUrl' => $imageUrl,
                'finalPrompt' => $designPrompt
            ]);

            return [
                'success' => true,
                'message' => '智能体头像生成成功',
                'data' => [
                    'imageUrl' => $imageUrl,
                    'agentName' => $agentName,
                    'agentDesc' => $agentDesc,
                    'prompt' => $designPrompt,
                    'generateTime' => date('Y-m-d H:i:s')
                ]
            ];
        } catch (\Exception $e) {
            // 记录错误日志
            LogError('ai_agent_avatar', 'AI头像生成失败', $e->getMessage(), [
                'userId' => $platformUserSysId,
                'agentName' => $agentName,
                'agentDesc' => $agentDesc,
                'params' => $params
            ]);

            throwException(SysErrorCode::SYS_ERROR_CODE, 'AI头像生成失败：' . $e->getMessage());
        }
    }

    /**
     * 智能体详情（基本信息，不包含敏感数据）
     * @param array $params
     * @return array
     */
    public function agentDetail(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $agentGuid = $params['agentGuid'] ?? '';
        $agentSysId = $params['agentSysId'] ?? 0;
        if (empty($agentGuid) && empty($agentSysId)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '请提供智能体GUID或SYS_ID');
        }

        // 从token中获取用户ID（可能为空，支持未登录用户查看）
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId ?? 0;

        // 查询智能体基本信息
        $agent = UserAiAgentModel::getInstance()
            ->where(function ($query) use ($agentGuid, $agentSysId) {
                if (!empty($agentGuid)) {
                    $query->where('guid', $agentGuid);
                }
                if (!empty($agentSysId)) {
                    $query->where('sys_id', $agentSysId);
                }
            })
            ->where('merchant_guid', $merchantGuid)
            ->where('status', UserAiAgentModel::STATUS_NORMAL)
            ->where('audit_status', UserAiAgentModel::AUDIT_STATUS_APPROVED)
            ->with(['category'])
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '智能体不存在或未审核通过');
        }

        // 检查是否为公开智能体（如果是私有的，只有创建者可以查看）
        if (!$agent->isPublic && $agent->platformUserSysId != $platformUserSysId) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '该智能体为私有，无权限查看');
        }

        // 获取创建者信息
        $creator = UsersModel::getInstance()
            ->where('sys_id', $agent->platformUserSysId)
            ->findOrEmpty();

        // 统计订阅数量
        $subscribeCount = UserAiAgentSubscriptionModel::getInstance()
            ->where('agent_guid', $agent->guid)
            ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
            ->count();

        // 检查当前用户是否已订阅
        $isSubscribed = false;
        if ($platformUserSysId > 0) {
            $subscription = UserAiAgentSubscriptionModel::getInstance()
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('agent_guid', $agent->guid)
                ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
                ->findOrEmpty();
            $isSubscribed = !$subscription->isEmpty();
        }

        // 增加浏览次数
        UserAiAgentModel::getInstance()
            ->where('guid', $agentGuid)
            ->inc('view_count')
            ->update();

        // 处理价格显示
        $priceYuan = fen_to_yuan($agent->price);
        $priceText = $agent->isPaid ? '￥' . $priceYuan : '免费';
        $priceDisplay = $agent->isPaid ? $priceYuan . '元' : '免费使用';

        // 返回基本信息（不包含敏感数据）
        return [
            'sysId' => $agent->sysId,
            'guid' => $agent->guid,
            'agentName' => $agent->agentName,
            'agentDesc' => $agent->agentDesc,
            'agentAvatar' => $agent->agentAvatar,
            'agentType' => $agent->agentType,
            'agentTypeText' => UserAiAgentModel::AGENT_TYPE_TEXT[$agent->agentType] ?? '',
            'welcomeMessage' => $agent->welcomeMessage,
            'commonQuestions' => $agent->commonQuestions ?? [],
            'isPaid' => $agent->isPaid,
            'isPaidText' => $agent->isPaid ? '付费' : '免费',
            'price' => $agent->price,                    // 原始价格（分）
            'priceYuan' => $priceYuan,                   // 价格（元）
            'priceText' => $priceText,                   // 格式化价格文本
            'priceDisplay' => $priceDisplay,             // 价格显示
            'isPublic' => $agent->isPublic,
            'isPublicText' => $agent->isPublic ? '公开' : '私有',
            'viewCount' => $agent->viewCount + 1,       // 包含本次浏览
            'subscribeCount' => $subscribeCount,
            'isSubscribed' => $isSubscribed,
            'createTime' => date('Y-m-d H:i:s', $agent->createTime),
            'updateTime' => date('Y-m-d H:i:s', $agent->updateTime),

            // 分类信息
            'category' => [
                'guid' => $agent->category->guid ?? '',
                'categoryName' => $agent->category->categoryName ?? '',
                'categoryDesc' => $agent->category->categoryDesc ?? '',
                'categoryIcon' => $agent->category->categoryIcon ?? '',
            ],

            // 创建者信息（脱敏）
            'creator' => [
                'userId' => $creator->sys_id ?? 0,
                'nickname' => $creator->nickname ?? '匿名用户',
                'avatar' => $creator->head_imgurl ?? '',
                'isCreator' => $platformUserSysId > 0 && $agent->platformUserSysId == $platformUserSysId,
            ],

            // 注意：不返回以下敏感信息
            // - promptContent（提示词内容）
            // - agentConfig（智能体配置）
            // - knowledgeBaseIds（知识库ID）
            // - platformUserSysId（创建者用户ID）
            // - merchantGuid（商户GUID）
        ];
    }

    /**
     * 绑定邀请关系
     * @param array $params
     * @return array
     */
    public function bindInvitation(array $params): array
    {
        LogInfo('ai_agent_invitation', '邀请关系绑定开始', '开始绑定邀请关系', [
            'params' => $params,
        ]);

        $merchantGuid = $params['merchantGuid'];
        $invitationCode = trim($params['invitationCode']);

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 验证邀请码格式
        if (empty($invitationCode) || strlen($invitationCode) < 6 || strlen($invitationCode) > 20) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '邀请码格式不正确');
        }

        // 检查用户是否已被邀请过
        if (UserAiAgentInvitationModel::isUserInvited($platformUserSysId)) {
            $existingInvitation = UserAiAgentInvitationModel::getUserInvitation($platformUserSysId);
            return [
                'success' => false,
                'message' => '您已被其他用户邀请，不能重复绑定',
                'existingInviter' => [
                    'nickname' => $existingInvitation->inviter->nickname ?? '未知用户',
                    'bindTime' => date('Y-m-d H:i:s', $existingInvitation->bindTime),
                ]
            ];
        }

        // 判断当前上级是否本身就是自己的下级，禁止循环绑定
        $inviter = UserAiAgentInvitationModel::findInviterByCode($invitationCode);
        $existChild = UserAiAgentInvitationModel::getInstance()
            ->where('inviter_user_id', $platformUserSysId)
            ->where('invitee_user_id', $inviter->sysId)
            ->findOrEmpty();
        if (!$existChild->isEmpty()) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '不能循环绑定');
        }

        // 根据邀请码查找邀请人
        $inviter = UserAiAgentInvitationModel::findInviterByCode($invitationCode);
        if (!$inviter) {
            throwException(SysErrorCode::QUERY_EMPTY, '邀请码不存在或已失效');
        }

        // 不能邀请自己
        if ($inviter->sysId == $platformUserSysId) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '不能使用自己的邀请码');
        }

        // 检查发起的用户是否购买过智能体会员
        $hasPurchasedMembership = $this->checkUserMembershipPurchaseHistory($inviter->sysId, $merchantGuid);
        if (!$hasPurchasedMembership) {
            return [
                'success' => false,
                'msg' => '只有购买过智能体会员的用户才能绑定邀请关系'
            ];
            //throwException(SysErrorCode::SYS_ERROR_CODE, '只有购买过智能体会员的用户才能绑定邀请关系');
        }

        // 开启事务
        Db::startTrans();
        try {
            // 创建邀请关系
            $invitation = new UserAiAgentInvitationModel();
            $invitation->guid = get_guid();
            $invitation->merchantGuid = $merchantGuid;
            $invitation->inviterUserId = $inviter->sysId;
            $invitation->inviteeUserId = $platformUserSysId;
            $invitation->invitationCode = $invitationCode;
            $invitation->invitationType = UserAiAgentInvitationModel::INVITATION_TYPE_CODE;
            $invitation->invitationSource = '';
            $invitation->bindTime = time();
            $invitation->createTime = time();
            $invitation->updateTime = time();

            if (!$invitation->save()) {
                throw new \Exception('创建邀请关系失败');
            }

            // 提交事务
            Db::commit();

            LogInfo('ai_agent_invitation', '邀请关系绑定成功', '用户成功绑定邀请关系', [
                'inviteeUserId' => $platformUserSysId,
                'inviterUserId' => $inviter->sys_id,
                'invitationCode' => $invitationCode,
                'invitationGuid' => $invitation->guid,
                'merchantGuid' => $merchantGuid
            ]);

            return [
                'success' => true,
                'message' => '邀请关系绑定成功',
                'invitationGuid' => $invitation->guid,
                'inviterInfo' => [
                    'userId' => $inviter->sys_id,
                    'nickname' => $inviter->nickname,
                    'avatar' => $inviter->head_imgurl,
                ],
                'bindTime' => date('Y-m-d H:i:s', $invitation->bindTime),
                'invitationType' => UserAiAgentInvitationModel::INVITATION_TYPE_TEXT[$invitation->invitationType] ?? '',
            ];
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            LogError('ai_agent_invitation', '邀请关系绑定失败', $e->getMessage(), [
                'inviteeUserId' => $platformUserSysId,
                'invitationCode' => $invitationCode,
                'error' => $e->getMessage()
            ]);

            throwException(SysErrorCode::SYS_ERROR_CODE, '绑定邀请关系失败：' . $e->getMessage());
        }
    }

    /**
     * 生成智能体小程序码
     * @param array $params
     * @return array
     */
    public function generateMiniCode(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $miniPath = $params['miniPath'];
        $pathQuery = $params['pathQuery'] ?? '';
        $width = 600; // 固定宽度

        // 验证参数
        if (empty($miniPath)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '小程序路径不能为空');
        }

        try {
            // 构建完整的小程序路径（包含参数）
            $fullPath = $miniPath;
            if (!empty($pathQuery)) {
                $fullPath .= '?' . $pathQuery;
            }

            // 生成小程序码文件名（基于路径的MD5）
            $fileName = 'ai_agent_code_' . md5($fullPath) . '.png';
            $savePath = public_path('/ai_agent_codes');

            // 检查目录是否存在
            if (!is_dir($savePath)) {
                mkdir($savePath, 0777, true);
            }

            $fullFilePath = $savePath . '/' . $fileName;

            // 检查文件是否已存在
            if (!file_exists($fullFilePath)) {
                // 获取微信小程序实例
                $app = WechatService::getInstance()->getMiniProgramApp($merchantGuid);

                // 生成小程序码
                $response = $app->app_code->get($fullPath);

                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    // 保存到本地
                    $response->saveAs($savePath, $fileName);
                } else {
                    throwException(SysErrorCode::SYS_ERROR_CODE, '小程序码生成失败');
                }
            }

            // 返回本地URL
            $miniCodeUrl = getPageUrl() . '/ai_agent_codes/' . $fileName;

            LogInfo('ai_agent_mini_code', '小程序码生成成功', '小程序码生成', [
                'miniPath' => $miniPath,
                'pathQuery' => $pathQuery,
                'fullPath' => $fullPath,
                'fileName' => $fileName,
                'miniCodeUrl' => $miniCodeUrl
            ]);

            return [
                'miniCodeUrl' => $miniCodeUrl,
                'codeInfo' => [
                    'miniPath' => $miniPath,
                    'pathQuery' => $pathQuery,
                    'fullPath' => $fullPath,
                    'fileName' => $fileName,
                ],
                'generateTime' => date('Y-m-d H:i:s'),
                'message' => '小程序码生成成功'
            ];
        } catch (\Exception $e) {
            LogError('ai_agent_mini_code', '小程序码生成失败', $e->getMessage(), [
                'miniPath' => $miniPath,
                'pathQuery' => $pathQuery,
                'error' => $e->getMessage()
            ]);

            throwException(SysErrorCode::SYS_ERROR_CODE, '小程序码生成失败：' . $e->getMessage());
        }
    }

    /**
     * 检查用户是否有智能体会员购买历史
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @return bool
     */
    private function checkUserMembershipPurchaseHistory(int $platformUserSysId, string $merchantGuid): bool
    {
        // 检查是否有已支付的会员订单
        $paidOrderCount = UserAiAgentMembershipOrderModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('order_status', UserAiAgentMembershipOrderModel::ORDER_STATUS_PAID)
            ->count();

        return $paidOrderCount > 0;
    }
}
