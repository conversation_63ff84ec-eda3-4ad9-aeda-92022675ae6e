<?php

declare(strict_types=1);

namespace app\useragent\logic\api;

use app\libraries\service\ai_open\ai_person_video\lmp\ChanjingAiPersonVideo;
use app\useragent\models\MerchantAiAgentConfigModel;
use app\useragent\models\UserChanjingDigitalPersonModel;
use app\useragent\cache\ChanjingDigitalHumanCache;
use app\user\models\UserAssetsModel;
use app\user\models\UserAssetsChangeRecordModel;
use app\user\models\UserVideoOrderModel;
use app\user\models\UserWorksRecordModel;
use app\user\models\UsersModel;
use app\constdir\SysErrorCode;
use app\libraries\service\token\TokenService;
use think\facade\Db;

/**
 * 蝉镜数字人业务逻辑类
 */
class ChanjingDigitalHumanLogic
{
    /**
     * 获取公共形象列表
     * @param array $params
     * @return array
     */
    public function getPersonList(array $params): array
    {
        $merchantGuid = $params['merchantGuid'] ?? '';
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['pageSize'] ?? 20);

        // 参数验证
        if ($page < 1) {
            $page = 1;
        }
        if ($pageSize < 1 || $pageSize > 100) {
            $pageSize = 20;
        }

        try {
            // 初始化蝉镜客户端
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            // 调用蝉镜API获取形象列表
            $result = $chanjing->getPersonList($page, $pageSize);

            // 格式化返回数据
            return $this->formatPersonListResponse($result);

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 格式化公共形象列表响应数据
     * @param array $data
     * @return array
     */
    private function formatPersonListResponse(array $data): array
    {
        $list = [];

        if (!empty($data['list'])) {
            foreach ($data['list'] as $item) {
                // 格式化figures数组
                $figures = [];
                if (!empty($item['figures'])) {
                    foreach ($item['figures'] as $figure) {
                        $figures[] = [
                            'picPath' => $figure['pic_path'] ?? '',
                            'type' => $figure['type'] ?? '',
                            'cover' => $figure['cover'] ?? '',
                            'width' => (int)($figure['width'] ?? 0),
                            'height' => (int)($figure['height'] ?? 0),
                            'previewVideoUrl' => $figure['preview_video_url'] ?? '',
                        ];
                    }
                }

                $list[] = [
                    'id' => $item['id'] ?? '',
                    'name' => $item['name'] ?? '',
                    'figures' => $figures,
                    'gender' => $item['gender'] ?? '',
                    'width' => (int)($item['width'] ?? 0),
                    'height' => (int)($item['height'] ?? 0),
                    'audioName' => $item['audio_name'] ?? '',
                    'audioManId' => $item['audio_man_id'] ?? '',
                    'audioPreview' => $item['audio_preview'] ?? '',
                ];
            }
        }

        $pageInfo = $data['page_info'] ?? [];

        return [
            'list' => $list,
            'pageInfo' => [
                'page' => (int)($pageInfo['page'] ?? 1),
                'pageSize' => (int)($pageInfo['size'] ?? 20),
                'totalCount' => (int)($pageInfo['total_count'] ?? 0),
                'totalPage' => (int)($pageInfo['total_page'] ?? 1),
            ],
        ];
    }

    /**
     * 格式化定制形象列表响应数据
     * @param array $data
     * @return array
     */
    private function formatCustomPersonListResponse(array $data): array
    {
        $list = [];

        if (!empty($data['list'])) {
            foreach ($data['list'] as $item) {
                $list[] = [
                    'id' => $item['id'] ?? '',
                    'name' => $item['name'] ?? '',
                    'type' => $item['type'] ?? 'person',
                    'picUrl' => $item['pic_url'] ?? '',
                    'previewUrl' => $item['preview_url'] ?? '',
                    'width' => (int)($item['width'] ?? 0),
                    'height' => (int)($item['height'] ?? 0),
                    'audioManId' => $item['audio_man_id'] ?? '',
                    'status' => (int)($item['status'] ?? 0),
                    'statusText' => $this->getStatusText((int)($item['status'] ?? 0)),
                    'errReason' => $item['err_reason'] ?? '',
                    'isOpen' => (int)($item['is_open'] ?? 0),
                    'isAvailable' => (int)($item['is_open'] ?? 0) === 1 && (int)($item['status'] ?? 0) === 2,
                    'reason' => $item['reason'] ?? '',
                    'progress' => (int)($item['progress'] ?? 0),
                ];
            }
        }

        $pageInfo = $data['page_info'] ?? [];

        return [
            'list' => $list,
            'pageInfo' => [
                'page' => (int)($pageInfo['page'] ?? 1),
                'pageSize' => (int)($pageInfo['size'] ?? 20),
                'totalCount' => (int)($pageInfo['total_count'] ?? 0),
                'totalPage' => (int)($pageInfo['total_page'] ?? 1),
            ],
        ];
    }

    /**
     * 获取定制形象列表
     * @param array $params
     * @return array
     */
    public function getCustomPersonList(array $params): array
    {
        $merchantGuid = $params['merchantGuid'] ?? '';
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['pageSize'] ?? 20);

        // 参数验证
        if ($page < 1) {
            $page = 1;
        }
        if ($pageSize < 1 || $pageSize > 100) {
            $pageSize = 20;
        }

        try {
            // 初始化蝉镜客户端
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            // 调用蝉镜API获取定制形象列表
            $result = $chanjing->getCustomPersonList($page, $pageSize);

            // 格式化返回数据
            return $this->formatCustomPersonListResponse($result);

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 创建视频合成任务
     * @param array $params
     * @return array
     */
    public function createVideo(array $params): array
    {
        $merchantGuid = $params['merchantGuid'] ?? '';
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        // // 获取缓存锁，防止重复提交
        // $lock = ChanjingDigitalHumanCache::getInstance()->videoCreateLock($platformUserSysId);
        // if ($lock === false) {
        //     throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        // }

        try {
            // 1. 计算所需算力点数
            $requiredPoints = $this->calculateRequiredPoints($params, $merchantGuid);

            // 2. 校验用户算力是否充足
            $this->validateUserPoints($platformUserSysId, $requiredPoints);

            // 3. 扣除用户算力点数
            $this->deductUserPoints($platformUserSysId, $requiredPoints, $params);

            // 4. 初始化蝉镜客户端
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            // 5. 构建请求参数
            $requestData = $this->buildCreateVideoParams($params);

            // 6. 创建数字人视频订单
            $orderNo = $this->createVideoOrder($platformUserSysId, $merchantGuid, $params, $requiredPoints);

            // 7. 调用蝉镜API创建视频
            $result = $chanjing->createVideo($requestData);

            // 8. 更新订单的视频ID
            $this->updateOrderVideoId($orderNo, $result);

            return [
                'orderNo' => $orderNo,
                'videoId' => $result,
                'message' => '视频合成任务创建成功',
                'consumedPoints' => $requiredPoints,
            ];

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放锁
            // ChanjingDigitalHumanCache::getInstance()->unLockVideoCreate($platformUserSysId, $lock);
        }
    }

    /**
     * 获取视频详情
     * @param array $params
     * @return array
     */
    public function getVideoDetail(array $params): array
    {
        $merchantGuid = $params['merchantGuid'] ?? '';
        $orderNo = $params['orderNo'] ?? '';

        try {
            // 1. 根据订单号获取本地订单信息
            $localOrder = $this->getLocalOrderByNo($orderNo);
            if (empty($localOrder)) {
                throwException(SysErrorCode::QUERY_EMPTY, '订单不存在');
            }

            // 2. 初始化蝉镜客户端
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            // 3. 调用蝉镜API获取最新视频详情
            $result = $chanjing->getVideoDetail($localOrder['videoId']);

            // 4. 更新本地订单状态
            $this->updateLocalOrderStatus($orderNo, $result);

            // 5. 格式化返回数据
            $formattedResult = $this->formatVideoDetailResponse($result);

            // 6. 添加本地订单信息
            $formattedResult['orderNo'] = $orderNo;
            $formattedResult['payPoint'] = $localOrder['payPoint'];
            $formattedResult['textContent'] = $localOrder['textContent'];
            $formattedResult['audioType'] = $localOrder['audioType'];
            $formattedResult['orderStatus'] = $localOrder['orderStatus'];
            $formattedResult['orderCreateTime'] = $localOrder['createTime'];
            $formattedResult['orderCreateTimeText'] = date('Y-m-d H:i:s', $localOrder['createTime']);

            // 7. 添加完整的作品参数
            if (!empty($localOrder['workParams'])) {
                $formattedResult['workParams'] = json_decode($localOrder['workParams'], true);
            }

            return $formattedResult;

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 我的数字人作品列表
     * @param array $params
     * @return array
     */
    public function getMyWorks(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['pageSize'] ?? 10);

        // 参数验证
        if ($page < 1) {
            $page = 1;
        }
        if ($pageSize < 1 || $pageSize > 100) {
            $pageSize = 10;
        }

        try {
            // 1. 获取用户的数字人作品列表
            $worksList = $this->getUserDigitalHumanWorks($platformUserSysId, $page, $pageSize);

            // 2. 更新未完成作品的状态
            $this->updatePendingWorksStatus($worksList['data'], $merchantGuid);

            // 3. 重新获取更新后的作品列表
            $updatedWorksList = $this->getUserDigitalHumanWorks($platformUserSysId, $page, $pageSize);

            // 4. 格式化返回数据
            return $this->formatMyWorksResponse($updatedWorksList);

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 构建创建视频的请求参数
     * @param array $params
     * @return array
     */
    private function buildCreateVideoParams(array $params): array
    {
        $requestData = [
            'person' => [
                'id' => $params['personId'],
                'x' => (int)($params['personX'] ?? 0),
                'y' => (int)($params['personY'] ?? 480),
                'width' => (int)($params['personWidth'] ?? 1080),
                'height' => (int)($params['personHeight'] ?? 1440),
            ],
            'audio' => [
                'type' => $params['audioType'],
                'volume' => (int)($params['volume'] ?? 100),
                'language' => $params['language'] ?? 'cn',
            ],
            'screen_width' => (int)($params['screenWidth'] ?? 1080),
            'screen_height' => (int)($params['screenHeight'] ?? 1920),
        ];

        // 添加可选的figure_type参数（公共数字人需要）
        if (!empty($params['figureType'])) {
            $requestData['person']['figure_type'] = $params['figureType'];
        }

        // 添加可选的驱动模式
        if (!empty($params['driveMode'])) {
            $requestData['person']['drive_mode'] = $params['driveMode'];
        }

        // 添加可选的RGBA模式
        if (isset($params['isRgbaMode'])) {
            $requestData['person']['is_rgba_mode'] = (bool)$params['isRgbaMode'];
        }

        // 添加可选的播放顺序
        if (!empty($params['backway'])) {
            $requestData['person']['backway'] = (int)$params['backway'];
        }

        // 根据音频类型添加相应参数
        if ($params['audioType'] === 'tts') {
            $requestData['audio']['tts'] = [
                'text' => [$params['text']],
                'speed' => (float)($params['speed'] ?? 1.0),
                'audio_man' => $params['audioManId'],
            ];

            // 添加可选的音调参数
            if (!empty($params['pitch'])) {
                $requestData['audio']['tts']['pitch'] = (float)$params['pitch'];
            }
        } elseif ($params['audioType'] === 'audio') {
            $requestData['audio']['wav_url'] = $params['wavUrl'];
        }

        // 添加背景颜色
        if (!empty($params['bgColor'])) {
            $requestData['bg_color'] = $params['bgColor'];
        }

        // 添加背景图片
        if (!empty($params['bgSrcUrl'])) {
            $requestData['bg'] = [
                'src_url' => $params['bgSrcUrl'],
                'x' => (int)($params['bgX'] ?? 0),
                'y' => (int)($params['bgY'] ?? 0),
                'width' => (int)($params['bgWidth'] ?? 1080),
                'height' => (int)($params['bgHeight'] ?? 1920),
            ];
        }

        // 添加字幕配置
        if (isset($params['subtitleShow'])) {
            $requestData['subtitle_config'] = [
                'show' => (bool)$params['subtitleShow'],
                'x' => (int)($params['subtitleX'] ?? 31),
                'y' => (int)($params['subtitleY'] ?? 1521),
                'width' => (int)($params['subtitleWidth'] ?? 1000),
                'height' => (int)($params['subtitleHeight'] ?? 200),
                'font_size' => (int)($params['subtitleFontSize'] ?? 64),
            ];

            // 添加可选的字幕样式参数
            if (!empty($params['subtitleColor'])) {
                $requestData['subtitle_config']['color'] = $params['subtitleColor'];
            }
            if (!empty($params['subtitleStrokeColor'])) {
                $requestData['subtitle_config']['stroke_color'] = $params['subtitleStrokeColor'];
            }
            if (!empty($params['subtitleStrokeWidth'])) {
                $requestData['subtitle_config']['stroke_width'] = (int)$params['subtitleStrokeWidth'];
            }
            if (!empty($params['subtitleFontId'])) {
                $requestData['subtitle_config']['font_id'] = $params['subtitleFontId'];
            }
        }

        // 添加回调地址
        if (!empty($params['callback'])) {
            $requestData['callback'] = $params['callback'];
        }

        return $requestData;
    }
    /**
     * 格式化视频详情响应数据
     * @param array $data
     * @return array
     */
    private function formatVideoDetailResponse(array $data): array
    {
        return [
            'id' => $data['id'] ?? '',
            'status' => (int)($data['status'] ?? 0),
            'statusText' => $this->getVideoStatusText((int)($data['status'] ?? 0)),
            'progress' => (int)($data['progress'] ?? 0),
            'msg' => $data['msg'] ?? '',
            'videoUrl' => $data['video_url'] ?? '',
            'subtitleDataUrl' => $data['subtitle_data_url'] ?? '',
            'createTime' => (int)($data['create_time'] ?? 0),
            'createTimeText' => !empty($data['create_time']) ? date('Y-m-d H:i:s', $data['create_time']) : '',
            'previewUrl' => $data['preview_url'] ?? '',
            'duration' => (int)($data['duration'] ?? 0),
            'audioUrls' => $data['audio_urls'] ?? [],
        ];
    }

    /**
     * 获取视频状态文本
     * @param int $status
     * @return string
     */
    private function getVideoStatusText(int $status): string
    {
        $statusTexts = [
            10 => '生成中',
            30 => '成功',
            40 => '参数异常',
            41 => '参数异常',
            42 => '参数异常',
            43 => '参数异常',
            44 => '参数异常',
            45 => '参数异常',
            46 => '参数异常',
            47 => '参数异常',
            48 => '参数异常',
            49 => '参数异常',
            50 => '服务异常',
            51 => '服务异常',
            52 => '服务异常',
            53 => '服务异常',
            54 => '服务异常',
            55 => '服务异常',
            56 => '服务异常',
            57 => '服务异常',
            58 => '服务异常',
            59 => '服务异常',
        ];

        return $statusTexts[$status] ?? '未知状态';
    }

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    private function getStatusText(int $status): string
    {
        $statusTexts = [
            1 => '制作中',
            2 => '成功',
            4 => '失败',
            5 => '系统错误',
        ];

        return $statusTexts[$status] ?? '未知状态';
    }

    /**
     * 计算所需算力点数
     * @param array $params
     * @param string $merchantGuid
     * @return int
     */
    private function calculateRequiredPoints(array $params, string $merchantGuid): int
    {
        // 获取商户配置的数字人每秒收费点数
        $pointsPerSecond = MerchantAiAgentConfigModel::getShuzirenPointsPerSecond($merchantGuid);

        // 计算视频时长（秒）
        $videoSeconds = $this->calculateVideoSeconds($params);

        // 计算所需总点数
        $requiredPoints = $videoSeconds * $pointsPerSecond;

        return max(1, $requiredPoints); // 最少消耗1个点数
    }

    /**
     * 计算视频时长（秒）
     * @param array $params
     * @return int
     */
    private function calculateVideoSeconds(array $params): int
    {
        if ($params['audioType'] === 'tts') {
            // TTS模式：按1秒4个字计算
            $text = $params['text'] ?? '';
            $textLength = mb_strlen($text, 'UTF-8');
            $seconds = ceil($textLength / 4); // 向上取整
            return (int) max(1, $seconds); // 最少1秒
        } else {
            // 音频文件模式：暂时按60秒计算，后续可以通过音频文件分析获取实际时长
            return 60;
        }
    }

    /**
     * 校验用户算力是否充足
     * @param int $platformUserSysId
     * @param int $requiredPoints
     * @throws \Exception
     */
    private function validateUserPoints(int $platformUserSysId, int $requiredPoints): void
    {
        $userAssets = UserAssetsModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($userAssets->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户资产信息不存在');
        }

        $currentPoints = (int)$userAssets->chat_count;
        if ($currentPoints < $requiredPoints) {
            throwException(SysErrorCode::SYS_ERROR_CODE, "算力点数不足，当前：{$currentPoints}点，需要：{$requiredPoints}点");
        }
    }

    /**
     * 扣除用户算力点数
     * @param int $platformUserSysId
     * @param int $requiredPoints
     * @param array $params
     * @throws \Exception
     */
    private function deductUserPoints(int $platformUserSysId, int $requiredPoints, array $params): void
    {
        Db::startTrans();
        try {
            // 扣除算力点数
            $affected = UserAssetsModel::getInstance()
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('chat_count', '>=', $requiredPoints) // 再次确认余额充足
                ->dec('chat_count', $requiredPoints)
                ->update();

            if ($affected === 0) {
                throw new \Exception('算力点数不足或扣除失败');
            }

            // 记录消费记录
            $this->recordPointsConsumption($platformUserSysId, $requiredPoints, $params);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 记录算力点数消费记录
     * @param int $platformUserSysId
     * @param int $points
     * @param array $params
     */
    private function recordPointsConsumption(int $platformUserSysId, int $points, array $params): void
    {
        $record = new UserAssetsChangeRecordModel();
        $record->platform_user_sys_id = $platformUserSysId;
        $record->change_type = UserAssetsChangeRecordModel::CHANGE_TYPE_REDUCE;
        $record->change_amount = $points; // 负数表示消费
        $record->change_reason = '数字人视频创作消费';
        $record->related_id = ''; // 可以在视频创建成功后更新为视频ID
        $record->create_time = time();

        // 添加详细信息
        $details = [
            'audioType' => $params['audioType'] ?? '',
            'textLength' => isset($params['text']) ? mb_strlen($params['text'], 'UTF-8') : 0,
            'estimatedSeconds' => $this->calculateVideoSeconds($params),
            'pointsPerSecond' => $points / max(1, $this->calculateVideoSeconds($params)),
        ];
        $record->change_details = json_encode($details, JSON_UNESCAPED_UNICODE);

        $record->save();
    }

    /**
     * 创建数字人视频订单
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param array $params
     * @param int $requiredPoints
     * @return string
     */
    private function createVideoOrder(int $platformUserSysId, string $merchantGuid, array $params, int $requiredPoints): string
    {
        // 获取用户信息
        $user = UsersModel::getInstance()
            ->where('sys_id', $platformUserSysId)
            ->field('sys_id,nickname,head_imgurl')
            ->findOrEmpty();

        if ($user->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '用户不存在');
        }

        // 生成订单号
        $orderNo = order_no('CJ');

        // 创建视频订单记录
        $videoOrder = new UserVideoOrderModel();
        $videoOrder->platform_user_sys_id = $platformUserSysId;
        $videoOrder->merchant_guid = $merchantGuid;
        $videoOrder->order_no = $orderNo;
        $videoOrder->order_status = UserVideoOrderModel::STATUS_WAIT;
        $videoOrder->video_type = 'chanjing_digital_human'; // 蝉镜数字人
        $videoOrder->pay_point = $requiredPoints;
        $videoOrder->video_id = ''; // 稍后更新
        $videoOrder->video_url = '';
        $videoOrder->video_result = '';
        $videoOrder->create_time = time();
        $videoOrder->update_time = time();
        $videoOrder->save();

        // 创建作品记录
        $worksRecord = new UserWorksRecordModel();
        $worksRecord->platform_user_sys_id = $platformUserSysId;
        $worksRecord->merchant_guid = $merchantGuid;
        $worksRecord->order_no = $orderNo;
        $worksRecord->work_type = 'chanjing_digital_human';
        $worksRecord->work_title = $this->generateWorkTitle($params);
        $worksRecord->work_params = $this->formatWorkParams($params);
        $worksRecord->work_status = UserWorksRecordModel::STATUS_WAIT;
        $worksRecord->work_result = '';
        $worksRecord->create_time = time();
        $worksRecord->update_time = time();
        $worksRecord->save();

        return $orderNo;
    }

    /**
     * 更新订单的视频ID
     * @param string $orderNo
     * @param string $videoId
     */
    private function updateOrderVideoId(string $orderNo, string $videoId): void
    {
        UserVideoOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->update([
                'video_id' => $videoId,
                'order_status' => UserVideoOrderModel::STATUS_DOING,
                'update_time' => time(),
            ]);

        UserWorksRecordModel::getInstance()
            ->where('order_no', $orderNo)
            ->update([
                'work_status' => UserWorksRecordModel::STATUS_DOING,
                'update_time' => time(),
            ]);
    }

    /**
     * 根据订单号获取本地订单信息
     * @param string $orderNo
     * @return array
     */
    private function getLocalOrderByNo(string $orderNo): array
    {
        $order = UserVideoOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if ($order->isEmpty()) {
            return [];
        }

        $orderData = $order->toArray();

        // 获取作品记录
        $worksRecord = UserWorksRecordModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if (!$worksRecord->isEmpty()) {
            $orderData['textContent'] = $this->extractTextFromWorkParams($worksRecord->work_params);
            $orderData['audioType'] = $this->extractAudioTypeFromWorkParams($worksRecord->work_params);
            $orderData['workParams'] = $worksRecord->work_params;
        }

        return $orderData;
    }

    /**
     * 更新本地订单状态
     * @param string $orderNo
     * @param array $videoResult
     */
    private function updateLocalOrderStatus(string $orderNo, array $videoResult): void
    {
        $status = $videoResult['status'] ?? 0;
        $orderStatus = UserVideoOrderModel::STATUS_DOING;
        $workStatus = UserWorksRecordModel::STATUS_DOING;

        // 根据蝉镜状态映射本地状态
        if ($status == 30) { // 成功
            $orderStatus = UserVideoOrderModel::STATUS_SUCCESS;
            $workStatus = UserWorksRecordModel::STATUS_SUCCESS;
        } elseif ($status >= 40) { // 失败
            $orderStatus = UserVideoOrderModel::STATUS_FAIL;
            $workStatus = UserWorksRecordModel::STATUS_FAIL;
        }

        // 更新视频订单
        $updateData = [
            'order_status' => $orderStatus,
            'video_result' => json_encode($videoResult, JSON_UNESCAPED_UNICODE),
            'update_time' => time(),
        ];

        if (!empty($videoResult['video_url'])) {
            $updateData['video_url'] = $videoResult['video_url'];
        }

        UserVideoOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->update($updateData);

        // 更新作品记录
        UserWorksRecordModel::getInstance()
            ->where('order_no', $orderNo)
            ->update([
                'work_status' => $workStatus,
                'work_result' => json_encode($videoResult, JSON_UNESCAPED_UNICODE),
                'update_time' => time(),
            ]);
    }
    /**
     * 生成作品标题
     * @param array $params
     * @return string
     */
    private function generateWorkTitle(array $params): string
    {
        if ($params['audioType'] === 'tts') {
            $text = $params['text'] ?? '';
            $title = mb_substr($text, 0, 20, 'UTF-8');
            if (mb_strlen($text, 'UTF-8') > 20) {
                $title .= '...';
            }
            return $title ?: '数字人视频创作';
        } else {
            return '数字人音频视频创作';
        }
    }

    /**
     * 格式化作品参数
     * @param array $params
     * @return string
     */
    private function formatWorkParams(array $params): string
    {
        // 保存完整的请求参数
        $workParams = [
            // 基础参数
            'audioType' => $params['audioType'],
            'personId' => $params['personId'],

            // 数字人位置配置
            'personX' => $params['personX'] ?? 0,
            'personY' => $params['personY'] ?? 480,
            'personWidth' => $params['personWidth'] ?? 1080,
            'personHeight' => $params['personHeight'] ?? 1440,

            // 屏幕配置
            'screenWidth' => $params['screenWidth'] ?? 1080,
            'screenHeight' => $params['screenHeight'] ?? 1920,

            // 音频配置
            'volume' => $params['volume'] ?? 100,
            'language' => $params['language'] ?? 'cn',
        ];

        // TTS模式参数
        if ($params['audioType'] === 'tts') {
            $workParams['text'] = $params['text'] ?? '';
            $workParams['audioManId'] = $params['audioManId'] ?? '';
            $workParams['speed'] = $params['speed'] ?? 1.0;
            if (!empty($params['pitch'])) {
                $workParams['pitch'] = $params['pitch'];
            }
        } else {
            // 音频文件模式参数
            $workParams['wavUrl'] = $params['wavUrl'] ?? '';
        }

        // 可选参数
        if (!empty($params['figureType'])) {
            $workParams['figureType'] = $params['figureType'];
        }
        if (!empty($params['driveMode'])) {
            $workParams['driveMode'] = $params['driveMode'];
        }
        if (isset($params['isRgbaMode'])) {
            $workParams['isRgbaMode'] = $params['isRgbaMode'];
        }
        if (!empty($params['backway'])) {
            $workParams['backway'] = $params['backway'];
        }
        if (!empty($params['bgColor'])) {
            $workParams['bgColor'] = $params['bgColor'];
        }
        if (!empty($params['bgSrcUrl'])) {
            $workParams['bgSrcUrl'] = $params['bgSrcUrl'];
            $workParams['bgX'] = $params['bgX'] ?? 0;
            $workParams['bgY'] = $params['bgY'] ?? 0;
            $workParams['bgWidth'] = $params['bgWidth'] ?? 1080;
            $workParams['bgHeight'] = $params['bgHeight'] ?? 1920;
        }
        if (isset($params['subtitleShow'])) {
            $workParams['subtitleShow'] = $params['subtitleShow'];
            $workParams['subtitleX'] = $params['subtitleX'] ?? 31;
            $workParams['subtitleY'] = $params['subtitleY'] ?? 1521;
            $workParams['subtitleWidth'] = $params['subtitleWidth'] ?? 1000;
            $workParams['subtitleHeight'] = $params['subtitleHeight'] ?? 200;
            $workParams['subtitleFontSize'] = $params['subtitleFontSize'] ?? 64;
            if (!empty($params['subtitleColor'])) {
                $workParams['subtitleColor'] = $params['subtitleColor'];
            }
            if (!empty($params['subtitleStrokeColor'])) {
                $workParams['subtitleStrokeColor'] = $params['subtitleStrokeColor'];
            }
            if (!empty($params['subtitleStrokeWidth'])) {
                $workParams['subtitleStrokeWidth'] = $params['subtitleStrokeWidth'];
            }
            if (!empty($params['subtitleFontId'])) {
                $workParams['subtitleFontId'] = $params['subtitleFontId'];
            }
        }
        if (!empty($params['callback'])) {
            $workParams['callback'] = $params['callback'];
        }

        return json_encode($workParams, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 从作品参数中提取文本
     * @param string $workParams
     * @return string
     */
    private function extractTextFromWorkParams(string $workParams): string
    {
        $params = json_decode($workParams, true);
        return $params['text'] ?? '';
    }

    /**
     * 从作品参数中提取音频类型
     * @param string $workParams
     * @return string
     */
    private function extractAudioTypeFromWorkParams(string $workParams): string
    {
        $params = json_decode($workParams, true);
        return $params['audioType'] ?? 'tts';
    }

    /**
     * 获取用户的数字人作品列表
     * @param int $platformUserSysId
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    private function getUserDigitalHumanWorks(int $platformUserSysId, int $page, int $pageSize): array
    {
        $result = UserVideoOrderModel::getInstance()
            ->alias('vo')
            ->leftJoin('user_works_record wr', 'vo.order_no = wr.order_no')
            ->where('vo.platform_user_sys_id', $platformUserSysId)
            ->where('vo.video_type', 'chanjing_digital_human')
            ->where('vo.order_status', '<>', UserVideoOrderModel::STATUS_FAIL)
            ->field([
                'vo.*',
                'wr.work_title',
                'wr.work_params',
                'wr.work_status',
                'wr.work_result'
            ])
            ->order('vo.create_time', 'desc')
            ->paginate($pageSize, false, ['page' => $page])
            ->toArray();

        return $result;
    }

    /**
     * 更新未完成作品的状态
     * @param array $worksList
     * @param string $merchantGuid
     */
    private function updatePendingWorksStatus(array $worksList, string $merchantGuid): void
    {
        if (empty($worksList)) {
            return;
        }

        // 筛选出未完成的作品
        $pendingWorks = array_filter($worksList, function($work) {
            return in_array($work['orderStatus'], [
                UserVideoOrderModel::STATUS_WAIT,
                UserVideoOrderModel::STATUS_DOING
            ]) && !empty($work['videoId']);
        });

        if (empty($pendingWorks)) {
            return;
        }

        // 初始化蝉镜客户端
        try {
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            foreach ($pendingWorks as $work) {
                try {
                    // 查询蝉镜API获取最新状态
                    $videoResult = $chanjing->getVideoDetail($work['videoId']);

                    // 更新本地状态
                    $this->updateLocalOrderStatus($work['orderNo'], $videoResult);

                    // 避免请求过于频繁
                    usleep(100000); // 0.1秒延迟

                } catch (\Exception $e) {
                    // 单个作品更新失败不影响其他作品
                    continue;
                }
            }
        } catch (\Exception $e) {
            // 蝉镜客户端初始化失败，跳过状态更新
            return;
        }
    }

    /**
     * 格式化我的作品响应数据
     * @param array $worksList
     * @return array
     */
    private function formatMyWorksResponse(array $worksList): array
    {
        $formattedList = [];
        foreach ($worksList['data'] as $work) {
            $workParams = !empty($work['workParams']) ? json_decode($work['workParams'], true) : [];
            $videoResult = !empty($work['videoResult']) ? json_decode($work['videoResult'], true) : [];
            $formattedWork = [
                'orderNo' => $work['orderNo'],
                'videoId' => $work['videoId'],
                'workTitle' => $work['workTitle'] ?: '数字人视频作品',
                'orderStatus' => $work['orderStatus'],
                'orderStatusText' => $this->getOrderStatusText($work['orderStatus']),
                'workStatus' => $work['workStatus'],
                'workStatusText' => $this->getWorkStatusText($work['workStatus']),
                'payPoint' => (int)$work['payPoint'],
                'createTime' => $work['createTime'],
                'createTimeText' => date('Y-m-d H:i:s', $work['createTime']),
                'updateTime' => $work['updateTime'],
                'updateTimeText' => date('Y-m-d H:i:s', $work['updateTime']),

                // 视频结果信息
                'videoUrl' => $work['videoUrl'] ?: ($videoResult['video_url'] ?? ''),
                'previewUrl' => $videoResult['preview_url'] ?? '',
                'duration' => (int)($videoResult['duration'] ?? 0),
                'progress' => (int)($videoResult['progress'] ?? 0),
                'errorMsg' => $videoResult['msg'] ?? '',
            ];

            $formattedList[] = $formattedWork;
        }

        return [
            'list' => $formattedList,
            'pageInfo' => [
                'page' => (int)$worksList['current_page'],
                'pageSize' => (int)$worksList['per_page'],
                'totalCount' => (int)$worksList['total'],
                'totalPage' => (int)$worksList['last_page'],
            ],
        ];
    }

    /**
     * 获取订单状态文本
     * @param string $status
     * @return string
     */
    private function getOrderStatusText(string $status): string
    {
        $statusTexts = [
            UserVideoOrderModel::STATUS_WAIT => '等待处理',
            UserVideoOrderModel::STATUS_DOING => '处理中',
            UserVideoOrderModel::STATUS_SUCCESS => '处理成功',
            UserVideoOrderModel::STATUS_FAIL => '处理失败',
        ];

        return $statusTexts[$status] ?? '未知状态';
    }

    /**
     * 获取作品状态文本
     * @param string $status
     * @return string
     */
    private function getWorkStatusText(string $status): string
    {
        $statusTexts = [
            UserWorksRecordModel::STATUS_WAIT => '等待处理',
            UserWorksRecordModel::STATUS_DOING => '处理中',
            UserWorksRecordModel::STATUS_SUCCESS => '处理成功',
            UserWorksRecordModel::STATUS_FAIL => '处理失败',
        ];

        return $statusTexts[$status] ?? '未知状态';
    }

    /**
     * 获取我的数字人形象列表
     * @param array $params
     * @return array
     */
    public function getMyPersonList(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['pageSize'] ?? 20);

        // 参数验证
        if ($page < 1) {
            $page = 1;
        }
        if ($pageSize < 1 || $pageSize > 100) {
            $pageSize = 20;
        }

        try {
            // 获取用户的数字人形象列表
            $personList = UserChanjingDigitalPersonModel::getUserPersonList($platformUserSysId, $merchantGuid, $page, $pageSize);

            // 更新未完成形象的状态
            $this->updatePendingPersonStatus($personList['data'], $merchantGuid);

            // 重新获取更新后的形象列表
            $updatedPersonList = UserChanjingDigitalPersonModel::getUserPersonList($platformUserSysId, $merchantGuid, $page, $pageSize);

            // 格式化返回数据
            return $this->formatMyPersonListResponse($updatedPersonList);

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 创建数字人形象
     * @param array $params
     * @return array
     */
    public function createPerson(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $personName = $params['personName'] ?? '';
        $materialVideoUrl = $params['materialVideoUrl'] ?? '';
        $trainType = $params['trainType'] ?? UserChanjingDigitalPersonModel::TRAIN_TYPE_BOTH;
        $language = $params['language'] ?? UserChanjingDigitalPersonModel::LANGUAGE_CN;
        $callback = $params['callback'] ?? '';

        try {
            // 1. 初始化蝉镜客户端
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            // 2. 调用蝉镜API创建定制数字人
            $chanjingPersonId = $chanjing->createCustomPerson($personName, $materialVideoUrl, $callback, $trainType, $language);

            // 3. 保存到本地数据库
            $personGuid = $this->savePersonToLocal($platformUserSysId, $merchantGuid, $personName, $chanjingPersonId, $materialVideoUrl, $trainType, $language);

            return [
                'personGuid' => $personGuid,
                'chanjingPersonId' => $chanjingPersonId,
                'message' => '数字人形象创建成功，正在制作中...',
            ];

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 删除数字人形象
     * @param array $params
     * @return array
     */
    public function deletePerson(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $personGuid = $params['personGuid'] ?? '';

        try {
            // 1. 获取数字人形象记录
            $person = UserChanjingDigitalPersonModel::getByGuid($personGuid, $platformUserSysId, $merchantGuid);
            if (!$person) {
                throwException(SysErrorCode::QUERY_EMPTY, '数字人形象不存在');
            }

            // 2. 软删除本地记录
            $person->deleted_at = time();
            $person->update_time = time();
            $person->save();

            return [
                'message' => '数字人形象删除成功',
            ];

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 计算数字人视频创作所需算力
     * @param array $params
     * @return array
     */
    public function calculatePoints(array $params): array
    {
        $merchantGuid = $params['merchantGuid'] ?? '';
        $audioType = $params['audioType'] ?? 'tts';

        try {
            // 1. 获取商户配置的数字人每秒收费点数
            $pointsPerSecond = MerchantAiAgentConfigModel::getShuzirenPointsPerSecond($merchantGuid);

            // 2. 计算视频时长（秒）
            $videoSeconds = $this->calculateVideoSeconds($params);

            // 3. 计算所需总点数
            $requiredPoints = max(1, $videoSeconds * $pointsPerSecond);

            // 4. 获取用户当前算力余额（如果已登录）
            $currentPoints = 0;
            $isLoggedIn = false;
            try {
                $tokenEntity = TokenService::getInstance()->getTokenEntity();
                if ($tokenEntity && $tokenEntity->userId > 0) {
                    $isLoggedIn = true;
                    $platformUserSysId = $tokenEntity->userId;
                    $userAssets = UserAssetsModel::getInstance()
                        ->where('platform_user_sys_id', $platformUserSysId)
                        ->findOrEmpty();
                    $currentPoints = $userAssets->isEmpty() ? 0 : (int)$userAssets->chat_count;
                }
            } catch (\Exception $e) {
                // 未登录状态，忽略错误
                $isLoggedIn = false;
            }

            // 5. 计算详细信息
            $calculationDetails = [
                'audioType' => $audioType,
                'audioTypeText' => $audioType === 'tts' ? 'TTS语音合成' : '音频文件',
                'videoSeconds' => $videoSeconds,
                'pointsPerSecond' => $pointsPerSecond,
                'requiredPoints' => $requiredPoints,
                'isLoggedIn' => $isLoggedIn,
            ];

            // 只有登录用户才显示余额和充足性信息
            if ($isLoggedIn) {
                $calculationDetails['currentPoints'] = $currentPoints;
                $calculationDetails['isEnough'] = $currentPoints >= $requiredPoints;
                $calculationDetails['shortfall'] = max(0, $requiredPoints - $currentPoints);
            }

            // 6. 添加文本相关信息（TTS模式）
            if ($audioType === 'tts' && !empty($params['text'])) {
                $text = $params['text'];
                $textLength = mb_strlen($text, 'UTF-8');
                $calculationDetails['text'] = $text;
                $calculationDetails['textLength'] = $textLength;
                $calculationDetails['calculationRule'] = '按1秒4个字计算';
                $calculationDetails['calculationFormula'] = "视频时长 = ceil({$textLength} ÷ 4) = {$videoSeconds}秒";
            } else {
                $calculationDetails['calculationRule'] = '音频文件模式固定时长';
                $calculationDetails['calculationFormula'] = "视频时长 = {$videoSeconds}秒（固定）";
            }

            $calculationDetails['pointsCalculationFormula'] = "{$videoSeconds}秒 × {$pointsPerSecond}点/秒 = {$requiredPoints}点";

            // 6. 生成提示消息
            $message = "预计消耗 {$requiredPoints} 点算力";
            if ($isLoggedIn) {
                if ($calculationDetails['isEnough']) {
                    $message = "算力充足，预计消耗 {$requiredPoints} 点，可以开始创作";
                } else {
                    $message = "算力不足，需要 {$requiredPoints} 点，还差 {$calculationDetails['shortfall']} 点";
                }
            } else {
                $message .= "，请登录查看余额详情";
            }

            return [
                'calculation' => $calculationDetails,
                'message' => $message,
            ];

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 获取数字人算力配置信息
     * @param array $params
     * @return array
     */
    public function getPointsConfig(array $params): array
    {
        $merchantGuid = $params['merchantGuid'] ?? '';

        try {
            // 1. 获取商户配置的数字人每秒收费点数
            $pointsPerSecond = MerchantAiAgentConfigModel::getShuzirenPointsPerSecond($merchantGuid);

            // 2. 获取用户当前算力余额（如果已登录）
            $currentPoints = 0;
            $isLoggedIn = false;
            try {
                $tokenEntity = TokenService::getInstance()->getTokenEntity();
                if ($tokenEntity && $tokenEntity->userId > 0) {
                    $isLoggedIn = true;
                    $platformUserSysId = $tokenEntity->userId;
                    $userAssets = UserAssetsModel::getInstance()
                        ->where('platform_user_sys_id', $platformUserSysId)
                        ->findOrEmpty();
                    $currentPoints = $userAssets->isEmpty() ? 0 : (int)$userAssets->chat_count;
                }
            } catch (\Exception $e) {
                // 未登录状态，忽略错误
                $isLoggedIn = false;
            }

            // 3. 构建配置信息
            $config = [
                'pointsPerSecond' => $pointsPerSecond,
                'ttsCalculationRule' => '按1秒4个字计算',
                'audioCalculationRule' => '音频文件模式固定60秒',
                'minPoints' => 1,
                'isLoggedIn' => $isLoggedIn,
            ];

            // 只有登录用户才显示余额信息
            if ($isLoggedIn) {
                $config['currentPoints'] = $currentPoints;
            }

            // 4. 添加示例计算
            $examples = [
                [
                    'type' => 'tts',
                    'description' => 'TTS语音合成（20字文本）',
                    'textLength' => 20,
                    'videoSeconds' => ceil(20 / 4),
                    'requiredPoints' => max(1, ceil(20 / 4) * $pointsPerSecond),
                ],
                [
                    'type' => 'tts',
                    'description' => 'TTS语音合成（100字文本）',
                    'textLength' => 100,
                    'videoSeconds' => ceil(100 / 4),
                    'requiredPoints' => max(1, ceil(100 / 4) * $pointsPerSecond),
                ],
                [
                    'type' => 'audio',
                    'description' => '音频文件模式',
                    'textLength' => 0,
                    'videoSeconds' => 60,
                    'requiredPoints' => max(1, 60 * $pointsPerSecond),
                ],
            ];

            $config['examples'] = $examples;

            return [
                'config' => $config,
                'message' => '算力配置获取成功',
            ];

        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 批量删除数字人作品
     * @param array $params
     * @return array
     */
    public function batchDeleteWorks(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $orderNos = $params['orderNos'] ?? [];

        if (empty($orderNos) || !is_array($orderNos)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '订单编号列表不能为空');
        }

        // 限制批量删除数量，防止一次删除过多
        if (count($orderNos) > 50) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '单次最多删除50个作品');
        }

        try {
            Db::startTrans();

            $deletedCount = 0;
            $failedOrders = [];
            $currentTime = time();

            foreach ($orderNos as $orderNo) {
                try {
                    // 1. 验证订单是否属于当前用户
                    $videoOrder = UserVideoOrderModel::getInstance()
                        ->where('order_no', $orderNo)
                        ->where('platform_user_sys_id', $platformUserSysId)
                        ->where('merchant_guid', $merchantGuid)
                        ->where('video_type', 'chanjing_digital_human')
                        ->where('deleted_at', 0) // 确保未被删除
                        ->findOrEmpty();

                    if ($videoOrder->isEmpty()) {
                        $failedOrders[] = [
                            'orderNo' => $orderNo,
                            'reason' => '作品不存在或无权限删除'
                        ];
                        continue;
                    }

                    // 2. 软删除视频订单记录
                    $videoOrder->deleted_at = $currentTime;
                    $videoOrder->update_time = $currentTime;
                    $videoOrder->save();

                    // 3. 软删除对应的作品记录
                    UserWorksRecordModel::getInstance()
                        ->where('order_no', $orderNo)
                        ->where('platform_user_sys_id', $platformUserSysId)
                        ->where('merchant_guid', $merchantGuid)
                        ->where('work_type', 'chanjing_digital_human')
                        ->where('deleted_at', 0)
                        ->update([
                            'deleted_at' => $currentTime,
                            'update_time' => $currentTime,
                        ]);

                    $deletedCount++;

                } catch (\Exception $e) {
                    $failedOrders[] = [
                        'orderNo' => $orderNo,
                        'reason' => '删除失败：' . $e->getMessage()
                    ];
                }
            }

            Db::commit();

            // 4. 构建返回结果
            $result = [
                'totalCount' => count($orderNos),
                'deletedCount' => $deletedCount,
                'failedCount' => count($failedOrders),
                'message' => "批量删除完成，成功删除 {$deletedCount} 个作品",
            ];

            // 如果有失败的记录，添加详细信息
            if (!empty($failedOrders)) {
                $result['failedOrders'] = $failedOrders;
                $result['message'] .= "，{$result['failedCount']} 个作品删除失败";
            }

            return $result;

        } catch (\Exception $e) {
            Db::rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, '批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 删除单个数字人作品
     * @param array $params
     * @return array
     */
    public function deleteWork(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $orderNo = $params['orderNo'] ?? '';

        if (empty($orderNo)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '订单编号不能为空');
        }

        try {
            Db::startTrans();

            // 1. 验证订单是否属于当前用户
            $videoOrder = UserVideoOrderModel::getInstance()
                ->where('order_no', $orderNo)
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('merchant_guid', $merchantGuid)
                ->where('video_type', 'chanjing_digital_human')
                ->where('deleted_at', 0) // 确保未被删除
                ->findOrEmpty();

            if ($videoOrder->isEmpty()) {
                throwException(SysErrorCode::QUERY_EMPTY, '作品不存在或无权限删除');
            }

            $currentTime = time();

            // 2. 软删除视频订单记录
            $videoOrder->deleted_at = $currentTime;
            $videoOrder->update_time = $currentTime;
            $videoOrder->save();

            // 3. 软删除对应的作品记录
            UserWorksRecordModel::getInstance()
                ->where('order_no', $orderNo)
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('merchant_guid', $merchantGuid)
                ->where('work_type', 'chanjing_digital_human')
                ->where('deleted_at', 0)
                ->update([
                    'deleted_at' => $currentTime,
                    'update_time' => $currentTime,
                ]);

            Db::commit();

            return [
                'orderNo' => $orderNo,
                'message' => '数字人作品删除成功',
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, '删除作品失败：' . $e->getMessage());
        }
    }

    /**
     * 恢复已删除的数字人作品
     * @param array $params
     * @return array
     */
    public function restoreWork(array $params): array
    {
        $platformUserSysId = TokenService::getInstance()->getTokenEntity()->userId;
        $merchantGuid = $params['merchantGuid'] ?? '';
        $orderNo = $params['orderNo'] ?? '';

        if (empty($orderNo)) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '订单编号不能为空');
        }

        try {
            Db::startTrans();

            // 1. 验证订单是否属于当前用户且已被软删除
            $videoOrder = UserVideoOrderModel::getInstance()
                ->where('order_no', $orderNo)
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('merchant_guid', $merchantGuid)
                ->where('video_type', 'chanjing_digital_human')
                ->where('deleted_at', '>', 0) // 确保已被软删除
                ->findOrEmpty();

            if ($videoOrder->isEmpty()) {
                throwException(SysErrorCode::QUERY_EMPTY, '作品不存在、无权限操作或未被删除');
            }

            $currentTime = time();

            // 2. 恢复视频订单记录
            $videoOrder->deleted_at = 0;
            $videoOrder->update_time = $currentTime;
            $videoOrder->save();

            // 3. 恢复对应的作品记录
            UserWorksRecordModel::getInstance()
                ->where('order_no', $orderNo)
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('merchant_guid', $merchantGuid)
                ->where('work_type', 'chanjing_digital_human')
                ->where('deleted_at', '>', 0)
                ->update([
                    'deleted_at' => 0,
                    'update_time' => $currentTime,
                ]);

            Db::commit();

            return [
                'orderNo' => $orderNo,
                'message' => '数字人作品恢复成功',
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, '恢复作品失败：' . $e->getMessage());
        }
    }

    /**
     * 保存数字人形象到本地数据库
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param string $personName
     * @param string $chanjingPersonId
     * @param string $materialVideoUrl
     * @param string $trainType
     * @param string $language
     * @return string
     */
    private function savePersonToLocal(int $platformUserSysId, string $merchantGuid, string $personName, string $chanjingPersonId, string $materialVideoUrl, string $trainType, string $language): string
    {
        $person = new UserChanjingDigitalPersonModel();
        $person->guid = get_guid();
        $person->merchantGuid = $merchantGuid;
        $person->platformUserSysId = $platformUserSysId;
        $person->personName = $personName;
        $person->chanjingPersonId = $chanjingPersonId;
        $person->materialVideoUrl = $materialVideoUrl;
        $person->trainType = $trainType;
        $person->language = $language;
        $person->status = UserChanjingDigitalPersonModel::STATUS_CREATING;
        $person->progress = 0;
        $person->isOpen = 0;
        $person->createTime = time();
        $person->updateTime = time();
        $person->save();

        return $person->guid;
    }

    /**
     * 更新未完成形象的状态
     * @param array $personList
     * @param string $merchantGuid
     */
    private function updatePendingPersonStatus(array $personList, string $merchantGuid): void
    {
        if (empty($personList)) {
            return;
        }

        // 筛选出未完成的形象
        $pendingPersons = array_filter($personList, function($person) {
            return in_array($person['status'], [
                UserChanjingDigitalPersonModel::STATUS_CREATING
            ]) && !empty($person['chanjingPersonId']);
        });

        if (empty($pendingPersons)) {
            return;
        }

        // 初始化蝉镜客户端
        try {
            $chanjing = new ChanjingAiPersonVideo($merchantGuid);

            foreach ($pendingPersons as $person) {
                try {
                    // 查询蝉镜API获取最新状态
                    $personResult = $chanjing->getCustomPersonDetail($person['chanjingPersonId']);

                    // 更新本地状态
                    UserChanjingDigitalPersonModel::updatePersonStatus($person['chanjingPersonId'], $personResult);

                    // 避免请求过于频繁
                    usleep(100000); // 0.1秒延迟

                } catch (\Exception $e) {
                    // 单个形象更新失败不影响其他形象
                    continue;
                }
            }
        } catch (\Exception $e) {
            // 蝉镜客户端初始化失败，跳过状态更新
            return;
        }
    }

    /**
     * 格式化我的数字人形象列表响应数据
     * @param array $personList
     * @return array
     */
    private function formatMyPersonListResponse(array $personList): array
    {
        $formattedList = [];
        foreach ($personList['data'] as $person) {
            $formattedPerson = [
                'id' => $person['sysId'],
                'personGuid' => $person['guid'],
                'personName' => $person['personName'],
                'chanjingPersonId' => $person['chanjingPersonId'],
                'materialVideoUrl' => $person['materialVideoUrl'],
                'trainType' => $person['trainType'],
                'trainTypeText' => UserChanjingDigitalPersonModel::getTrainTypeText($person['trainType']),
                'language' => $person['language'],
                'languageText' => UserChanjingDigitalPersonModel::getLanguageText($person['language']),
                'status' => $person['status'],
                'statusText' => UserChanjingDigitalPersonModel::getStatusText($person['status']),
                'progress' => (int)$person['progress'],
                'picUrl' => $person['picUrl'] ?: '',
                'previewUrl' => $person['previewUrl'] ?: '',
                'width' => (int)$person['width'],
                'height' => (int)$person['height'],
                'audioManId' => $person['audioManId'] ?: '',
                'errReason' => $person['errReason'] ?: '',
                'isOpen' => (int)$person['isOpen'],
                'reason' => $person['reason'] ?: '',
                'isAvailable' => $person['status'] == UserChanjingDigitalPersonModel::STATUS_SUCCESS && $person['isOpen'] == 1,
                'createTime' => $person['createTime'],
                'createTimeText' => date('Y-m-d H:i:s', $person['createTime']),
                'updateTime' => $person['updateTime'],
                'updateTimeText' => date('Y-m-d H:i:s', $person['updateTime']),
            ];

            $formattedList[] = $formattedPerson;
        }

        return [
            'list' => $formattedList,
            'pageInfo' => [
                'page' => (int)$personList['current_page'],
                'pageSize' => (int)$personList['per_page'],
                'totalCount' => (int)$personList['total'],
                'totalPage' => (int)$personList['last_page'],
            ],
        ];
    }
}
