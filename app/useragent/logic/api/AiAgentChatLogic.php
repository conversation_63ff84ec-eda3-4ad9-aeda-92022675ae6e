<?php

declare(strict_types=1);

namespace app\useragent\logic\api;

use app\user\models\UserSecretKeyModel;
use app\useragent\models\UserAiAgentChatSessionModel;
use app\useragent\models\UserAiAgentModel;
use app\useragent\models\UserAiAgentChatMessageModel;
use app\useragent\models\UserAiAgentSubscriptionModel;
use app\useragent\models\UserAiAgentMembershipSubscriptionModel;
use app\useragent\models\UserAiAgentCreatorSubscriptionModel;
use app\useragent\models\UserAiAgentCategorySubscriptionModel;
use app\useragent\models\UserAiAgentMessageCollectionModel;
use app\useragent\cache\AiAgentChatCache;
use app\user\models\UsersModel;
use app\libraries\service\token\TokenService;
use app\libraries\service\ai_open\ai_chat\AiChatFactory;
use app\libraries\service\ai_open\ai_agent\AiAgentFactory;
use app\libraries\service\ai_open\ai_chat\entity\ChatCompleteEntity;
use app\libraries\service\ai_open\ai_agent\entity\AgentCompleteEntity;
use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use think\facade\Db;

/**
 * 智能体对话API业务逻辑类
 */
class AiAgentChatLogic
{
    protected $agentCache;

    // 智能体聊天特殊状态码
    const AGENT_NOT_SUBSCRIBED = 40001;      // 未订阅智能体
    const AGENT_SUBSCRIPTION_EXPIRED = 40002; // 订阅已过期
    const AGENT_TRIAL_EXHAUSTED = 40003;     // 试用次数已用完
    const AGENT_NOT_ACTIVATED = 40004;       // 未激活，需要购买

    public function __construct()
    {
        $this->agentCache = new AiAgentChatCache();
    }
    /**
     * 我的智能体对话列表
     * @param array $params
     * @return array
     */
    public function mySessionList(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 获取用户的对话轮次列表
        $query = UserAiAgentChatSessionModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('session_status', 'in', [
                UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE,
                UserAiAgentChatSessionModel::SESSION_STATUS_ENDED
            ]);

        // 按智能体筛选
        if (!empty($params['agentGuid'])) {
            $query->where('agent_guid', $params['agentGuid']);
        }

        $result = $query->with(['agent'])
            ->order('is_top', 'desc')        // 置顶的在前
            ->order('top_time', 'desc')      // 置顶时间倒序
            ->order('last_message_time', 'desc')
            ->order('create_time', 'desc')
            ->paginate($params['pageSize'] ?? 20)
            ->toArray();

        // 处理返回数据
        foreach ($result['data'] as &$session) {
            // 获取最后一条消息
            $lastMessage = UserAiAgentChatMessageModel::getInstance()
                ->where('session_guid', $session['guid'])
                ->order('message_index', 'desc')
                ->findOrEmpty();

            // 获取未读消息数量（智能体回复的消息，这里简化处理，实际可以根据业务需求调整）
            $unreadCount = 0;
            if (!$lastMessage->isEmpty() && $lastMessage->message_type == UserAiAgentChatMessageModel::MESSAGE_TYPE_AGENT_REPLY) {
                $unreadCount = 1; // 简化处理，如果最后一条是智能体回复则显示1个未读
            }

            $session['sessionStatusText'] = UserAiAgentChatSessionModel::SESSION_STATUS_TEXT[$session['sessionStatus']] ?? '';
            $session['isTop'] = $session['isTop'] ?? 0;
            $session['isTopText'] = UserAiAgentChatSessionModel::TOP_TEXT[$session['isTop']] ?? '否';
            $session['topTime'] = $session['topTime'] ? date('Y-m-d H:i:s', $session['topTime']) : '';
            $session['createTime'] = date('Y-m-d H:i:s', $session['createTime']);
            $session['lastMessageTime'] = $session['lastMessageTime'] ? date('Y-m-d H:i:s', $session['lastMessageTime']) : '';

            // 智能体信息
            $session['agent'] = [
                'guid' => $session['agent']['guid'] ?? '',
                'agentName' => $session['agent']['agentName'] ?? '未知智能体',
                'agentAvatar' => $session['agent']['agentAvatar'] ?? '',
                'agentType' => $session['agent']['agentType'] ?? 1,
            ];

            // 最后一条消息信息
            $lastMessageContent = '';
            if (!$lastMessage->isEmpty()) {
                if ($lastMessage->content_type == 'text') {
                    $lastMessageContent = mb_substr($lastMessage->content, 0, 50) . (mb_strlen($lastMessage->content) > 50 ? '...' : '');
                } else {
                    $lastMessageContent = '[' . (UserAiAgentChatMessageModel::CONTENT_TYPE_TEXT_MAP[$lastMessage->content_type] ?? '消息') . ']';
                }
            }

            $session['lastMessage'] = [
                'content' => $lastMessageContent,
                'contentType' => $lastMessage->content_type ?? 'text',
                'messageType' => $lastMessage->message_type ?? 0,
                'messageTypeText' => UserAiAgentChatMessageModel::MESSAGE_TYPE_TEXT[$lastMessage->message_type ?? 0] ?? '',
                'createTime' => $lastMessage->create_time ? date('Y-m-d H:i:s', $lastMessage->create_time) : '',
            ];

            // 未读消息数量
            $session['unreadCount'] = $unreadCount;

            // 格式化显示时间（相对时间）
            $session['displayTime'] = $this->formatDisplayTime($session['lastMessageTime'] ?: $session['createTime']);
        }

        return $result;
    }

    /**
     * 聊天消息历史
     * @param array $params
     * @return array
     */
    public function messageHistory(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $sessionGuid = $params['sessionGuid'];
        $page = $params['page'] ?? 1;
        $pageSize = $params['pageSize'] ?? 20;
        $isAll = $params['isAll'] ?? 0;

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 验证对话轮次是否属于当前用户，并关联智能体信息
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->with(['agent' => function ($query) {
                $query->field('guid,agent_name,agent_avatar,agent_desc,agent_type,common_questions');
            }])
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '对话轮次不存在或无权限访问');
        }

        // 构建查询条件
        $query = UserAiAgentChatMessageModel::getInstance()
            ->where('session_guid', $sessionGuid)
            ->where('merchant_guid', $merchantGuid);

        // 如果是获取全部消息，不分页
        if ($isAll) {
            $messages = $query->order('sys_id', 'asc')
                ->select()
                ->toArray();
        } else {
            // 使用page分页，计算offset
            $offset = ($page - 1) * $pageSize;

            // 获取消息列表（按sys_id正序，保证分页的一致性）
            $messages = $query->order('sys_id', 'desc')
                ->limit($offset, $pageSize)
                ->select()
                ->toArray();
        }

        // 获取用户对这些消息的收藏状态
        $messageGuids = array_column($messages, 'guid');
        $collectedMessages = [];
        if (!empty($messageGuids)) {
            $collections = UserAiAgentMessageCollectionModel::getInstance()
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('merchant_guid', $merchantGuid)
                ->whereIn('message_guid', $messageGuids)
                ->column('message_guid');
            $collectedMessages = array_flip($collections);
        }

        // 处理返回数据，参考square模块的格式
        $result = [];
        foreach ($messages as $message) {
            $result[] = [
                'sysId' => $message['sysId'],
                'guid' => $message['guid'],
                'chatRole' => $this->getChatRole($message['messageType']),
                'contentType' => $message['contentType'],
                'chatContent' => $message['content'],
                'msgId' => $message['guid'], // 使用guid作为msgId
                'lastMsgId' => $message['parentMessageGuid'],
                'sendDay' => (int)date('Ymd', $message['createTime']),
                'platformUserSysId' => $message['platformUserSysId'],
                'sessionGuid' => $message['sessionGuid'],
                'agentGuid' => $message['agentGuid'],
                'imgUrls' => $this->getImageUrls($message['fileUrls'], $message['contentType']),
                'videoUrls' => $this->getVideoUrls($message['fileUrls'], $message['contentType']),
                'messageType' => $message['messageType'],
                'messageTypeText' => UserAiAgentChatMessageModel::MESSAGE_TYPE_TEXT[$message['messageType']] ?? '',
                'messageIndex' => $message['messageIndex'],
                'tokensUsed' => $message['tokensUsed'],
                'processingTime' => $message['processingTime'],
                'errorInfo' => $message['errorInfo'],
                'isCollected' => isset($collectedMessages[$message['guid']]), // 新增：是否已收藏
                'createTime' => $message['createTime'],
                'updateTime' => $message['updateTime'],
                'createTimeText' => date('Y-m-d H:i:s', $message['createTime']),
                'updateTimeText' => date('Y-m-d H:i:s', $message['updateTime']),
            ];
        }

        // 计算分页信息
        $totalMessages = $session->message_count;
        $totalPages = $isAll ? 1 : ceil($totalMessages / $pageSize);
        $hasMore = !$isAll && ($page < $totalPages);
        $nextPage = $hasMore ? ($page + 1) : 0;

        // 为了保持向后兼容，计算nextStartId（基于当前页最后一条消息的sysId）
        $nextStartId = 0;
        if (!$isAll && !empty($messages)) {
            $lastMessage = end($messages);
            $nextStartId = $lastMessage['sysId'];
        }

        return [
            'list' => $result,
            'hasMore' => $hasMore,
            'nextStartId' => $nextStartId, // 保持原有字段，向后兼容
            'total' => $totalMessages,
            'page' => $page, // 新增页码信息
            'pageSize' => $pageSize,
            'totalPages' => $totalPages,
            'nextPage' => $nextPage,
            'sessionInfo' => [
                'guid' => $session->guid,
                'sessionTitle' => $session->session_title,
                'sessionStatus' => $session->session_status,
                'messageCount' => $session->message_count,
                'tokensUsed' => $session->tokens_used,
                'createTime' => date('Y-m-d H:i:s', $session->create_time),
                'lastMessageTime' => $session->last_message_time ? date('Y-m-d H:i:s', $session->last_message_time) : '',

                // 智能体信息
                'agentInfo' => [
                    'guid' => $session->agent->guid ?? '',
                    'agentName' => $session->agent->agentName ?? '智能体',
                    'agentAvatar' => $session->agent->agentAvatar ?? '',
                    'agentDesc' => $session->agent->agentDesc ?? '',
                    'agentType' => $session->agent->agentType ?? 1,
                    'agentTypeText' => UserAiAgentModel::AGENT_TYPE_TEXT[$session->agent->agentType ?? 1] ?? '对话型',
                    'commonQuestions' => $session->agent->commonQuestions ?? [],
                ],
            ],
        ];
    }

    /**
     * 修改对话标题
     * @param array $params
     * @return array
     */
    public function updateSessionTitle(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $sessionGuid = $params['sessionGuid'];
        $sessionTitle = trim($params['sessionTitle']);

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 验证对话轮次是否属于当前用户
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '对话轮次不存在或无权限访问');
        }

        // 验证对话状态（只有进行中和已结束的对话可以修改标题）
        if (
            !in_array($session->session_status, [
            UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE,
            UserAiAgentChatSessionModel::SESSION_STATUS_ENDED
            ])
        ) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '当前对话状态不允许修改标题');
        }

        // 检查标题是否有变化
        if ($session->session_title === $sessionTitle) {
            return [
                'sessionGuid' => $sessionGuid,
                'sessionTitle' => $sessionTitle,
                'message' => '标题未发生变化',
            ];
        }

        // 更新对话标题
        $session->session_title = $sessionTitle;
        $session->update_time = time();

        if (!$session->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '修改对话标题失败');
        }

        return [
            'sessionGuid' => $sessionGuid,
            'sessionTitle' => $sessionTitle,
            'oldTitle' => $session->getOrigin('session_title'), // 获取修改前的标题
            'updateTime' => date('Y-m-d H:i:s', $session->update_time),
            'message' => '对话标题修改成功',
        ];
    }

    /**
     * 删除聊天对话
     * @param array $params
     * @return array
     */
    public function deleteSession(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $sessionGuid = $params['sessionGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 验证对话轮次是否属于当前用户
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '对话轮次不存在或无权限访问');
        }

        // 检查对话是否已经被删除
        if ($session->session_status == UserAiAgentChatSessionModel::SESSION_STATUS_DELETED) {
            return [
                'sessionGuid' => $sessionGuid,
                'message' => '对话已经被删除',
            ];
        }

        // 开启事务
        Db::startTrans();
        try {
            // 1. 删除聊天对话（软删除，只修改状态）
            $session->session_status = UserAiAgentChatSessionModel::SESSION_STATUS_DELETED;
            $session->update_time = time();

            if (!$session->save()) {
                throw new \Exception('删除对话失败');
            }

            // 2. 修改对应的智能体订阅状态为已取消
            $this->cancelAgentSubscription(
                $platformUserSysId,
                $merchantGuid,
                $session->agent_guid,
                $session->subscription_guid
            );

            // 提交事务
            Db::commit();

            return [
                'sessionGuid' => $sessionGuid,
                'agentGuid' => $session->agent_guid,
                'subscriptionGuid' => $session->subscription_guid,
                'deleteTime' => date('Y-m-d H:i:s', $session->update_time),
                'message' => '对话删除成功',
            ];
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, '删除对话失败：' . $e->getMessage());
        }
    }

    /**
     * 取消智能体订阅（只修改订阅状态，不影响订阅人数统计）
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param string $agentGuid
     * @param string $subscriptionGuid
     * @return void
     */
    private function cancelAgentSubscription(
        int $platformUserSysId,
        string $merchantGuid,
        string $agentGuid,
        string $subscriptionGuid
    ): void {
        // 如果有订阅GUID，直接通过GUID查找
        if (!empty($subscriptionGuid)) {
            $subscription = UserAiAgentSubscriptionModel::getInstance()
                ->where('guid', $subscriptionGuid)
                ->where('platform_user_sys_id', $platformUserSysId)
                ->findOrEmpty();
        } else {
            // 如果没有订阅GUID，通过用户ID和智能体GUID查找
            $subscription = UserAiAgentSubscriptionModel::getInstance()
                ->where('platform_user_sys_id', $platformUserSysId)
                ->where('merchant_guid', $merchantGuid)
                ->where('agent_guid', $agentGuid)
                ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
                ->findOrEmpty();
        }

        // 如果找到订阅记录，修改状态为已取消
        if (!$subscription->isEmpty()) {
            $subscription->subscription_status = UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_CANCELLED;
            $subscription->update_time = time();

            if (!$subscription->save()) {
                throw new \Exception('取消订阅失败');
            }
        }
    }

    /**
     * 格式化显示时间（相对时间）
     * @param string $timeStr
     * @return string
     */
    private function formatDisplayTime(string $timeStr): string
    {
        if (empty($timeStr)) {
            return '';
        }

        $timestamp = strtotime($timeStr);
        $now = time();
        $diff = $now - $timestamp;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 86400 * 2) {
            return '昨天';
        } elseif ($diff < 86400 * 7) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('m-d', $timestamp);
        }
    }

    /**
     * 获取聊天角色（兼容square模块格式）
     * @param int $messageType
     * @return string
     */
    private function getChatRole(int $messageType): string
    {
        switch ($messageType) {
            case UserAiAgentChatMessageModel::MESSAGE_TYPE_USER:
                return 'user';
            case UserAiAgentChatMessageModel::MESSAGE_TYPE_AGENT_REPLY:
                return 'assistant';
            case UserAiAgentChatMessageModel::MESSAGE_TYPE_SYSTEM:
                return 'system';
            default:
                return 'user';
        }
    }

    /**
     * 获取图片URL列表
     * @param array $fileUrls
     * @param string $contentType
     * @return array
     */
    private function getImageUrls(array $fileUrls, string $contentType): array
    {
        if ($contentType !== UserAiAgentChatMessageModel::CONTENT_TYPE_IMAGE) {
            return [];
        }

        return $fileUrls ?? [];
    }

    /**
     * 获取视频URL列表
     * @param array $fileUrls
     * @param string $contentType
     * @return array
     */
    private function getVideoUrls(array $fileUrls, string $contentType): array
    {
        if ($contentType !== UserAiAgentChatMessageModel::CONTENT_TYPE_VIDEO) {
            return [];
        }

        return $fileUrls ?? [];
    }

    /**
     * 保存智能体聊天消息
     * @param array $data
     * @return array
     * @throws ApiException
     */
    public function saveMsg($data): array
    {
        // FIXME: 临时兼容
        $data['contentType'] = 'text';
        // 1. 基础参数验证
        $this->validateBasicParams($data);

        // 2. 获取用户信息
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 3. 获取聊天轮次（从中获取agentGuid）
        $session = $this->getSessionByGuid($data['sessionGuid'], $userId, $userInfo['merchantGuid']);

        // 4. 从聊天轮次中获取agentGuid
        $data['agentGuid'] = $session->agentGuid;

        // 5. 智能体订阅校验
        $subscription = $this->validateAgentSubscription($data, $userId, $userInfo['merchantGuid']);

        // 5.1. 点数余额检查（仅对用户消息且已订阅会员的用户）
        $userPoints = null;
        if ($data['role'] == 'user' && $subscription->purchaseStatus == 1) {
            $userPoints = $this->checkAndDeductUserPoints($userId);
        }

        // 6. 保存消息（使用适配器）
        try {
            $result = $this->agentCache->saveAgentMsg(
                $data['role'],
                $data['content'],
                $session->guid,
                $data['agentGuid'],
                $data['lastMsgId'] ?? '',
                $data['imgs'] ?? [],
                $data['videos'] ?? []
            );

            // 7. 更新轮次统计
            $this->updateSessionStats($session, $result);

            // 8. 扣减试用次数（仅对付费智能体的未激活用户且是用户消息）
            if ($data['role'] == 'user') {
                $this->handleTrialCountDeduction($subscription, $data['agentGuid'], $userId, $userInfo['merchantGuid']);
            }

            // 9. 记录成功日志
            LogInfo('ai_agent_chat_save', '智能体聊天消息保存成功', '用户消息已保存', [
                'sessionGuid' => $session->guid,
                'agentGuid' => $data['agentGuid'],
                'role' => $data['role'],
                'userId' => $userId,
                'trialCount' => $subscription->trialChatCount,
                'purchaseStatus' => $subscription->purchaseStatus
            ]);

            $returnData = array_merge($result, [
                'messageGuid' => $result['guid'] ?? '', // 添加保存的消息GUID
                'sessionGuid' => $session->guid,
                'agentGuid' => $data['agentGuid'],
                'trialCount' => $subscription->trialChatCount,
                'subscriptionStatus' => $subscription->subscriptionStatus,
                'purchaseStatus' => $subscription->purchaseStatus
            ]);

            // 如果扣除了点数，返回剩余点数信息
            if ($userPoints !== null) {
                $returnData['userPoints'] = [
                    'remainingPoints' => $userPoints,
                    'remainingPointsText' => '剩余' . $userPoints . '点'
                ];
            }

            return $returnData;
        } catch (\Exception $e) {
            LogError('ai_agent_chat_save', '保存智能体聊天消息失败', $e->getMessage(), $data);
            throwException(SysErrorCode::SYS_ERROR_CODE, '保存消息失败，请重试');
        }
    }

    /**
     * 发送智能体聊天（流式）
     * @param array $data
     * @throws ApiException
     */
    public function sendOpen($data)
    {
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');

        // 1. 参数验证
        if (empty($data['msgId'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '消息ID不能为空');
        }

        // 2. 从缓存获取消息上下文
        $message = $this->agentCache->getMsg($data['msgId']);
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }

        // 3. 从消息上下文中获取智能体信息
        $agentInfo = $this->getAgentInfoFromMessage($message, $data);

        // 4. 根据智能体类型处理
        $this->processAgentChatFromMessage($agentInfo['agent'], $message, $data);
    }

    /**
     * 发送智能体聊天（非流式）
     * @param array $data
     * @return array
     * @throws ApiException
     */
    public function sendAll($data): array
    {
        // 1. 参数验证
        if (empty($data['msgId'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '消息ID不能为空');
        }

        // 2. 从缓存获取消息上下文
        $message = $this->agentCache->getMsg($data['msgId']);
        if (!$message) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取聊天信息失败，请重新发送');
        }

        // 3. 从消息上下文中获取智能体信息
        $agentInfo = $this->getAgentInfoFromMessage($message, $data);

        // 4. 根据智能体类型处理（非流式）
        return $this->processAgentChatAllFromMessage($agentInfo['agent'], $message, $data);
    }

    /**
     * 基础参数验证
     * @param array $data
     * @throws ApiException
     */
    private function validateBasicParams($data)
    {
        if (empty($data['sessionGuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天轮次GUID不能为空');
        }
        if (empty($data['role'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天角色不能为空');
        }
        if (empty($data['content'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '消息内容不能为空');
        }

        // 验证角色类型（与现有聊天接口保持一致）
        $allowedRoles = ['user', 'assistant'];
        if (!in_array($data['role'], $allowedRoles)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天角色异常');
        }
    }

    /**
     * 智能体订阅校验
     * @param array $data
     * @param int $userId
     * @param string $merchantGuid
     * @return UserAiAgentSubscriptionModel
     * @throws ApiException
     */
    private function validateAgentSubscription($data, $userId, $merchantGuid)
    {
        $agentGuid = $data['agentGuid'];

        // 首先检查智能体是否支持会员使用
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $agentGuid)
            ->where('merchant_guid', $merchantGuid)
            ->field('guid,is_membership_available,platform_user_sys_id,category_guid,is_featured,is_paid,price')
            ->findOrEmpty();

        if (!$agent->isEmpty()) {
            // 精品智能体特殊验证：必须订阅该智能体才能使用
            if ($agent->isFeatured == 1) {
                LogInfo('ai_agent_subscription', '精品智能体验证', '检测到精品智能体，必须订阅才能使用', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'merchantGuid' => $merchantGuid,
                    'isFeatured' => $agent->isFeatured
                ]);

                // 精品智能体必须直接订阅，不能通过会员、创作者订阅或分类订阅使用
                return $this->validateFeaturedAgentSubscription($userId, $merchantGuid, $agentGuid);
            }

            // 检查用户是否为有效大会员
            if ($agent->isMembershipAvailable && UserAiAgentMembershipSubscriptionModel::isActiveMember($userId, $merchantGuid)) {
                LogInfo('ai_agent_subscription', '大会员权限验证通过', '用户为有效大会员，跳过订阅验证', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'merchantGuid' => $merchantGuid
                ]);

                return $this->createVirtualSubscription($userId, $merchantGuid, $agentGuid, 'membership');
            }

            // 检查用户是否订阅了该智能体的创作者
            if (UserAiAgentCreatorSubscriptionModel::isSubscribedToAgentCreator($userId, $agent->platformUserSysId, $merchantGuid)) {
                LogInfo('ai_agent_subscription', '创作者订阅权限验证通过', '用户已订阅该智能体的创作者', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'agentCreatorUserId' => $agent->platformUserSysId,
                    'merchantGuid' => $merchantGuid
                ]);

                return $this->createVirtualSubscription($userId, $merchantGuid, $agentGuid, 'creator');
            }

            // 检查用户是否订阅了该智能体所属的分类
            if (!empty($agent->categoryGuid) && UserAiAgentCategorySubscriptionModel::isSubscribedToCategory($userId, $agent->categoryGuid)) {
                LogInfo('ai_agent_subscription', '分类订阅权限验证通过', '用户已订阅该智能体所属的分类', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'categoryGuid' => $agent->categoryGuid,
                    'merchantGuid' => $merchantGuid
                ]);

                return $this->createVirtualSubscription($userId, $merchantGuid, $agentGuid, 'category');
            }
        }

        // 查询订阅记录
        $subscription = UserAiAgentSubscriptionModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('merchant_guid', $merchantGuid)
            ->where('agent_guid', $agentGuid)
            ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
            ->findOrEmpty();

        if ($subscription->isEmpty()) {
            LogError('ai_agent_subscription', '智能体订阅校验失败', '用户未订阅该智能体', [
                'userId' => $userId,
                'agentGuid' => $agentGuid,
                'merchantGuid' => $merchantGuid
            ]);
            throwException(SysErrorCode::SYS_ERROR_CODE, '您未订阅该智能体');
        }

        // 免费智能体特殊处理：只要有订阅记录就可以使用，不需要校验试用次数和激活状态
        if (!$agent->isEmpty() && $agent->isPaid == UserAiAgentModel::PAID_FREE) {
            LogInfo('ai_agent_subscription', '免费智能体验证通过', '免费智能体只需要有订阅记录即可使用', [
                'userId' => $userId,
                'agentGuid' => $agentGuid,
                'merchantGuid' => $merchantGuid,
                'isPaid' => $agent->isPaid,
                'price' => $agent->price
            ]);

            // 免费智能体保持原有的购买状态，不强制修改
            // 免费智能体的试用次数扣减将在handleTrialCountDeduction中处理

            return $subscription;
        }

        // 付费智能体需要进行额外验证
        // 检查是否已激活
        if ($subscription->purchaseStatus == 0) {
            // 未激活，检查试用次数
            if ($subscription->trialChatCount <= 0) {
                LogError('ai_agent_subscription', '智能体试用次数已用完', '用户试用次数不足', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionGuid' => $subscription->guid,
                    'trialChatCount' => $subscription->trialChatCount
                ]);
                throwException(SysErrorCode::AI_AGENT_TRIAL_EXPIRED, '试用次数已用完，请购买激活');
            }
        } else {
            // 已激活，检查是否过期
            if ($subscription->expireTime > 0 && $subscription->expireTime < time()) {
                LogError('ai_agent_subscription', '智能体订阅已过期', '用户订阅已过期', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionGuid' => $subscription->guid,
                    'expireTime' => $subscription->expireTime,
                    'currentTime' => time()
                ]);
                throwException(self::AGENT_SUBSCRIPTION_EXPIRED, '订阅已过期');
            }
        }

        return $subscription;
    }

    /**
     * 验证精品智能体订阅
     * @param int $userId
     * @param string $merchantGuid
     * @param string $agentGuid
     * @return UserAiAgentSubscriptionModel
     * @throws ApiException
     */
    private function validateFeaturedAgentSubscription(int $userId, string $merchantGuid, string $agentGuid): UserAiAgentSubscriptionModel
    {
        // 查询精品智能体的直接订阅记录
        $subscription = UserAiAgentSubscriptionModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('merchant_guid', $merchantGuid)
            ->where('agent_guid', $agentGuid)
            ->where('subscription_status', UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL)
            ->findOrEmpty();

        if ($subscription->isEmpty()) {
            LogError('ai_agent_subscription', '精品智能体订阅校验失败', '用户未订阅该精品智能体', [
                'userId' => $userId,
                'agentGuid' => $agentGuid,
                'merchantGuid' => $merchantGuid,
                'reason' => '精品智能体必须直接订阅才能使用'
            ]);
            throwException(SysErrorCode::SYS_ERROR_CODE, '该智能体为精品智能体，必须订阅后才能使用');
        }

        // 获取智能体信息，检查是否为免费智能体
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $agentGuid)
            ->where('merchant_guid', $merchantGuid)
            ->field('guid,is_paid,price')
            ->findOrEmpty();

        // 免费精品智能体特殊处理：只要有订阅记录就可以使用
        if (!$agent->isEmpty() && $agent->isPaid == UserAiAgentModel::PAID_FREE) {
            LogInfo('ai_agent_subscription', '免费精品智能体验证通过', '免费精品智能体只需要有订阅记录即可使用', [
                'userId' => $userId,
                'agentGuid' => $agentGuid,
                'merchantGuid' => $merchantGuid,
                'isPaid' => $agent->isPaid,
                'price' => $agent->price,
                'isFeatured' => true
            ]);

            // 确保免费智能体的订阅状态正确
            if ($subscription->purchaseStatus == 0) {
                // 对于免费智能体，自动设置为已购买状态
                $subscription->purchaseStatus = 1;
                $subscription->save();
            }

            return $subscription;
        }

        // 付费精品智能体需要进行额外验证
        // 检查是否已激活
        if ($subscription->purchaseStatus == 0) {
            // 未激活，检查试用次数
            if ($subscription->trialChatCount <= 0) {
                LogError('ai_agent_subscription', '精品智能体试用次数已用完', '用户试用次数不足', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionGuid' => $subscription->guid,
                    'trialChatCount' => $subscription->trialChatCount
                ]);
                throwException(SysErrorCode::AI_AGENT_TRIAL_EXPIRED, '试用次数已用完，请购买激活');
            }
        } else {
            // 已激活，检查是否过期
            if ($subscription->expireTime > 0 && $subscription->expireTime < time()) {
                LogError('ai_agent_subscription', '精品智能体订阅已过期', '用户订阅已过期', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionGuid' => $subscription->guid,
                    'expireTime' => $subscription->expireTime,
                    'currentTime' => time()
                ]);
                throwException(SysErrorCode::AI_AGENT_TRIAL_EXPIRED, '订阅已过期');
            }
        }

        LogInfo('ai_agent_subscription', '精品智能体订阅验证通过', '用户已订阅该精品智能体', [
            'userId' => $userId,
            'agentGuid' => $agentGuid,
            'subscriptionGuid' => $subscription->guid,
            'purchaseStatus' => $subscription->purchaseStatus,
            'trialChatCount' => $subscription->trialChatCount
        ]);

        return $subscription;
    }

    /**
     * 创建虚拟订阅对象
     * @param int $userId
     * @param string $merchantGuid
     * @param string $agentGuid
     * @param string $subscriptionType
     * @return UserAiAgentSubscriptionModel
     */
    private function createVirtualSubscription(int $userId, string $merchantGuid, string $agentGuid, string $subscriptionType): UserAiAgentSubscriptionModel
    {
        // 创建一个虚拟的订阅对象，表示专属订阅权限
        $virtualSubscription = new UserAiAgentSubscriptionModel();
        $virtualSubscription->platformUserSysId = $userId;
        $virtualSubscription->merchantGuid = $merchantGuid;
        $virtualSubscription->agentGuid = $agentGuid;
        $virtualSubscription->subscriptionStatus = UserAiAgentSubscriptionModel::SUBSCRIPTION_STATUS_NORMAL;
        $virtualSubscription->purchaseStatus = UserAiAgentSubscriptionModel::PURCHASE_STATUS_ACTIVE;
        $virtualSubscription->trialChatCount = 999999; // 专属订阅无限制
        $virtualSubscription->expireTime = 0; // 永不过期（以专属订阅到期时间为准）

        return $virtualSubscription;
    }

    /**
     * 根据GUID获取聊天轮次
     * @param string $sessionGuid
     * @param int $userId
     * @param string $merchantGuid
     * @return UserAiAgentChatSessionModel
     * @throws ApiException
     */
    private function getSessionByGuid($sessionGuid, $userId, $merchantGuid)
    {
        // 获取指定的聊天轮次（不需要agentGuid，因为要从轮次中获取）
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('platform_user_sys_id', $userId)
            ->where('merchant_guid', $merchantGuid)
            ->where('session_status', 'in', [
                UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE,
                UserAiAgentChatSessionModel::SESSION_STATUS_ENDED
            ])
            ->findOrEmpty();

        if ($session->isEmpty()) {
            LogError('ai_agent_session', '聊天轮次不存在', '指定的聊天轮次不存在或无权限访问', [
                'sessionGuid' => $sessionGuid,
                'userId' => $userId,
                'merchantGuid' => $merchantGuid
            ]);
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天轮次不存在或无权限访问');
        }

        // 检查轮次状态，如果是已结束状态，重新激活
        if ($session->sessionStatus == UserAiAgentChatSessionModel::SESSION_STATUS_ENDED) {
            $session->sessionStatus = UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE;
            $session->save();
        }

        return $session;
    }

    /**
     * 根据GUID获取聊天轮次（不需要用户认证，用于sendOpen接口）
     * @param string $sessionGuid
     * @return UserAiAgentChatSessionModel
     * @throws ApiException
     */
    private function getSessionByGuidWithoutAuth($sessionGuid)
    {
        // 获取指定的聊天轮次（不需要用户ID和商家GUID验证）
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('session_status', 'in', [
                UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE,
                UserAiAgentChatSessionModel::SESSION_STATUS_ENDED
            ])
            ->findOrEmpty();

        if ($session->isEmpty()) {
            LogError('ai_agent_session', '聊天轮次不存在', '指定的聊天轮次不存在', [
                'sessionGuid' => $sessionGuid
            ]);
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天轮次不存在');
        }

        // 检查轮次状态，如果是已结束状态，重新激活
        if ($session->sessionStatus == UserAiAgentChatSessionModel::SESSION_STATUS_ENDED) {
            $session->sessionStatus = UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE;
            $session->save();
        }

        return $session;
    }

    /**
     * 更新轮次统计
     * @param UserAiAgentChatSessionModel $session
     * @param array $result
     */
    private function updateSessionStats($session, $result)
    {
        try {
            $session->messageCount += 1;
            $session->tokensUsed += $result['useToken'] ?? 0;
            $session->lastMessageTime = time();
            $session->save();
        } catch (\Exception $e) {
            LogError('ai_agent_chat_stats', '更新轮次统计失败', $e->getMessage(), [
                'sessionGuid' => $session->guid,
                'result' => $result
            ]);
        }
    }

    /**
     * 处理试用次数扣减逻辑
     * @param UserAiAgentSubscriptionModel $subscription
     * @param string $agentGuid
     * @param int $userId
     * @param string $merchantGuid
     */
    private function handleTrialCountDeduction($subscription, $agentGuid, $userId, $merchantGuid)
    {
        try {
            // 1. 检查是否为虚拟订阅（大会员、创作者订阅、分类订阅）
            if (!isset($subscription->sysId) || empty($subscription->sysId)) {
                // 虚拟订阅不扣减试用次数
                LogInfo('ai_agent_trial', '虚拟订阅跳过试用次数扣减', '虚拟订阅用户无需扣减试用次数', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionType' => 'virtual'
                ]);
                return;
            }

            // 2. 获取智能体信息，检查是否为付费智能体
            $agent = UserAiAgentModel::getInstance()
                ->where('guid', $agentGuid)
                ->where('merchant_guid', $merchantGuid)
                ->field('guid,is_paid,price')
                ->findOrEmpty();

            if ($agent->isEmpty()) {
                LogError('ai_agent_trial', '智能体不存在', '无法获取智能体信息', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid
                ]);
                return;
            }

            // 3. 免费智能体不扣减试用次数
            if ($agent->isPaid == UserAiAgentModel::PAID_FREE) {
                LogInfo('ai_agent_trial', '免费智能体跳过试用次数扣减', '免费智能体无需扣减试用次数', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'isPaid' => $agent->isPaid
                ]);
                return;
            }

            // 4. 付费智能体且未激活才扣减试用次数
            if ($subscription->purchaseStatus == UserAiAgentSubscriptionModel::PURCHASE_STATUS_INACTIVE) {
                $this->deductTrialCount($subscription);
                LogInfo('ai_agent_trial', '扣减试用次数', '付费智能体未激活用户扣减试用次数', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionGuid' => $subscription->guid,
                    'remainingTrialCount' => $subscription->trialChatCount,
                    'purchaseStatus' => $subscription->purchaseStatus
                ]);
            } else {
                LogInfo('ai_agent_trial', '已激活用户跳过试用次数扣减', '已激活用户无需扣减试用次数', [
                    'userId' => $userId,
                    'agentGuid' => $agentGuid,
                    'subscriptionGuid' => $subscription->guid,
                    'purchaseStatus' => $subscription->purchaseStatus
                ]);
            }
        } catch (\Exception $e) {
            LogError('ai_agent_trial', '处理试用次数扣减失败', $e->getMessage(), [
                'userId' => $userId,
                'agentGuid' => $agentGuid,
                'subscriptionGuid' => $subscription->guid ?? 'unknown'
            ]);
        }
    }

    /**
     * 扣减试用次数
     * @param UserAiAgentSubscriptionModel $subscription
     */
    private function deductTrialCount($subscription)
    {
        try {
            if ($subscription->trialChatCount > 0) {
                $subscription->trialChatCount -= 1;
                $subscription->lastChatTime = time();
                $subscription->totalChatCount += 1;
                $subscription->save();
            }
        } catch (\Exception $e) {
            LogError('ai_agent_trial_deduct', '扣减试用次数失败', $e->getMessage(), [
                'subscriptionGuid' => $subscription->guid
            ]);
        }
    }

    /**
     * 获取智能体配置
     * @param string $agentGuid
     * @return UserAiAgentModel
     * @throws ApiException
     */
    private function getAgentConfig($agentGuid)
    {
        $agent = UserAiAgentModel::getInstance()
            ->where('guid', $agentGuid)
            ->where('audit_status', UserAiAgentModel::AUDIT_STATUS_APPROVED)
            ->findOrEmpty();

        if ($agent->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '智能体不存在或未通过审核');
        }

        return $agent;
    }

    /**
     * 处理智能体聊天（流式）
     * @param UserAiAgentModel $agent
     * @param array $context
     * @param array $data
     */
    private function processAgentChat($agent, $context, $data)
    {
        switch ($agent->agentType) {
            case UserAiAgentModel::AGENT_TYPE_INTERNAL:
                $this->handleInternalAgent($agent, $context, $data);
                break;
            case UserAiAgentModel::AGENT_TYPE_DIFY:
            case UserAiAgentModel::AGENT_TYPE_COZE:
            case UserAiAgentModel::AGENT_TYPE_ALIYUN:
                $this->handleExternalAgent($agent, $context, $data);
                break;
            default:
                throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体类型');
        }
    }

    /**
     * 处理智能体聊天（非流式）
     * @param UserAiAgentModel $agent
     * @param array $context
     * @param array $data
     * @return array
     */
    private function processAgentChatAll($agent, $context, $data): array
    {
        switch ($agent->agentType) {
            case UserAiAgentModel::AGENT_TYPE_INTERNAL:
                return $this->handleInternalAgentAll($agent, $context, $data);
            case UserAiAgentModel::AGENT_TYPE_DIFY:
            case UserAiAgentModel::AGENT_TYPE_COZE:
            case UserAiAgentModel::AGENT_TYPE_ALIYUN:
                return $this->handleExternalAgentAll($agent, $context, $data);
            default:
                throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体类型');
        }
    }

    /**
     * 处理内部智能体（流式）
     * @param UserAiAgentModel $agent
     * @param array $context
     * @param array $data
     */
    private function handleInternalAgent($agent, $context, $data)
    {
        // 组装系统提示词
        $systemPrompt = $agent->promptContent ?? '';

        // 如果有知识库，添加知识库内容
        if (!empty($agent->knowledgeBaseIds)) {
            $knowledgeContent = $this->getKnowledgeContent($agent->knowledgeBaseIds);
            if (!empty($knowledgeContent)) {
                $systemPrompt .= "\n\n知识库内容：\n" . $knowledgeContent;
            }
        }

        // 构建消息数组
        $messages = [];
        if (!empty($systemPrompt)) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }
        $messages = array_merge($messages, $context);

        // 调用AI模型
        $entity = new ChatCompleteEntity($messages);
        $entity->stream = true;
        $entity->temperature = $data['temperature'] ?? 0.8;
        // sendOpen接口不登录，使用默认用户ID或留空
        $entity->user_id = 'anonymous_user';

        // 使用默认模型或智能体配置的模型
        $modelType = $agent->agentConfig['model_type'] ?? 'azure_gpt4';

        (new AiChatFactory())->make($modelType)->chatComplete($entity);
    }

    /**
     * 处理内部智能体（非流式）
     * @param UserAiAgentModel $agent
     * @param array $context
     * @param array $data
     * @return array
     */
    private function handleInternalAgentAll($agent, $context, $data): array
    {
        // 组装系统提示词
        $systemPrompt = $agent->promptContent ?? '';

        // 如果有知识库，添加知识库内容
        if (!empty($agent->knowledgeBaseIds)) {
            $knowledgeContent = $this->getKnowledgeContent($agent->knowledgeBaseIds);
            if (!empty($knowledgeContent)) {
                $systemPrompt .= "\n\n知识库内容：\n" . $knowledgeContent;
            }
        }

        // 构建消息数组
        $messages = [];
        if (!empty($systemPrompt)) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }
        $messages = array_merge($messages, $context);

        // 调用AI模型
        $entity = new ChatCompleteEntity($messages);
        $entity->stream = false;
        $entity->temperature = $data['temperature'] ?? 0.8;
        // sendAll接口不登录，使用默认用户ID或留空
        $entity->user_id = 'anonymous_user';

        // 使用默认模型或智能体配置的模型
        $modelType = $agent->agentConfig['model_type'] ?? 'azure_gpt4';

        return (new AiChatFactory())->make($modelType)->chatComplete($entity);
    }

    /**
     * 处理外部智能体（流式）
     * @param UserAiAgentModel $agent
     * @param array $context
     * @param array $data
     */
    private function handleExternalAgent($agent, $context, $data)
    {
        $entity = new AgentCompleteEntity($context);
        $entity->stream = true;
        // sendOpen接口不登录，使用默认用户ID或留空
        $entity->user_id = 'anonymous_user';
        $entity->conversation_id = $data['sessionGuid'];

        // 从智能体配置中获取API密钥等
        $config = $agent->agentConfig ?? [];
        $entity->secret_token = $config['secret_token'] ?? '';
        $entity->sign = $config['sign'] ?? '';

        $vendorMap = [
            UserAiAgentModel::AGENT_TYPE_DIFY => 'dify',
            UserAiAgentModel::AGENT_TYPE_COZE => 'coze',
            UserAiAgentModel::AGENT_TYPE_ALIYUN => 'aliyun'
        ];

        $vendor = $vendorMap[$agent->agentType] ?? '';
        if (empty($vendor)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体厂商');
        }

        (new AiAgentFactory())->make($vendor)->agentComplete($entity);
    }

    /**
     * 处理外部智能体（非流式）
     * @param UserAiAgentModel $agent
     * @param array $context
     * @param array $data
     * @return array
     */
    private function handleExternalAgentAll($agent, $context, $data): array
    {
        $entity = new AgentCompleteEntity($context);
        $entity->stream = false;
        // sendAll接口不登录，使用默认用户ID或留空
        $entity->user_id = 'anonymous_user';
        $entity->conversation_id = $data['sessionGuid'];

        // 从智能体配置中获取API密钥等
        $config = $agent->agentConfig ?? [];
        $entity->secret_token = $config['secret_token'] ?? '';
        $entity->sign = $config['sign'] ?? '';

        $vendorMap = [
            UserAiAgentModel::AGENT_TYPE_DIFY => 'dify',
            UserAiAgentModel::AGENT_TYPE_COZE => 'coze',
            UserAiAgentModel::AGENT_TYPE_ALIYUN => 'aliyun'
        ];

        $vendor = $vendorMap[$agent->agentType] ?? '';
        if (empty($vendor)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体厂商');
        }

        return (new AiAgentFactory())->make($vendor)->agentComplete($entity);
    }

    /**
     * 获取知识库内容
     * @param array $knowledgeBaseIds
     * @return string
     */
    private function getKnowledgeContent($knowledgeBaseIds): string
    {
        // TODO: 实现知识库内容获取逻辑
        // 这里可以调用知识库服务获取相关内容
        return '';
    }

    /**
     * 从消息上下文中获取智能体信息（不登录状态）
     * @param array $message
     * @param array $data
     * @return array
     * @throws ApiException
     */
    private function getAgentInfoFromMessage($message, $data)
    {
        // 从消息的最后一条记录中获取智能体相关信息
        // 这里需要根据实际的消息结构来获取sessionGuid和agentGuid

        // 这里需要根据实际业务逻辑来获取sessionGuid
        // 可能需要从消息内容、数据库或其他地方获取
        $sessionGuid = $this->extractSessionGuidFromMessage($message, $data);

        if (empty($sessionGuid)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '无法获取聊天轮次信息');
        }

        // 获取聊天轮次（不需要用户ID验证）
        $session = $this->getSessionByGuidWithoutAuth($sessionGuid);

        // 获取智能体配置
        $agent = $this->getAgentConfig($session->agentGuid);

        return [
            'session' => $session,
            'agent' => $agent,
            'sessionGuid' => $sessionGuid,
            'agentGuid' => $session->agentGuid
        ];
    }

    /**
     * 从消息中提取sessionGuid
     * @param array $message
     * @param array $data
     * @return string
     */
    private function extractSessionGuidFromMessage($message, $data)
    {
        // 这里需要根据实际的消息结构来提取sessionGuid
        // 可能从chatLunciGuid、消息内容或其他地方获取

        // 如果有chatLunciGuid参数，可以用它来查找对应的session
        if (!empty($data['chatLunciGuid'])) {
            // 根据chatLunciGuid查找对应的智能体聊天轮次
            $session = UserAiAgentChatSessionModel::getInstance()
                ->where('guid', $data['chatLunciGuid'])
                ->findOrEmpty();

            if (!$session->isEmpty()) {
                return $session->guid;
            }
        }

        // 如果没有找到，可能需要其他方式来确定sessionGuid
        // 这里返回空，让上层处理
        return '';
    }

    /**
     * 处理智能体聊天（从消息，流式）
     * @param UserAiAgentModel $agent
     * @param array $message
     * @param array $data
     */
    private function processAgentChatFromMessage($agent, $message, $data)
    {
        switch ($agent->agentType) {
            case UserAiAgentModel::AGENT_TYPE_INTERNAL:
                $this->handleInternalAgentFromMessage($agent, $message, $data);
                break;
            case UserAiAgentModel::AGENT_TYPE_DIFY:
            case UserAiAgentModel::AGENT_TYPE_COZE:
            case UserAiAgentModel::AGENT_TYPE_ALIYUN:
                $this->handleExternalAgentFromMessage($agent, $message, $data);
                break;
            default:
                throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体类型');
        }
    }

    /**
     * 处理智能体聊天（从消息，非流式）
     * @param UserAiAgentModel $agent
     * @param array $message
     * @param array $data
     * @return array
     */
    private function processAgentChatAllFromMessage($agent, $message, $data): array
    {
        switch ($agent->agentType) {
            case UserAiAgentModel::AGENT_TYPE_INTERNAL:
                return $this->handleInternalAgentAllFromMessage($agent, $message, $data);
            case UserAiAgentModel::AGENT_TYPE_DIFY:
            case UserAiAgentModel::AGENT_TYPE_COZE:
            case UserAiAgentModel::AGENT_TYPE_ALIYUN:
                return $this->handleExternalAgentAllFromMessage($agent, $message, $data);
            default:
                throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体类型');
        }
    }

    /**
     * 处理内部智能体（从消息，流式）
     * @param UserAiAgentModel $agent
     * @param array $message
     * @param array $data
     */
    private function handleInternalAgentFromMessage($agent, $message, $data)
    {
        // 组装系统提示词
        $systemPrompt = $agent->promptContent ?? '';

        // 如果有知识库，添加知识库内容
        if (!empty($agent->knowledgeBaseIds)) {
            $knowledgeContent = $this->getKnowledgeContent($agent->knowledgeBaseIds);
            if (!empty($knowledgeContent)) {
                $systemPrompt .= "\n\n知识库内容：\n" . $knowledgeContent;
            }
        }

        // 构建消息数组
        $messages = [];
        if (!empty($systemPrompt)) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }
        $messages = array_merge($messages, $message);

        // 调用AI模型
        $entity = new ChatCompleteEntity($messages);
        $entity->stream = true;
        // sendOpen接口不登录，使用默认用户ID
        $entity->user_id = 'anonymous_user';

        // 使用默认模型或指定的模型
        $modelType = !empty($data['aiModelGuid']) ?
            $this->getModelTypeByGuid($data['aiModelGuid']) :
            ($agent->agentConfig['model_type'] ?? 'azure_gpt4');

        // 设置采样参数
        if (!empty($data['topP']) && $data['topP'] > 0 && $data['topP'] <= 1) {
            $entity->top_p = $data['topP'];
        }
        (new AiChatFactory())->make($modelType)->chatComplete($entity);
    }

    /**
     * 处理内部智能体（从消息，非流式）
     * @param UserAiAgentModel $agent
     * @param array $message
     * @param array $data
     * @return array
     */
    private function handleInternalAgentAllFromMessage($agent, $message, $data): array
    {
        // 组装系统提示词
        $systemPrompt = $agent->promptContent ?? '';

        // 如果有知识库，添加知识库内容
        if (!empty($agent->knowledgeBaseIds)) {
            $knowledgeContent = $this->getKnowledgeContent($agent->knowledgeBaseIds);
            if (!empty($knowledgeContent)) {
                $systemPrompt .= "\n\n知识库内容：\n" . $knowledgeContent;
            }
        }

        // 构建消息数组
        $messages = [];
        if (!empty($systemPrompt)) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }
        $messages = array_merge($messages, $message);

        // 调用AI模型
        $entity = new ChatCompleteEntity($messages);
        $entity->stream = false;
        // sendAll接口不登录，使用默认用户ID
        $entity->user_id = 'anonymous_user';

        // 使用默认模型或指定的模型
        $modelType = !empty($data['aiModelGuid']) ?
            $this->getModelTypeByGuid($data['aiModelGuid']) :
            ($agent->agentConfig['model_type'] ?? 'azure_gpt4');

        // 设置采样参数
        if (!empty($data['topP']) && $data['topP'] > 0 && $data['topP'] <= 1) {
            $entity->top_p = $data['topP'];
        }

        return (new AiChatFactory())->make($modelType)->chatComplete($entity);
    }

    /**
     * 处理外部智能体（从消息，流式）
     * @param UserAiAgentModel $agent
     * @param array $message
     * @param array $data
     */
    private function handleExternalAgentFromMessage($agent, $message, $data)
    {
        $entity = new AgentCompleteEntity($message);
        $entity->stream = true;
        // sendOpen接口不登录，使用默认用户ID
        $entity->user_id = 'anonymous_user';
        $entity->conversation_id = $data['conversation_id'] ?? '';

        // 从智能体配置中获取API密钥等
        $config = $agent->agentConfig ?? [];
        $entity->secret_token = $config['secret_token'] ?? '';
        $entity->sign = $config['coze_sign'] ?? '';

        // 如果秘钥方式为引用用户秘钥，则更新为用户实际的秘钥
        if (!empty($config['secret_key_type'])  && $config['secret_key_type'] == 'user') {
            $entity->secret_token = $this->getUserSecretKey($data['platformUserSysId'], $agent['agent_type']);
        }

        // 判断是否有自定义部署地址
        if (!empty($config['deploy_address'])) {
            $entity->deploy_address = $config['deploy_address'];
            $entity->deploy_address_secret = $config['deploy_address_secret'] ?? '';
        }

        $vendorMap = [
            UserAiAgentModel::AGENT_TYPE_DIFY => 'dify',
            UserAiAgentModel::AGENT_TYPE_COZE => 'coze',
            UserAiAgentModel::AGENT_TYPE_ALIYUN => 'aliyun'
        ];

        $vendor = $vendorMap[$agent->agentType] ?? '';
        if (empty($vendor)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体厂商');
        }
        (new AiAgentFactory())->make($vendor)->agentComplete($entity);
    }


    /**
     * 获取用户实际的秘钥
     * @param $platformUserSysId
     * @param $agentType
     * @return mixed|string
     */
    private function getUserSecretKey($platformUserSysId, $agentType)
    {
        $secretKeyType = '';
        switch ($agentType) {
            case UserAiAgentModel::AGENT_TYPE_DIFY:
                $secretKeyType = 'dify_api_key';
                break;
            case UserAiAgentModel::AGENT_TYPE_COZE:
                $secretKeyType = 'coze_api_key';
                break;
            case UserAiAgentModel::AGENT_TYPE_ALIYUN:
                $secretKeyType = 'aliyun_api_key';
                break;
            default:
                break;
        }
        if (empty($secretKeyType)) {
            return '';
        }

        $secretKey = UserSecretKeyModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('secret_key_type', $secretKeyType)
            ->value('secret_key');
        return $secretKey ?? '';
    }

    /**
     * 处理外部智能体（从消息，非流式）
     * @param UserAiAgentModel $agent
     * @param array $message
     * @param array $data
     * @return array
     */
    private function handleExternalAgentAllFromMessage($agent, $message, $data): array
    {
        $entity = new AgentCompleteEntity($message);
        $entity->stream = false;
        // sendAll接口不登录，使用默认用户ID
        $entity->user_id = 'anonymous_user';
        $entity->conversation_id = $data['chatLunciGuid'] ?? '';

        // 从智能体配置中获取API密钥等
        $config = $agent->agentConfig ?? [];
        $entity->secret_token = $config['secret_token'] ?? '';
        $entity->sign = $config['sign'] ?? '';

        $vendorMap = [
            UserAiAgentModel::AGENT_TYPE_DIFY => 'dify',
            UserAiAgentModel::AGENT_TYPE_COZE => 'coze',
            UserAiAgentModel::AGENT_TYPE_ALIYUN => 'aliyun'
        ];

        $vendor = $vendorMap[$agent->agentType] ?? '';
        if (empty($vendor)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '不支持的智能体厂商');
        }

        return (new AiAgentFactory())->make($vendor)->agentComplete($entity);
    }

    /**
     * 根据模型GUID获取模型类型
     * @param string $aiModelGuid
     * @return string
     */
    private function getModelTypeByGuid($aiModelGuid): string
    {
        // TODO: 根据aiModelGuid查询对应的模型类型
        // 这里需要查询ai_model表获取modelVendorSign
        return 'azure_gpt4'; // 默认返回
    }

    /**
     * 收藏智能体回复内容
     * @param array $params
     * @return array
     */
    public function collectMessage(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $messageGuid = $params['messageGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 查询消息信息
        $message = UserAiAgentChatMessageModel::getInstance()
            ->where('guid', $messageGuid)
            ->where('merchant_guid', $merchantGuid)
            ->findOrEmpty();

        if ($message->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '消息不存在');
        }

        // 验证消息是否属于当前用户的对话
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $message->sessionGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '无权限收藏此消息');
        }

        // 只能收藏智能体回复的消息
        if ($message->messageType != UserAiAgentChatMessageModel::MESSAGE_TYPE_AGENT_REPLY) {
            throwException(SysErrorCode::SYS_PARAMS_ERROR, '只能收藏智能体回复的消息');
        }

        // 检查是否已经收藏过
        if (UserAiAgentMessageCollectionModel::isCollected($platformUserSysId, $messageGuid)) {
            return [
                'messageGuid' => $messageGuid,
                'isCollected' => true,
                'message' => '该消息已经收藏过了'
            ];
        }
        // 添加收藏
        $collection = new UserAiAgentMessageCollectionModel();
        $collection->merchantGuid = $merchantGuid;
        $collection->platformUserSysId = $platformUserSysId;
        $collection->agentGuid = $message->agentGuid;
        $collection->sessionGuid = $message->sessionGuid;
        $collection->messageGuid = $messageGuid;
        $collection->messageContent = $message->content;
        $collection->save();

        if (!$collection) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '收藏失败');
        }

        LogInfo('ai_agent_collection', '消息收藏成功', '用户收藏智能体回复', [
            'userId' => $platformUserSysId,
            'messageGuid' => $messageGuid,
            'agentGuid' => $message->agentGuid,
            'sessionGuid' => $message->sessionGuid,
            'collectionGuid' => $collection->guid
        ]);

        return [
            'messageGuid' => $messageGuid,
            'collectionGuid' => $collection->guid,
            'isCollected' => true,
            'collectTime' => date('Y-m-d H:i:s', $collection->createTime),
            'message' => '收藏成功'
        ];
    }

    /**
     * 删除收藏的智能体回复内容
     * @param array $params
     * @return array
     */
    public function uncollectMessage(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $messageGuid = $params['messageGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 检查是否已收藏
        if (!UserAiAgentMessageCollectionModel::isCollected($platformUserSysId, $messageGuid)) {
            return [
                'messageGuid' => $messageGuid,
                'isCollected' => false,
                'message' => '该消息未收藏'
            ];
        }

        UserAiAgentMessageCollectionModel::destroy(function ($query) use ($platformUserSysId, $messageGuid) {
            $query->where('platform_user_sys_id', $platformUserSysId)
                ->where('message_guid', $messageGuid);
        });

        LogInfo('ai_agent_collection', '取消消息收藏', '用户取消收藏智能体回复', [
            'userId' => $platformUserSysId,
            'messageGuid' => $messageGuid,
            'merchantGuid' => $merchantGuid
        ]);

        return [
            'messageGuid' => $messageGuid,
            'isCollected' => false,
            'uncollectTime' => date('Y-m-d H:i:s'),
            'message' => '取消收藏成功'
        ];
    }

    /**
     * 删除聊天对话中的某一条消息
     * @param array $params
     * @return array
     */
    public function deleteMessage(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $messageGuid = $params['messageGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 查询消息信息
        $message = UserAiAgentChatMessageModel::getInstance()
            ->where('guid', $messageGuid)
            ->where('merchant_guid', $merchantGuid)
            ->findOrEmpty();

        if ($message->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '消息不存在');
        }

        // 验证消息是否属于当前用户的对话
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $message->sessionGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '无权限删除此消息');
        }

        // 开启事务
        Db::startTrans();
        try {
            // 软删除消息
            $message->deletedAt = time();
            $message->updateTime = time();
            $message->save();

            // 更新聊天轮次的消息数量
            $session->messageCount = UserAiAgentChatMessageModel::getInstance()
                ->where('session_guid', $message->sessionGuid)
                ->count();
            $session->updateTime = time();
            $session->save();

            // 提交事务
            Db::commit();

            LogInfo('ai_agent_chat', '删除消息成功', '用户删除聊天消息', [
                'userId' => $platformUserSysId,
                'messageGuid' => $messageGuid,
                'sessionGuid' => $message->sessionGuid,
                'messageType' => $message->messageType
            ]);

            return [
                'messageGuid' => $messageGuid,
                'sessionGuid' => $message->sessionGuid,
                'deleteTime' => date('Y-m-d H:i:s'),
                'message' => '消息删除成功'
            ];
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            LogError('ai_agent_chat', '删除消息失败', $e->getMessage(), [
                'userId' => $platformUserSysId,
                'messageGuid' => $messageGuid,
                'error' => $e->getMessage()
            ]);

            throwException(SysErrorCode::SYS_ERROR_CODE, '删除消息失败：' . $e->getMessage());
        }
    }

    /**
     * 删除聊天对话的所有消息
     * @param array $params
     * @return array
     */
    public function deleteAllMessages(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $sessionGuid = $params['sessionGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 验证聊天轮次是否属于当前用户
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '聊天轮次不存在或无权限操作');
        }

        // 开启事务
        Db::startTrans();
        try {
            // 先统计要删除的消息数量
            $deletedCount = UserAiAgentChatMessageModel::getInstance()
                ->where('session_guid', $sessionGuid)
                ->whereNull('deleted_at')
                ->count();

            // 批量删除该轮次的所有消息
            UserAiAgentChatMessageModel::destroy(function ($query) use ($sessionGuid) {
                $query->where('session_guid', $sessionGuid);
            });

            // 更新聊天轮次的消息数量为0
            $session->messageCount = 0;
            $session->updateTime = time();
            $session->save();

            // 提交事务
            Db::commit();

            LogInfo('ai_agent_chat', '删除所有消息成功', '用户删除聊天轮次的所有消息', [
                'userId' => $platformUserSysId,
                'sessionGuid' => $sessionGuid,
                'deletedCount' => $deletedCount
            ]);

            return [
                'sessionGuid' => $sessionGuid,
                'deletedCount' => $deletedCount,
                'deleteTime' => date('Y-m-d H:i:s'),
                'message' => "成功删除 {$deletedCount} 条消息"
            ];
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            LogError('ai_agent_chat', '删除所有消息失败', $e->getMessage(), [
                'userId' => $platformUserSysId,
                'sessionGuid' => $sessionGuid,
                'error' => $e->getMessage()
            ]);

            throwException(SysErrorCode::SYS_ERROR_CODE, '删除所有消息失败：' . $e->getMessage());
        }
    }

    /**
     * 我的智能体回复收藏列表
     * @param array $params
     * @return array
     */
    public function myCollectionList(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $page = $params['page'] ?? 1;
        $pageSize = $params['pageSize'] ?? 20;
        $agentGuid = $params['agentGuid'] ?? '';

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 参数验证
        if ($page < 1) {
            $page = 1;
        }
        if ($pageSize < 1 || $pageSize > 100) {
            $pageSize = 20;
        }

        // 构建查询条件
        $query = UserAiAgentMessageCollectionModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->whereNull('deleted_at');

        // 按智能体筛选
        if (!empty($agentGuid)) {
            $query->where('agent_guid', $agentGuid);
        }

        // 获取总数
        $total = $query->count();

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $collections = $query->with(['agent', 'session'])
            ->order('create_time', 'desc')
            ->limit($offset, $pageSize)
            ->select();

        // 格式化数据
        $list = [];
        foreach ($collections as $collection) {
            $item = [
                'collectionGuid' => $collection->guid,
                'messageGuid' => $collection->messageGuid,
                'messageContent' => $collection->messageContent,
                'collectionTitle' => $collection->collectionTitle,
                'collectionNote' => $collection->collectionNote,
                'collectTime' => date('Y-m-d H:i:s', $collection->createTime),
                'collectTimeStamp' => $collection->createTime,

                // 智能体信息
                'agent' => [
                    'guid' => $collection->agent->guid ?? '',
                    'agentName' => $collection->agent->agentName ?? '',
                    'agentAvatar' => $collection->agent->agentAvatar ?? '',
                    'agentType' => $collection->agent->agentType ?? 1,
                    'agentTypeText' => UserAiAgentModel::AGENT_TYPE_TEXT[$collection->agent->agentType ?? 1] ?? '',
                ],

                // 聊天轮次信息
                'session' => [
                    'guid' => $collection->session->guid ?? '',
                    'sessionTitle' => $collection->session->sessionTitle ?? '智能体对话',
                ],

                // 消息预览（截取前100字符）
                'contentPreview' => mb_substr(strip_tags($collection->messageContent), 0, 100) . (mb_strlen($collection->messageContent) > 100 ? '...' : ''),

                // 消息字数统计
                'contentLength' => mb_strlen(strip_tags($collection->messageContent)),
            ];

            $list[] = $item;
        }

        // 计算分页信息
        $totalPages = ceil($total / $pageSize);
        $hasMore = $page < $totalPages;

        LogInfo('ai_agent_collection', '获取收藏列表', '用户查看收藏列表', [
            'userId' => $platformUserSysId,
            'page' => $page,
            'pageSize' => $pageSize,
            'total' => $total,
            'agentGuid' => $agentGuid
        ]);

        return [
            'list' => $list,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasMore' => $hasMore,
            ],
            'summary' => [
                'totalCollections' => $total,
                'currentPageCount' => count($list),
                'agentFilter' => !empty($agentGuid),
                'agentGuid' => $agentGuid,
            ]
        ];
    }

    /**
     * 设置对话置顶
     * @param array $params
     * @return array
     */
    public function setSessionTop(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];
        $sessionGuid = $params['sessionGuid'];
        $isTop = (bool)$params['isTop'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 验证对话轮次是否属于当前用户
        $session = UserAiAgentChatSessionModel::getInstance()
            ->where('guid', $sessionGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        if ($session->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '对话轮次不存在或无权限访问');
        }

        // 验证对话状态（只有进行中和已结束的对话可以置顶）
        if (
            !in_array($session->session_status, [
            UserAiAgentChatSessionModel::SESSION_STATUS_ACTIVE,
            UserAiAgentChatSessionModel::SESSION_STATUS_ENDED
            ])
        ) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '当前对话状态不允许置顶操作');
        }

        // 检查置顶状态是否有变化
        $currentIsTop = (bool)$session->is_top;
        if ($currentIsTop === $isTop) {
            $message = $isTop ? '对话已经是置顶状态' : '对话已经是非置顶状态';
            return [
                'sessionGuid' => $sessionGuid,
                'isTop' => $isTop,
                'message' => $message,
            ];
        }

        // 设置置顶状态
        $result = UserAiAgentChatSessionModel::setSessionTop($sessionGuid, $platformUserSysId, $isTop);

        if (!$result) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '设置对话置顶状态失败');
        }

        $message = $isTop ? '对话置顶成功' : '取消对话置顶成功';

        return [
            'sessionGuid' => $sessionGuid,
            'isTop' => $isTop,
            'topTime' => $isTop ? date('Y-m-d H:i:s') : '',
            'message' => $message,
        ];
    }

    /**
     * 检查并扣除用户点数
     * @param int $userId
     * @return int 扣除后的剩余点数
     * @throws ApiException
     */
    private function checkAndDeductUserPoints(int $userId): int
    {
        // 查询用户资产表中的点数
        $userAssets = \app\user\models\UserAssetsModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();

        $currentPoints = $userAssets->isEmpty() ? 0 : $userAssets->chatCount;

        // 检查点数是否足够
        if ($currentPoints <= 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '对话点数不足，请购买会员或充值点数');
        }

        // 扣除1点
        if ($userAssets->isEmpty()) {
            // 理论上不应该到这里，因为上面已经检查了点数不足
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户资产记录不存在');
        } else {
            $userAssets->chatCount = $userAssets->chatCount - 1;
            $userAssets->updateTime = time();
            $userAssets->save();
        }

        return $userAssets->chatCount;
    }

    /**
     * 获取用户点数
     * @param array $params
     * @return array
     */
    public function getUserPoints(array $params): array
    {
        $merchantGuid = $params['merchantGuid'];

        // 从token中获取用户ID
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $platformUserSysId = $tokenEntity->userId;

        if (empty($platformUserSysId)) {
            throwException(SysErrorCode::NOT_LOGIN, '用户未登录');
        }

        // 查询用户资产表中的点数
        $userAssets = \app\user\models\UserAssetsModel::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->findOrEmpty();

        $totalPoints = $userAssets->isEmpty() ? 0 : $userAssets->chatCount;

        return [
            'totalPoints' => $totalPoints,
            'totalPointsText' => $totalPoints . '点',
            'lastUpdateTime' => $userAssets->isEmpty() ? '' : date('Y-m-d H:i:s', $userAssets->updateTime)
        ];
    }
}
