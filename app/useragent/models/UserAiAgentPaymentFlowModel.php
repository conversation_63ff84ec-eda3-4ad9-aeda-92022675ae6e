<?php

declare(strict_types=1);

namespace app\useragent\models;

use app\libraries\models\BaseModel;
use think\model\relation\HasOne;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property int $platformUserSysId 用户id
 * @property int $flowType 流水类型：1-收入；2-支出
 * @property int $businessType 业务类型：1-智能体购买；2-佣金提现；3-平台手续费
 * @property string $businessOrderNo 业务订单号
 * @property int $amount 金额，单位：分
 * @property string $flowDesc 流水描述
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class UserAiAgentPaymentFlowModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'user_ai_agent_payment_flow';
    protected static bool $isGuid = true;

    // 流水类型常量
    public const FLOW_TYPE_INCOME = 1; // 收入
    public const FLOW_TYPE_EXPENSE = 2; // 支出

    public const FLOW_TYPE_TEXT = [
        self::FLOW_TYPE_INCOME => '收入',
        self::FLOW_TYPE_EXPENSE => '支出',
    ];

    // 业务类型常量
    public const BUSINESS_TYPE_PURCHASE = 1; // 智能体购买
    public const BUSINESS_TYPE_WITHDRAW = 2; // 佣金提现
    public const BUSINESS_TYPE_FEE = 3; // 平台手续费
    public const BUSINESS_TYPE_COMMISSION = 4; // 佣金收入
    public const BUSINESS_TYPE_MEMBERSHIP = 5; // 会员套餐
    public const BUSINESS_TYPE_CREATOR_SUBSCRIPTION = 6; // 创作者订阅
    public const BUSINESS_TYPE_CATEGORY_SUBSCRIPTION = 7; // 分类订阅

    public const BUSINESS_TYPE_TEXT = [
        self::BUSINESS_TYPE_PURCHASE => '智能体购买',
        self::BUSINESS_TYPE_WITHDRAW => '佣金提现',
        self::BUSINESS_TYPE_FEE => '平台手续费',
        self::BUSINESS_TYPE_COMMISSION => '佣金收入',
        self::BUSINESS_TYPE_MEMBERSHIP => '会员套餐',
        self::BUSINESS_TYPE_CREATOR_SUBSCRIPTION => '创作者订阅',
        self::BUSINESS_TYPE_CATEGORY_SUBSCRIPTION => '分类订阅',
    ];

    /**
     * 关联用户
     * @return HasOne
     */
    public function user(): HasOne
    {
        return $this->hasOne(\app\user\models\UsersModel::class, 'sys_id', 'platform_user_sys_id')
            ->field('sys_id,nickname,head_imgurl');
    }

    /**
     * 添加流水记录
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param int $flowType
     * @param int $businessType
     * @param string $businessOrderNo
     * @param int $amount
     * @param string $flowDesc
     * @return bool
     */
    public static function addFlow(
        int $platformUserSysId,
        string $merchantGuid,
        int $flowType,
        int $businessType,
        string $businessOrderNo,
        int $amount,
        string $flowDesc
    ): bool {
        $flow = new self();
        $flow->platformUserSysId = $platformUserSysId;
        $flow->merchantGuid = $merchantGuid;
        $flow->flowType = $flowType;
        $flow->businessType = $businessType;
        $flow->businessOrderNo = $businessOrderNo;
        $flow->amount = $amount;
        $flow->flowDesc = $flowDesc;
        
        return $flow->save();
    }

    /**
     * 获取用户流水记录
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param int $flowType
     * @param int $businessType
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getUserFlowList(
        int $platformUserSysId,
        string $merchantGuid,
        int $flowType = 0,
        int $businessType = 0,
        int $page = 1,
        int $limit = 20
    ): array {
        $query = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid);

        if ($flowType > 0) {
            $query->where('flow_type', $flowType);
        }

        if ($businessType > 0) {
            $query->where('business_type', $businessType);
        }

        $total = $query->count();
        $list = $query->order('create_time', 'desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
        ];
    }

    /**
     * 获取用户收支统计
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getUserFlowStats(
        int $platformUserSysId,
        string $merchantGuid,
        string $startDate = '',
        string $endDate = ''
    ): array {
        $query = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid);

        if (!empty($startDate)) {
            $query->where('create_time', '>=', strtotime($startDate . ' 00:00:00'));
        }

        if (!empty($endDate)) {
            $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
        }

        $stats = $query->field([
            'flow_type',
            'business_type',
            'SUM(amount) as total_amount',
            'COUNT(*) as count'
        ])
        ->group('flow_type,business_type')
        ->select()
        ->toArray();

        $result = [
            'total_income' => 0, // 总收入
            'total_expense' => 0, // 总支出
            'commission_income' => 0, // 佣金收入
            'withdraw_expense' => 0, // 提现支出
            'fee_expense' => 0, // 手续费支出
            'membership_expense' => 0, // 会员套餐支出
            'creator_subscription_expense' => 0, // 创作者订阅支出
            'category_subscription_expense' => 0, // 分类订阅支出
        ];

        foreach ($stats as $stat) {
            $amount = (int)$stat['totalAmount'];
            
            if ($stat['flowType'] == self::FLOW_TYPE_INCOME) {
                $result['total_income'] += $amount;
                if ($stat['businessType'] == self::BUSINESS_TYPE_COMMISSION) {
                    $result['commission_income'] += $amount;
                }
            } elseif ($stat['flowType'] == self::FLOW_TYPE_EXPENSE) {
                $result['total_expense'] += $amount;
                if ($stat['businessType'] == self::BUSINESS_TYPE_WITHDRAW) {
                    $result['withdraw_expense'] += $amount;
                } elseif ($stat['businessType'] == self::BUSINESS_TYPE_FEE) {
                    $result['fee_expense'] += $amount;
                } elseif ($stat['businessType'] == self::BUSINESS_TYPE_MEMBERSHIP) {
                    $result['membership_expense'] += $amount;
                } elseif ($stat['businessType'] == self::BUSINESS_TYPE_CREATOR_SUBSCRIPTION) {
                    $result['creator_subscription_expense'] += $amount;
                } elseif ($stat['businessType'] == self::BUSINESS_TYPE_CATEGORY_SUBSCRIPTION) {
                    $result['category_subscription_expense'] += $amount;
                }
            }
        }

        return $result;
    }

    /**
     * 获取用户月度收支趋势
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param int $months
     * @return array
     */
    public static function getUserMonthlyTrend(int $platformUserSysId, string $merchantGuid, int $months = 12): array
    {
        $endTime = time();
        $startTime = strtotime("-{$months} months", $endTime);

        $stats = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<=', $endTime)
            ->field([
                'FROM_UNIXTIME(create_time, "%Y-%m") as month',
                'flow_type',
                'SUM(amount) as total_amount'
            ])
            ->group('month,flow_type')
            ->order('month', 'asc')
            ->select()
            ->toArray();

        $result = [];
        foreach ($stats as $stat) {
            $month = $stat['month'];
            if (!isset($result[$month])) {
                $result[$month] = ['income' => 0, 'expense' => 0];
            }
            
            if ($stat['flowType'] == self::FLOW_TYPE_INCOME) {
                $result[$month]['income'] = (int)$stat['totalAmount'];
            } elseif ($stat['flowType'] == self::FLOW_TYPE_EXPENSE) {
                $result[$month]['expense'] = (int)$stat['totalAmount'];
            }
        }

        return $result;
    }
}
