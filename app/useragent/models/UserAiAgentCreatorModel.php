<?php

declare(strict_types=1);

namespace app\useragent\models;

use app\libraries\models\BaseModel;
use think\model\relation\HasMany;
use think\model\relation\HasOne;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property int $platformUserSysId 创作者用户id
 * @property string $creatorName 创作者名称
 * @property string $creatorAvatar 创作者头像
 * @property string $creatorDesc 创作者简介
 * @property array $creatorTags 创作者标签
 * @property int $subscriptionPrice 订阅价格，单位：分
 * @property int $originalPrice 原价，单位：分
 * @property int $agentCount 智能体数量
 * @property int $subscriberCount 订阅人数
 * @property int $sortOrder 排序权重
 * @property int $isHot 是否热门：0-否；1-是
 * @property int $isRecommended 是否推荐：0-否；1-是
 * @property int $status 状态：1-启用；2-禁用
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class UserAiAgentCreatorModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'user_ai_agent_creator';
    protected static bool $isGuid = true;

    // JSON字段
    protected $json = ['creator_tags'];

    // 状态常量
    public const STATUS_ENABLED = 1; // 启用
    public const STATUS_DISABLED = 2; // 禁用

    public const STATUS_TEXT = [
        self::STATUS_ENABLED => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    /**
     * 获取创作者头像完整URL
     * @param $value
     * @return string
     */
    public function getCreatorAvatarAttr($value)
    {
        if (empty($value)) {
            return '';
        }
        // 判断是否为完整的url地址，http或者https开头
        if (strpos($value, 'http') === false && strpos($value, 'https') === false) {
            return getPageUrl() . $value;
        }
        return $value;
    }

    /**
     * 关联用户信息
     * @return HasOne
     */
    public function user(): HasOne
    {
        return $this->hasOne(\app\user\models\UsersModel::class, 'sys_id', 'platform_user_sys_id')
            ->field('sys_id,nickname,head_imgurl,mobile');
    }

    /**
     * 关联订单
     * @return HasMany
     */
    public function orders(): HasMany
    {
        return $this->hasMany(UserAiAgentCreatorOrderModel::class, 'creator_guid', 'guid');
    }

    /**
     * 关联订阅
     * @return HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserAiAgentCreatorSubscriptionModel::class, 'creator_guid', 'guid');
    }

    /**
     * 关联智能体
     * @return HasMany
     */
    public function agents(): HasMany
    {
        return $this->hasMany(UserAiAgentModel::class, 'platform_user_sys_id', 'platform_user_sys_id')
            ->where('audit_status', UserAiAgentModel::AUDIT_STATUS_APPROVED)
            ->where('status', UserAiAgentModel::STATUS_NORMAL);
    }

    /**
     * 获取启用的创作者列表
     * @param string $merchantGuid
     * @return array
     */
    public static function getEnabledCreators(string $merchantGuid): array
    {
        return self::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_ENABLED)
            ->order('sort_order', 'asc')
            ->order('subscriber_count', 'desc')
            ->order('create_time', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 检查创作者是否存在且启用
     * @param string $creatorGuid
     * @param string $merchantGuid
     * @return bool
     */
    public static function isCreatorAvailable(string $creatorGuid, string $merchantGuid): bool
    {
        $count = self::getInstance()
            ->where('guid', $creatorGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_ENABLED)
            ->count();
        
        return $count > 0;
    }

    /**
     * 获取创作者详情
     * @param string $creatorGuid
     * @param string $merchantGuid
     * @return array
     */
    public static function getCreatorDetail(string $creatorGuid, string $merchantGuid): array
    {
        $creator = self::getInstance()
            ->where('guid', $creatorGuid)
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_ENABLED)
            ->with(['user'])
            ->findOrEmpty();
        
        return $creator->isEmpty() ? [] : $creator->toArray();
    }

    /**
     * 获取推荐创作者
     * @param string $merchantGuid
     * @return array
     */
    public static function getRecommendedCreators(string $merchantGuid): array
    {
        return self::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_ENABLED)
            ->where('is_recommended', 1)
            ->order('sort_order', 'asc')
            ->order('subscriber_count', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 获取热门创作者列表
     * @param string $merchantGuid
     * @return array
     */
    public static function getHotCreators(string $merchantGuid): array
    {
        return self::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_ENABLED)
            ->where('is_hot', 1)
            ->order('sort_order', 'asc')
            ->order('subscriber_count', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 更新智能体数量
     * @param string $creatorGuid
     * @return bool
     */
    public static function updateAgentCount(string $creatorGuid): bool
    {
        $creator = self::getInstance()->where('guid', $creatorGuid)->findOrEmpty();
        if ($creator->isEmpty()) {
            return false;
        }

        $agentCount = UserAiAgentModel::getInstance()
            ->where('platform_user_sys_id', $creator->platformUserSysId)
            ->where('audit_status', UserAiAgentModel::AUDIT_STATUS_APPROVED)
            ->where('status', UserAiAgentModel::STATUS_NORMAL)
            ->count();

        $creator->agentCount = $agentCount;
        $creator->updateTime = time();
        
        return $creator->save();
    }

    /**
     * 增加订阅人数
     * @param string $creatorGuid
     * @return bool
     */
    public static function increaseSubscriberCount(string $creatorGuid): bool
    {
        return self::getInstance()
            ->where('guid', $creatorGuid)
            ->inc('subscriber_count')
            ->update(['update_time' => time()]) > 0;
    }

    /**
     * 减少订阅人数
     * @param string $creatorGuid
     * @return bool
     */
    public static function decreaseSubscriberCount(string $creatorGuid): bool
    {
        return self::getInstance()
            ->where('guid', $creatorGuid)
            ->where('subscriber_count', '>', 0)
            ->dec('subscriber_count')
            ->update(['update_time' => time()]) > 0;
    }

    /**
     * 根据用户ID获取创作者信息
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @return array
     */
    public static function getCreatorByUserId(int $platformUserSysId, string $merchantGuid): array
    {
        $creator = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_ENABLED)
            ->findOrEmpty();
        
        return $creator->isEmpty() ? [] : $creator->toArray();
    }
}
