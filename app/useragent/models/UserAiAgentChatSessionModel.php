<?php

declare(strict_types=1);

namespace app\useragent\models;

use app\libraries\models\BaseModel;
use think\model\relation\HasOne;
use think\model\relation\HasMany;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property int $platformUserSysId 用户id
 * @property string $agentGuid 智能体guid
 * @property string $subscriptionGuid 订阅记录guid
 * @property string $sessionTitle 聊天轮次标题
 * @property string $sessionDesc 聊天轮次描述
 * @property int $sessionStatus 轮次状态：1-进行中；2-已结束；3-已删除
 * @property int $isTop 是否置顶：0-否；1-是
 * @property int $topTime 置顶时间
 * @property int $messageCount 消息总数
 * @property int $tokensUsed 消耗的token总数
 * @property int $lastMessageTime 最后消息时间
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class UserAiAgentChatSessionModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'user_ai_agent_chat_session';
    protected static bool $isGuid = true;

    // 轮次状态常量
    public const SESSION_STATUS_ACTIVE = 1; // 进行中
    public const SESSION_STATUS_ENDED = 2; // 已结束
    public const SESSION_STATUS_DELETED = 3; // 已删除

    public const SESSION_STATUS_TEXT = [
        self::SESSION_STATUS_ACTIVE => '进行中',
        self::SESSION_STATUS_ENDED => '已结束',
        self::SESSION_STATUS_DELETED => '已删除',
    ];

    // 置顶状态常量
    public const TOP_NO = 0;  // 不置顶
    public const TOP_YES = 1; // 置顶

    public const TOP_TEXT = [
        self::TOP_NO => '否',
        self::TOP_YES => '是',
    ];

    /**
     * 关联智能体
     * @return HasOne
     */
    public function agent(): HasOne
    {
        return $this->hasOne(UserAiAgentModel::class, 'guid', 'agent_guid')
            ->field('guid,agent_name,agent_avatar,agent_desc,agent_type');
    }

    /**
     * 关联订阅记录
     * @return HasOne
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(UserAiAgentSubscriptionModel::class, 'guid', 'subscription_guid')
            ->field('guid,subscription_type,expire_time');
    }

    /**
     * 关联聊天消息
     * @return HasMany
     */
    public function messages(): HasMany
    {
        return $this->hasMany(UserAiAgentChatMessageModel::class, 'session_guid', 'guid')
            ->order('message_index', 'asc');
    }

    /**
     * 创建新的聊天轮次
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param string $agentGuid
     * @param string $subscriptionGuid
     * @param string $sessionTitle
     * @return UserAiAgentChatSessionModel|null
     */
    public static function createSession(
        int $platformUserSysId,
        string $merchantGuid,
        string $agentGuid,
        string $subscriptionGuid,
        string $sessionTitle = ''
    ): ?UserAiAgentChatSessionModel {
        $session = new self();
        $session->platform_user_sys_id = $platformUserSysId;
        $session->merchant_guid = $merchantGuid;
        $session->agent_guid = $agentGuid;
        $session->subscription_guid = $subscriptionGuid;
        $session->session_title = $sessionTitle ?: '新对话';
        $session->session_desc = '';
        $session->session_status = self::SESSION_STATUS_ACTIVE;
        $session->message_count = 0;
        $session->tokens_used = 0;
        $session->last_message_time = time();

        return $session->save() ? $session : null;
    }

    /**
     * 获取用户的聊天轮次列表
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param string $agentGuid
     * @return array
     */
    public static function getUserSessions(int $platformUserSysId, string $merchantGuid, string $agentGuid = ''): array
    {
        $query = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('session_status', '<>', self::SESSION_STATUS_DELETED);

        if (!empty($agentGuid)) {
            $query->where('agent_guid', $agentGuid);
        }

        return $query->with(['agent'])
            ->order('last_message_time', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 获取用户与某个智能体的活跃轮次
     * @param int $platformUserSysId
     * @param string $agentGuid
     * @return UserAiAgentChatSessionModel|null
     */
    public static function getActiveSession(int $platformUserSysId, string $agentGuid): ?UserAiAgentChatSessionModel
    {
        $session = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('agent_guid', $agentGuid)
            ->where('session_status', self::SESSION_STATUS_ACTIVE)
            ->order('last_message_time', 'desc')
            ->findOrEmpty();

        return $session->isEmpty() ? null : $session;
    }

    /**
     * 更新轮次统计信息
     * @param string $sessionGuid
     * @param int $tokensUsed
     * @return bool
     */
    public static function updateSessionStats(string $sessionGuid, int $tokensUsed = 0): bool
    {
        $updateData = [
            'last_message_time' => time(),
        ];

        if ($tokensUsed > 0) {
            $updateData['tokens_used'] = ['inc', $tokensUsed];
        }

        $updateData['message_count'] = ['inc', 1];

        return self::getInstance()
            ->where('guid', $sessionGuid)
            ->update($updateData);
    }

    /**
     * 清空聊天轮次（删除所有消息但保留轮次）
     * @param string $sessionGuid
     * @return UserAiAgentChatSessionModel
     */
    public static function clearSession(string $sessionGuid): UserAiAgentChatSessionModel
    {
        // 删除所有消息
        UserAiAgentChatMessageModel::getInstance()
            ->where('session_guid', $sessionGuid)
            ->delete();

        // 重置轮次统计
        return self::getInstance()
            ->where('guid', $sessionGuid)
            ->update([
                'message_count' => 0,
                'tokens_used' => 0,
                'last_message_time' => time(),
            ]);
    }

    /**
     * 结束聊天轮次
     * @param string $sessionGuid
     * @return UserAiAgentChatSessionModel
     */
    public static function endSession(string $sessionGuid): UserAiAgentChatSessionModel
    {
        return self::getInstance()
            ->where('guid', $sessionGuid)
            ->update(['session_status' => self::SESSION_STATUS_ENDED]);
    }

    /**
     * 删除聊天轮次
     * @param string $sessionGuid
     * @return UserAiAgentChatSessionModel
     */
    public static function deleteSession(string $sessionGuid): UserAiAgentChatSessionModel
    {
        return self::getInstance()
            ->where('guid', $sessionGuid)
            ->update(['session_status' => self::SESSION_STATUS_DELETED]);
    }

    /**
     * 更新轮次标题
     * @param string $sessionGuid
     * @param string $sessionTitle
     * @return UserAiAgentChatSessionModel
     */
    public static function updateSessionTitle(string $sessionGuid, string $sessionTitle): UserAiAgentChatSessionModel
    {
        return self::getInstance()
            ->where('guid', $sessionGuid)
            ->update(['session_title' => $sessionTitle]);
    }

    /**
     * 获取轮次详情（包含消息）
     * @param string $sessionGuid
     * @param int $platformUserSysId
     * @return array
     */
    public static function getSessionDetail(string $sessionGuid, int $platformUserSysId): array
    {
        $session = self::getInstance()
            ->where('guid', $sessionGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('session_status', '<>', self::SESSION_STATUS_DELETED)
            ->with(['agent', 'messages'])
            ->findOrEmpty();

        return $session->isEmpty() ? [] : $session->toArray();
    }

    /**
     * 设置对话置顶
     * @param string $sessionGuid
     * @param int $platformUserSysId
     * @param bool $isTop
     * @return bool
     */
    public static function setSessionTop(string $sessionGuid, int $platformUserSysId, bool $isTop = true): bool
    {
        $updateData = [
            'is_top' => $isTop ? self::TOP_YES : self::TOP_NO,
            'top_time' => $isTop ? time() : 0,
            'update_time' => time(),
        ];

        return self::getInstance()
            ->where('guid', $sessionGuid)
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('session_status', '<>', self::SESSION_STATUS_DELETED)
            ->update($updateData) > 0;
    }

    /**
     * 获取用户的聊天轮次列表（支持置顶排序）
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param string $agentGuid
     * @return array
     */
    public static function getUserSessionsWithTop(int $platformUserSysId, string $merchantGuid, string $agentGuid = ''): array
    {
        $query = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('session_status', '<>', self::SESSION_STATUS_DELETED);

        if (!empty($agentGuid)) {
            $query->where('agent_guid', $agentGuid);
        }

        return $query->with(['agent'])
            ->order('is_top', 'desc')        // 置顶的在前
            ->order('top_time', 'desc')      // 置顶时间倒序
            ->order('last_message_time', 'desc')  // 最后消息时间倒序
            ->select()
            ->toArray();
    }
}
