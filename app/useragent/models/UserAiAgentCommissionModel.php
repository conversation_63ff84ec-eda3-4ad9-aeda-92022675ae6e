<?php

declare(strict_types=1);

namespace app\useragent\models;

use app\libraries\models\BaseModel;
use think\model\relation\HasOne;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property int $platformUserSysId 获得佣金的用户id
 * @property string $purchaseOrderGuid 购买订单guid
 * @property string $agentGuid 智能体guid
 * @property int $buyerUserId 购买者用户id
 * @property int $commissionType 佣金类型：1-邀请佣金；2-创作者收入
 * @property int $commissionAmount 佣金金额，单位：分
 * @property float $commissionRate 佣金比例
 * @property int $status 状态：1-待结算；2-已结算；3-已提现
 * @property int $settleTime 结算时间
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class UserAiAgentCommissionModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'user_ai_agent_commission';
    protected static bool $isGuid = true;

    // 佣金类型常量
    public const COMMISSION_TYPE_INVITATION = 1; // 邀请佣金
    public const COMMISSION_TYPE_CREATOR = 2; // 创作者收入

    public const COMMISSION_TYPE_TEXT = [
        self::COMMISSION_TYPE_INVITATION => '邀请佣金',
        self::COMMISSION_TYPE_CREATOR => '创作者收入',
    ];

    // 状态常量
    public const STATUS_PENDING = 1; // 待结算
    public const STATUS_SETTLED = 2; // 已结算
    public const STATUS_WITHDRAWN = 3; // 已提现

    public const STATUS_TEXT = [
        self::STATUS_PENDING => '待结算',
        self::STATUS_SETTLED => '已结算',
        self::STATUS_WITHDRAWN => '已提现',
    ];

    /**
     * 关联购买订单
     * @return HasOne
     */
    public function purchaseOrder(): HasOne
    {
        return $this->hasOne(UserAiAgentPurchaseModel::class, 'guid', 'purchase_order_guid')
            ->field('guid,order_no,pay_amount,pay_time');
    }

    /**
     * 关联智能体
     * @return HasOne
     */
    public function agent(): HasOne
    {
        return $this->hasOne(UserAiAgentModel::class, 'guid', 'agent_guid')
            ->field('guid,agent_name,agent_avatar');
    }

    /**
     * 关联获得佣金的用户
     * @return HasOne
     */
    public function commissionUser(): HasOne
    {
        return $this->hasOne(\app\user\models\UsersModel::class, 'sys_id', 'platform_user_sys_id')
            ->field('sys_id,nickname,head_imgurl,mobile');
    }

    /**
     * 关联购买者
     * @return HasOne
     */
    public function buyer(): HasOne
    {
        return $this->hasOne(\app\user\models\UsersModel::class, 'sys_id', 'buyer_user_id')
            ->field('sys_id,nickname,head_imgurl');
    }

    /**
     * 获取用户佣金记录
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @param int $commissionType
     * @return array
     */
    public static function getUserCommissionList(int $platformUserSysId, string $merchantGuid, int $commissionType = 0): array
    {
        $query = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid);

        if ($commissionType > 0) {
            $query->where('commission_type', $commissionType);
        }

        return $query->with(['agent', 'buyer', 'purchaseOrder'])
            ->order('create_time', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 获取用户可提现佣金总额
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @return int
     */
    public static function getAvailableCommission(int $platformUserSysId, string $merchantGuid): int
    {
        return (int)self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->where('status', self::STATUS_SETTLED)
            ->sum('commission_amount');
    }

    /**
     * 获取用户总收入统计
     * @param int $platformUserSysId
     * @param string $merchantGuid
     * @return array
     */
    public static function getUserIncomeStats(int $platformUserSysId, string $merchantGuid): array
    {
        $stats = self::getInstance()
            ->where('platform_user_sys_id', $platformUserSysId)
            ->where('merchant_guid', $merchantGuid)
            ->field([
                'commission_type',
                'status',
                'SUM(commission_amount) as total_amount',
                'COUNT(*) as count'
            ])
            ->group('commission_type,status')
            ->select()
            ->toArray();

        $result = [
            'invitation_income' => 0, // 邀请收入
            'creator_income' => 0, // 创作者收入
            'available_amount' => 0, // 可提现金额
            'withdrawn_amount' => 0, // 已提现金额
            'total_income' => 0, // 总收入
        ];

        foreach ($stats as $stat) {
            $amount = (int)$stat['totalAmount'];
            
            if ($stat['commissionType'] == self::COMMISSION_TYPE_INVITATION) {
                $result['invitation_income'] += $amount;
            } elseif ($stat['commissionType'] == self::COMMISSION_TYPE_CREATOR) {
                $result['creator_income'] += $amount;
            }

            if ($stat['status'] == self::STATUS_SETTLED) {
                $result['available_amount'] += $amount;
            } elseif ($stat['status'] == self::STATUS_WITHDRAWN) {
                $result['withdrawn_amount'] += $amount;
            }

            $result['total_income'] += $amount;
        }

        return $result;
    }
}
