<?php

declare(strict_types=1);

namespace app\useragent\controller\api;

use app\useragent\ApiBaseController;
use app\useragent\logic\api\AiAgentLogic;
use app\useragent\request\api\AiAgentRequest;

/**
 * 智能体API控制器
 */
class AiAgent extends ApiBaseController
{
    protected $middleware = [
        'token' => [
            'except' => [],
        ],
        'must_login' => [
            'except' => ['info'],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => [],
        ],
    ];

    /**
     * 创建智能体
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function create(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->create($request->param());
    }

    /**
     * 我的智能体列表
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function myList(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->myList($request->param());
    }

    /**
     * 我的智能体详情
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function myDetail(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->myDetail($request->param());
    }

    /**
     * 编辑修改我的智能体
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function update(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->update($request->param());
    }

    /**
     * 删除我的智能体
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function delete(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->delete($request->param());
    }

    /**
     * AI生成智能体头像
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function generateAvatar(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->generateAvatar($request->param());
    }

    /**
     * 智能体详情（基本信息）
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function agentDetail(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->agentDetail($request->param());
    }

    /**
     * 绑定邀请关系
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function bindInvitation(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->bindInvitation($request->param());
    }

    /**
     * 生成智能体小程序码
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function generateMiniCode(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->generateMiniCode($request->param());
    }
}
