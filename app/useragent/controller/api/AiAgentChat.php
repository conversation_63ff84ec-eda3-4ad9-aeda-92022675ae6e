<?php

declare(strict_types=1);

namespace app\useragent\controller\api;

use app\useragent\ApiBaseController;
use app\useragent\logic\api\AiAgentChatLogic;
use app\useragent\request\api\AiAgentChatRequest;

/**
 * 智能体对话API控制器
 */
class AiAgentChat extends ApiBaseController
{
    protected $middleware = [
        'token' => [
            'except' => ['sendOpen', 'sendAll'],
        ],
        'must_login' => [
            'except' => ['sendOpen', 'sendAll'],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => ['sendOpen', 'sendAll'],
        ],
    ];

    /**
     * 保存智能体聊天消息
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function saveMsg(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->saveMsg($request->param());
    }

    /**
     * 发送智能体聊天（流式）
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     */
    public function sendOpen(AiAgentChatRequest $request, AiAgentChatLogic $logic)
    {
        $logic->sendOpen($request->param());
        exit();
    }

    /**
     * 发送智能体聊天（非流式）
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function sendAll(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->sendAll($request->param());
    }

    /**
     * 我的智能体对话列表
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function mySessionList(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->mySessionList($request->param());
    }

    /**
     * 聊天消息列表
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function messageHistory(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->messageHistory($request->param());
    }

    /**
     * 修改对话标题
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function updateSessionTitle(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->updateSessionTitle($request->param());
    }

    /**
     * 删除聊天对话
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function deleteSession(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->deleteSession($request->param());
    }

    /**
     * 收藏智能体回复内容
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function collectMessage(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->collectMessage($request->param());
    }

    /**
     * 删除收藏的智能体回复内容
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function uncollectMessage(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->uncollectMessage($request->param());
    }

    /**
     * 删除聊天对话中的某一条消息
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function deleteMessage(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->deleteMessage($request->param());
    }

    /**
     * 删除聊天对话的所有消息
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function deleteAllMessages(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->deleteAllMessages($request->param());
    }

    /**
     * 我的智能体回复收藏列表
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function myCollectionList(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->myCollectionList($request->param());
    }

    /**
     * 设置对话置顶
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function setSessionTop(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->setSessionTop($request->param());
    }

    /**
     * 获取用户点数
     * @param AiAgentChatRequest $request
     * @param AiAgentChatLogic $logic
     * @return array
     */
    public function getUserPoints(AiAgentChatRequest $request, AiAgentChatLogic $logic): array
    {
        return $logic->getUserPoints($request->param());
    }
}
