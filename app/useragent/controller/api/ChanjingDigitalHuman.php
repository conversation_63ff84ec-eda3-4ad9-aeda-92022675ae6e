<?php

declare(strict_types=1);

namespace app\useragent\controller\api;

use app\useragent\ApiBaseController;
use app\useragent\request\api\ChanjingDigitalHumanRequest;
use app\useragent\logic\api\ChanjingDigitalHumanLogic;

/**
 * 蝉镜数字人控制器
 */
class ChanjingDigitalHuman extends ApiBaseController
{
    protected $middleware = [
        'token' => [
            'except' => [],
        ],
        'must_login' => [
            'except' => [
                'agentList',
                'calculatePoints',
                'getPointsConfig',
            ],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => [],
        ],
    ];

    /**
     * 获取公共形象列表
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function getPersonList(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->getPersonList($request->param());
    }

    /**
     * 获取定制形象列表
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function getCustomPersonList(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->getCustomPersonList($request->param());
    }

    /**
     * 创建视频合成任务
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function createVideo(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->createVideo($request->param());
    }

    /**
     * 获取视频详情
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function getVideoDetail(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->getVideoDetail($request->param());
    }

    /**
     * 我的数字人作品列表
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function getMyWorks(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->getMyWorks($request->param());
    }

    /**
     * 我的数字人形象列表
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function getMyPersonList(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->getMyPersonList($request->param());
    }

    /**
     * 创建数字人形象
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function createPerson(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->createPerson($request->param());
    }

    /**
     * 删除数字人形象
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function deletePerson(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->deletePerson($request->param());
    }

    /**
     * 计算数字人视频创作所需算力
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function calculatePoints(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->calculatePoints($request->param());
    }

    /**
     * 获取数字人算力配置信息
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function getPointsConfig(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->getPointsConfig($request->param());
    }

    /**
     * 批量删除数字人作品
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function batchDeleteWorks(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->batchDeleteWorks($request->param());
    }

    /**
     * 删除单个数字人作品
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function deleteWork(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->deleteWork($request->param());
    }

    /**
     * 恢复已删除的数字人作品
     * @param ChanjingDigitalHumanRequest $request
     * @param ChanjingDigitalHumanLogic $logic
     * @return array
     */
    public function restoreWork(ChanjingDigitalHumanRequest $request, ChanjingDigitalHumanLogic $logic): array
    {
        return $logic->restoreWork($request->param());
    }
}
