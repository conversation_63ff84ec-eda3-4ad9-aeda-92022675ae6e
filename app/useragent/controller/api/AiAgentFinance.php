<?php

declare(strict_types=1);

namespace app\useragent\controller\api;

use app\useragent\ApiBaseController;
use app\useragent\logic\api\AiAgentFinanceLogic;
use app\useragent\request\api\AiAgentFinanceRequest;

/**
 * 智能体财务模块控制器
 */
class AiAgentFinance extends ApiBaseController
{
    protected $middleware = [
        'token' => [
            'except' => [],
        ],
        'must_login' => [
            'except' => ['info'],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => [],
        ],
    ];

    /**
     * 提现记录列表
     * @param AiAgentFinanceRequest $request
     * @param AiAgentFinanceLogic $logic
     * @return array
     */
    public function withdrawalList(AiAgentFinanceRequest $request, AiAgentFinanceLogic $logic): array
    {
        return $logic->withdrawalList($request->param());
    }

    /**
     * 账变历史列表
     * @param AiAgentFinanceRequest $request
     * @param AiAgentFinanceLogic $logic
     * @return array
     */
    public function transactionHistory(AiAgentFinanceRequest $request, AiAgentFinanceLogic $logic): array
    {
        return $logic->transactionHistory($request->param());
    }

    /**
     * 我的收益
     * @param AiAgentFinanceRequest $request
     * @param AiAgentFinanceLogic $logic
     * @return array
     */
    public function myEarnings(AiAgentFinanceRequest $request, AiAgentFinanceLogic $logic): array
    {
        return $logic->myEarnings($request->param());
    }
}
