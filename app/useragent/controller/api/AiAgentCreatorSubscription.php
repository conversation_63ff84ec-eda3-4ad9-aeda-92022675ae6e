<?php

declare(strict_types=1);

namespace app\useragent\controller\api;

use app\useragent\ApiBaseController;
use app\useragent\logic\api\AiAgentCreatorSubscriptionLogic;
use app\useragent\request\api\AiAgentCreatorSubscriptionRequest;

/**
 * 创作者订阅相关接口控制器
 */
class AiAgentCreatorSubscription extends ApiBaseController
{
    protected $middleware = [
        'token' => [
            'except' => [],
        ],
        'must_login' => [
            'except' => ['info'],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => [],
        ],
    ];

    /**
     * 获取我的创作者订阅详情
     * @param AiAgentCreatorSubscriptionRequest $request
     * @param AiAgentCreatorSubscriptionLogic $logic
     * @return array
     */
    public function myCreatorSubscriptions(AiAgentCreatorSubscriptionRequest $request, AiAgentCreatorSubscriptionLogic $logic): array
    {
        return $logic->myCreatorSubscriptions($request->param());
    }

    /**
     * 获取创作者列表
     * @param AiAgentCreatorSubscriptionRequest $request
     * @param AiAgentCreatorSubscriptionLogic $logic
     * @return array
     */
    public function creatorList(AiAgentCreatorSubscriptionRequest $request, AiAgentCreatorSubscriptionLogic $logic): array
    {
        return $logic->creatorList($request->param());
    }

    /**
     * 购买创作者订阅
     * @param AiAgentCreatorSubscriptionRequest $request
     * @param AiAgentCreatorSubscriptionLogic $logic
     * @return array
     */
    public function purchaseCreatorSubscription(AiAgentCreatorSubscriptionRequest $request, AiAgentCreatorSubscriptionLogic $logic): array
    {
        return $logic->purchaseCreatorSubscription($request->param());
    }

    /**
     * 查询支付结果
     * @param AiAgentCreatorSubscriptionRequest $request
     * @param AiAgentCreatorSubscriptionLogic $logic
     * @return array
     */
    public function queryPayment(AiAgentCreatorSubscriptionRequest $request, AiAgentCreatorSubscriptionLogic $logic): array
    {
        return $logic->queryPayment($request->param());
    }

    /**
     * 获取我的创作者订单列表
     * @param AiAgentCreatorSubscriptionRequest $request
     * @param AiAgentCreatorSubscriptionLogic $logic
     * @return array
     */
    public function myCreatorOrders(AiAgentCreatorSubscriptionRequest $request, AiAgentCreatorSubscriptionLogic $logic): array
    {
        return $logic->myCreatorOrders($request->param());
    }

    /**
     * 取消创作者订阅
     * @param AiAgentCreatorSubscriptionRequest $request
     * @param AiAgentCreatorSubscriptionLogic $logic
     * @return array
     */
    public function cancelCreatorSubscription(AiAgentCreatorSubscriptionRequest $request, AiAgentCreatorSubscriptionLogic $logic): array
    {
        return $logic->cancelCreatorSubscription($request->param());
    }
}
