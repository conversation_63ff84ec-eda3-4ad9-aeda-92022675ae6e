<?php

declare(strict_types=1);

namespace app\useragent\controller\admin;

use app\useragent\AdminBaseController;
use app\useragent\logic\admin\AiAgentCreatorOrderLogic;
use app\useragent\request\admin\AiAgentCreatorOrderRequest;

/**
 * 创作者订阅订单管理控制器
 */
class AiAgentCreatorOrder extends AdminBaseController
{
    /**
     * 订单列表
     * @param AiAgentCreatorOrderRequest $request
     * @param AiAgentCreatorOrderLogic $logic
     * @return array
     */
    public function index(AiAgentCreatorOrderRequest $request, AiAgentCreatorOrderLogic $logic): array
    {
        return $logic->index($request->param());
    }

    /**
     * 订单详情
     * @param AiAgentCreatorOrderRequest $request
     * @param AiAgentCreatorOrderLogic $logic
     * @return array
     */
    public function detail(AiAgentCreatorOrderRequest $request, AiAgentCreatorOrderLogic $logic): array
    {
        return $logic->detail($request->param());
    }

    /**
     * 订单统计
     * @param AiAgentCreatorOrderRequest $request
     * @param AiAgentCreatorOrderLogic $logic
     * @return array
     */
    public function statistics(AiAgentCreatorOrderRequest $request, AiAgentCreatorOrderLogic $logic): array
    {
        return $logic->statistics($request->param());
    }

    /**
     * 导出订单
     * @param AiAgentCreatorOrderRequest $request
     * @param AiAgentCreatorOrderLogic $logic
     * @return array
     */
    public function export(AiAgentCreatorOrderRequest $request, AiAgentCreatorOrderLogic $logic): array
    {
        return $logic->export($request->param());
    }
}
