<?php

declare(strict_types=1);

namespace app\useragent\controller\admin;

use app\useragent\AdminBaseController;
use app\useragent\logic\admin\AiAgentLogic;
use app\useragent\request\admin\AiAgentRequest;

/**
 * 智能体管理控制器
 */
class AiAgent extends AdminBaseController
{
    /**
     * 智能体列表
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function index(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->index($request->param());
    }

    /**
     * 智能体详情
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function detail(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->detail($request->param());
    }

    /**
     * 审核智能体
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function audit(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->audit($request->param());
    }

    /**
     * 更新智能体状态（上架/下架）
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function updateStatus(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->updateStatus($request->param());
    }

    /**
     * 删除智能体
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function delete(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->delete($request->param());
    }

    /**
     * 修改智能体
     * @param AiAgentRequest $request
     * @param AiAgentLogic $logic
     * @return array
     */
    public function update(AiAgentRequest $request, AiAgentLogic $logic): array
    {
        return $logic->update($request->param());
    }
}
