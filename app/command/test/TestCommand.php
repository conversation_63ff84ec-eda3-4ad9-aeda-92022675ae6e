<?php

/*
 * @Author: 杨红兵
 * @Date: 2022-08-08 13:20:03
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-08-08 13:33:43
 */

namespace app\command\test;

use app\command\BaseCommand;
use app\constdir\SysErrorCode;
use app\libraries\service\ai_open\ai_chat\AiChatFactory;
use app\libraries\service\ai_open\ai_chat\entity\ChatCompleteEntity;
use app\libraries\service\ai_open\ai_img\AiImgFactory;
use app\libraries\service\ai_open\ai_img\entity\AiImgCreateEntity;
use app\libraries\service\ai_open\ai_img\lmp\MidjourneyImage;
use app\libraries\service\ai_open\ai_person_video\AiPersonVideoFactory;
use app\libraries\service\ai_open\ai_person_video\entity\Mp3VideoEntity;
use app\libraries\service\ai_open\ai_workflow\tools\ChatCompletionTool;
use app\libraries\service\ai_open\xiaoyi_open\AiShuzirenService;
use app\libraries\service\ai_open\xiaoyi_open\ImageService;
use app\libraries\service\ai_workflow\prompt\PromptBuildService;
use app\libraries\service\ai_workflow\tools\AzureAiTool;
use app\libraries\service\ai_workflow\tools\ZhipuTool;
use app\libraries\service\alipay\AlipayService;
use app\libraries\service\dify\DifyDatabaseService;
use app\libraries\service\dify\XinghuoDatabaseService;
use app\libraries\utils\CosService;
use app\square\models\UserChatGoodsOrdersModel;
use app\user\models\UsersModel;
use app\user\models\UserVideoOrderModel;
use app\user\service\UserService;
use app\zhanhui\task\ZhanhuiAiBuildTask;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use Overtrue\EasySms\EasySms;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Util\PemUtil;

require_once __DIR__ . '/../../../app/libraries/service/getID3/getid3/getid3.php';

class TestCommand extends BaseCommand
{
    protected bool $isRunWithSwoole = false;

    protected function configure()
    {
        $this->setName('test:test')
            ->setDescription('测试脚本');
    }

    public function handle()
    {
        // $this->contetMjTest();
        // return;
        // $value = 'https://replicate.delivery/pbxt/ar0KE3hAPPJTBhVz9eLBy170Hfe61wv6LgyduIft7ai1ocDIB/out-0.png';
        // $cosImgs =  CosService::upload($value, 'image/png', 'image', '.png');
        // var_dump($cosImgs);
        // return;
        // $this->xinghuoTest();
        // Queue::push(AiShuzirenJob::class, ['orderNo' => 'voice202403032329160001']);
        // $this->setVideoImg();
        //$this->sendGptImg();
        //$this->difyTest();
        //$this->xinghuoDtabaTest();
        //$this->aiToolsTest();
        // $this->bigVipTest();
        $this->newPayTest();
        return;
        // Queue::push(
        //     AiImgJob::class,
        //     [
        //         'orderNo' => 'img202312162230120001',
        //         // 'type' => 'upscale',
        //         // 'customId' => 'MJ::JOB::variation::2::f655b8e0-a967-4a24-81b1-19e17a12d0bf',
        //         // 'customIndex' => 0,
        //         // 'msgUniqueId' => '1702311999',
        //         // 'isChange' => 1
        //     ]
        // );
        // return;
        // $filename = public_path('static/mp3tmp/20240130') . '34021fda051d482aa86c15e823aebf1a.mp3';
        // $ret =  getID3Service::getAudioDuration($filename);
        // var_dump($ret);
        return;
        // $chatTest = new ChatTest();
        // $message = [
        //      [
        //          "role" => "user",
        //          "content" => "你好",
        //      ]
        // ];
        // $chatTest->doSend($message);

        // $imgageTest = new VedioText();
        // $imgageTest->doSearch('tlk_UdzL5Yr_rKOt3OixzP59F');

        // var_dump($this->getHeygen());

        // $heygenText = new HeygenTest();
        // $heygenText->doImg();
        // $heygenText->doSearch('6578e8666ce941f3a0a975b55dd53205');

        // $imgTest = new ImageTest();
        // $imgTest->doImg();
        // Queue::push(RoomChatJob::class, ['msgId' => 6]);
        // Queue::push(AiShuzirenJob::class, ['orderNo' => 'voice202310041833280001']);
        // $url = 'https://files.movio.la/aws_pacific/avatar_tmp/c844cc5703bc49e693aff723089c9ce8/3991880c84774f78860e5b40f2ef3cca.mp4?Expires=1698325978&Signature=SjQZ2hkG7MQy3c3E-iWUkCiiSM~akc8b3bNttbijxBGUYshAlvlbO3ehrdaMbUwoRqM-h957GnaEJZajy1rWUwL5JjJIu9PoJHcSLs-2gx4D7yjZ2C-4AZ6aCEPwjG-Li0e4Lu4NYon-pRE~syNks834Vo2EQXWLk4aSinMiEtNpCP7~56fIeeNdFX3RPLenLxxDhcsJqxw1BR0HA1NBXFGF4YeVJCmBzklHQawIXLMMRSbTP9xpsPjxp~uEMHTPdD12oCUg5MBB8EJE184wuLnykgkTIILv9itJ5ZPy8d73tZdLJPVE8moi3AjaPAA6WBoyFZ0whqr0Uk0-m4r1lg__&Key-Pair-Id=K49TZTO9GZI6K';
        // $result =  (new AiShuzirenJob())->cosXiangGang($url);
         // $result = (new AiPersonVideoFactory())->make('heygen')->getVideoResult('38b1937c37db4deeb33ab29c30cfa04a');
         //  var_dump($result['data']);

        // $this->alipayTest();
        // $url = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/shuziren/ea859ebf532c4c63b079e916becfec32.mp4';
        // $url = 'https://files.movio.la/aws_pacific/avatar_tmp/c844cc5703bc49e693aff723089c9ce8/b6007a3c8f3d4b7197687e64539162b4.mp4?Expires=1698152164&Signature=N2jNTAju8Ha~OtZB61Ft5k6MDfVXb3QsdIUn4dvIDuEVjaZRhRne-e9iUx~wJj~zGFks1cJK7CUdMdz0DdUEggfFnP0ShRJJZr~YhsYBz0Ym5NkPLqcXkWq5GIqnxnBq4Iw2Kxi9AiLhRBaYS9TlvrIER2F9gZXhKF4n6fhii3m~OYxLlIt4hdDbFGyvO6SyKUjYbMZw9BHRHf5ED5ncJYkdQtWNyTiS4zyHQcmsVJ~iD8r6ATjYMGxF9l8LGY~ipvej0~c3ptcbg~9poNEGwKbgR3yEG9OdpFZdm-N9D1TsA2kx~POy0w8JKbdeA8CyWepbn8Z0CfXOfg4jlOihwA__&Key-Pair-Id=K49TZTO9GZI6K';
        //$url = 'https://files.movio.la/aws_pacific/avatar_tmp/c844cc5703bc49e693aff723089c9ce8/66ad848d7c6a40aeb9ba7415cd715cff.mp4?Expires=1698150137&Signature=MMqwqQIAKeLdcVDtYhLo2SaMVGsKhlAOyGpnsVhaH1x97GpIkY8LIlpPYR4K2O-OFxTYfc242d3LurmjUCXZrYyYnxXIKj8OaqmoR8Q-ntMMVIdq6Y6SETJxZdbPvE73UIbHDUvQvnjcKkcj3O3UugY-AgY74AscJBAdUL3uA0lIt1iJZ7sEIxAsSycDhFh5kkF6sPmy9TrNw6~s1d7KE7AX4rwbo9KNrxi21Hr7JwbRtVimnBvjYDDpoUVOoqIx-Yi5~3EeKE5ppE8nzNe4JB4BJsIYIKAk2ckKVjS4I3hdvZEUcJkOVnkYnWzTXGaMLM2NkBdkJw~fHtTi-l2mZA__&Key-Pair-Id=K49TZTO9GZI6K';
        //$cosImgs = CosService::upload($url, 'video/mp4', 'video1', '.mp4');
        //var_dump($cosImgs);
        // $this->getVoiceList();
        //$ret = $this->httpSavelocal($url, '.mp4');
        //var_dump($ret);
        //$notifyUrl = config('app.app_url') . '/index/callback/alipayChatNotify';
        //$ret = (new AlipayService())->doWapPay('e108201b02ae42e686bcc4c302cbbd11','测试商品',
        //    0.01, 'chat202310151829200002',$notifyUrl);
        ////$ret = (new AlipayService())->queryOrder('e108201b02ae42e686bcc4c302cbbd11','chat202310151829200001');
        //var_dump($ret);
        // $url = 'https://files.movio.la/aws_pacific/avatar_tmp/c844cc5703bc49e693aff723089c9ce8/3991880c84774f78860e5b40f2ef3cca.mp4?Expires=1698325978&Signature=SjQZ2hkG7MQy3c3E-iWUkCiiSM~akc8b3bNttbijxBGUYshAlvlbO3ehrdaMbUwoRqM-h957GnaEJZajy1rWUwL5JjJIu9PoJHcSLs-2gx4D7yjZ2C-4AZ6aCEPwjG-Li0e4Lu4NYon-pRE~syNks834Vo2EQXWLk4aSinMiEtNpCP7~56fIeeNdFX3RPLenLxxDhcsJqxw1BR0HA1NBXFGF4YeVJCmBzklHQawIXLMMRSbTP9xpsPjxp~uEMHTPdD12oCUg5MBB8EJE184wuLnykgkTIILv9itJ5ZPy8d73tZdLJPVE8moi3AjaPAA6WBoyFZ0whqr0Uk0-m4r1lg__&Key-Pair-Id=K49TZTO9GZI6K';
        // $cosImgs =  CosService::upload($url, 'video/mp4', 'video1', '.mp4');
        // var_dump($cosImgs);
        //$videoId = 'a6c6e1544bac4e70b93fc653aaea697a';
        //$result = $this->queryVideoByProxy($videoId);
        //var_dump($result);

        // $this->mjImage();
    }

    public function queryVideoByProxy($videoId)
    {
        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET', 'https://api.heygen.com/v1/video_status.get?video_id=' . $videoId, [
            'headers' => [
                'accept' => 'application/json',
                'x-api-key' => config('chatgpt.heygen_api_key'),
            ],
            'timeout' => 1800, //超时时间
            // 'proxy' => 'https://us-pr.oxylabs.io:10001'
        ]);

        $res = $response->getBody();
        $res = json_decode($res, true);
        var_dump($res);
    }



    private function yongjin()
    {
        $orders =  UserChatGoodsOrdersModel::getInstance()
            ->where('order_status', UserChatGoodsOrdersModel::ORDER_STATUS_PAY)
            ->select()
            ->toArray();
        $userIds = array_column($orders, 'platformUserSysId');
        $userInfo = UsersModel::getInstance()->whereIn('sys_id', $userIds)->select()->toArray();
        $parentById = array_column($userInfo, 'parentUid', 'sysId');
        $changeCount = 0;
        foreach ($orders as $key => $value) {
            if (
                !empty($parentById[$value['platformUserSysId']])
                && $parentById[$value['platformUserSysId']] > 0
            ) {
                UserChatGoodsOrdersModel::getInstance()->where('sys_id', $value['sysId'])
                    ->update([
                        'parent_uid' => $parentById[$value['platformUserSysId']]
                    ]);
                $changeCount += 1;
            }
        }
        var_dump('修复数据【' . $changeCount . '】条');
        return [];
    }

    private function smsSend()
    {
        $config = config('easy_sms');
        $easySms = new EasySms($config);
        $ret = $easySms->send(18280435325, [
            'content'  => '您的验证码为: 6379',
            'template' => 'SMS_219752154',
            'data' => [
                'code' => 6379
            ],
        ]);
        var_dump($ret);
    }


    private function sendWeiruan()
    {
        $message = [
            [
                "role" => "user",
                "content" =>  '请你帮我写一个300字的春节文案',
            ]
        ];
        $postData = [
            "temperature" => 0.7,
              "top_p" => 0.95,
              "frequency_penalty" => 0,
              "presence_penalty" => 0,
              "max_tokens" => 800,
              "stop" => null,
              "stream" => true,
              "messages" => $message,
        ];
        $headers  = [
            'Accept: application/json',
            'Content-Type: application/json',
            'api-key: ********************************'
        ];
        $callback = function ($ch, $data) {
            $complete = json_decode($data);
            if (isset($complete->error)) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '访问失败');
            } else {
                echo $data;
                return strlen($data);
            }
            return strlen($data);
        };
        $url = 'https://xiaoyi-japan.openai.azure.com/openai/deployments/japan-xiaoyo-0926/chat/completions?api-version=2023-07-01-preview';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
        var_dump($postData);
        return curl_exec($ch);
    }


    public function sendGptImg()
    {
       // var_dump((new AiImgJob())->getEnglishText('Beautiful butterfly'));
       // return [];
        $imgText = '一位长相甜美的高中生妹妹，穿着校服，背着书包，站在校园里，微笑着看着镜头。';
        $imgEntity = new AiImgCreateEntity($imgText);
        $imgEntity->n = 1;
        $ret = (new AiImgFactory())->make('azureDall3')->createImg($imgEntity);
        var_dump($ret);
        return;
        // $url = "https://api.replicate.com/v1/predictions/rvjcvn3bug7sea26nf3wizixia";
        // return (new AiImgFactory())->make('replicateSdxl')->getImageResult($url);
    }

    public function sendGptChat()
    {
        $msg =  [
            [
                "role" => "user",
                "content" => "你好啊！"
            ]
        ];
        $chatEntity = new ChatCompleteEntity($msg);
        $chatEntity->stream = true;
        return (new AiChatFactory())->make('zhipu')->chatComplete($chatEntity);
    }

    public function videoBuild()
    {
        // $textEntity = new TextVideoEntity(
        //     'zh-CN-YunjianNeural',
        //     'https://api.yidongpaidui.com/upload/images/20230720/183U2NdvbdEV9SsYONKSzHfCRDZWlOb2KEqE4GPP.jpg',
        //     '大家好，我是阿洋，很高兴认识到家！希望大家能喜欢我这个数字人分身。'
        // );
        // $response = (new AiPersonVideoFactory())->make('did')->createTextVideo($textEntity);
        // var_dump($response);

        $mp3Entity = new Mp3VideoEntity(
            'https://api.yidongpaidui.com/upload/files/20230720/yaKx0S83orqxXpxSIdOUQ2MO1U4iqDTIqvW0iRMy.m4a',
            'https://api.yidongpaidui.com/upload/images/20230720/183U2NdvbdEV9SsYONKSzHfCRDZWlOb2KEqE4GPP.jpg',
        );
        $response = (new AiPersonVideoFactory())->make('did')->createMp3Video($mp3Entity);
        var_dump($response);

        //获取视频链接
        //tlk_QFKypom9tOzB0cs2dqspD
        $id = 'tlk_210D7EBZF-nJgS_zAN8FB';
        $response2 = (new AiPersonVideoFactory())->make('did')->getVideoResult($id);
        var_dump($response2);
        return [];
    }

    public function openImg()
    {
        $imageResult = (new ImageService())->buildImg(
            'img' . time(),
            'Summer cool water play scene, high-definition, ultra realistic, realistic portrait',
            2,
            '1024*1024',
        );
        // var_dump($imageResult);
        return $imageResult;
    }


    public function getVedioUrl()
    {
        return (new AiShuzirenService())->getResultUrl('tlk_xjOc1B8xpLn__v5OTgnCr', 'voice202307281953370001');
    }

    public function heygenTest()
    {
        // 文件路径
        $file_path = '3332.png';

// 目标URL
        $target_url = 'https://upload.heygen.com/v1/talking_photo';

// 创建文件对象
        $file = curl_file_create($file_path);

// 设置POST数据
        $post_data = array(
            'file' => $file
        );
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://upload.heygen.com/v1/talking_photo",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",

            CURLOPT_POSTFIELDS => $file_path,
            CURLOPT_HTTPHEADER => [
                "Content-Type: image/png",
                "X-API-KEY: MzI1Y2NjZGI4ZGIxNDFmMmE5ZjAwYzJjNTAzZWZjYmQtMTY5NDMxMjk4OA==",
                "accept: application/json"
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            echo $response;
        }
    }

    public function heygenTest2()
    {
        (new HeygenTest())->testv2();
    }

    public function heygetnTest()
    {
        $filePath =  __DIR__ . '/3332.png';
        $url = "https://upload.heygen.com/v1/talking_photo";

        $headers = array(
            "Content-Type: image/jpeg",
            "x-api-key: MzI1Y2NjZGI4ZGIxNDFmMmE5ZjAwYzJjNTAzZWZjYmQtMTY5NDMxMjk4OA=="
        );

        $ch = curl_init();
        $data = file_get_contents($filePath);

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Request Error:' . curl_error($ch);
        } else {
            $result = json_decode($response, true);
            print_r($result);
        }

        curl_close($ch);
    }

    public function hegenyMp4()
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.heygen.com/v1/video.generate",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode([
                'background' => 'https://api.yidongpaidui.com/upload/files/20230910/DBORNU5nWpK1ntL3JBj5r8E6fin8jQuwfXElzPLh.mp4',
                'ratio' => '16:9',
                'test' => false,
                'version' => 'v1alpha',
                'clips' => [
                    [
                        'avatar_id' => 'Daisy-inskirt-20220818',
                        'avatar_style' => 'normal',
                        'caption' => false,
                        'input_text' => '“惊呼！你闻到了吗？那飘逸的蟹香，那浓厚的醇香，它们在空气里跳跃，唤醒你的味蕾。你知道这是谁的呼唤吗？没错，这就是阳澄湖的螃蟹，这个秋季的美味使者。  阳澄湖螃蟹，这是怀揣着阳光和湖水的味道的螃蟹。它们在阳澄湖中肆意游弋，汲取着湖水的甘甜，沐浴着阳光的温暖。每一只阳澄湖螃蟹，都是大自然的恩赐，它们的每一口肉质都鲜美异常，每一滴蟹黄都浓郁无比。',
                        'scale' => 1,
                        'voice_id' => '43eb6d3dc5294a119aa17ab1d60444d1',
                        'talking_photo_style' => 'normal'
                    ]
                ]
            ]),
            CURLOPT_HTTPHEADER => [
                "accept: application/json",
                "content-type: application/json",
                "x-api-key: MzI1Y2NjZGI4ZGIxNDFmMmE5ZjAwYzJjNTAzZWZjYmQtMTY5NDMxMjk4OA=="
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            echo $response;
        }
    }

    public function getHeygen()
    {
        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', 'https://api.heygen.com/v1/video_status.get?video_id=d2361a37d0c4415eadf79afdfdf88eb6', [
            'headers' => [
                'accept' => 'application/json',
                'x-api-key' => 'MzI1Y2NjZGI4ZGIxNDFmMmE5ZjAwYzJjNTAzZWZjYmQtMTY5NDMxMjk4OA==',
            ],
        ]);

        echo $response->getBody();
    }

    public function testWenxin()
    {
        $chatEntity = new ChatCompleteEntity([
            [
                "role" => "system",
                "content" => "你好!从现在开始，你的名字叫小艺"
            ],
            [
                "role" => "user",
                "content" => "你好啊！请你介绍一下你自己"
            ]
        ]);
        $chatEntity->stream = true;
        $result = (new AiChatFactory())->make('zhipu')->chatComplete($chatEntity);
        var_dump($result);
    }

    function textGet()
    {
        $str = 'data: {"id":"as-1gx70bmmma","object":"chat.completion","created":1696506100,"sentence_id":1,"is_end":false,"is_truncated":false,"result":"大语言模型，我能够与人对话互动，回答问题，协助创作，高效便捷地帮助人们获取信息、知识和灵感。","need_clear_history":false,"usage":{"prompt_tokens":10,"completion_tokens":40,"total_tokens":61}}

data: {"id":"as-1gx70bmmma","object":"chat.completion","created":1696506100,"sentence_id":2,"is_end":true,"is_truncated":false,"result"';

        // 找到 "is_end" 所在的位置
        $isEndPos = strrpos($str, '"is_end":');

        // 从 "is_end" 的位置开始找到下一个逗号（,）或者大括号（}）
        $isEndValuePos = strpos($str, ',', $isEndPos);
        if ($isEndValuePos === false) {
            $isEndValuePos = strpos($str, '}', $isEndPos);
        }

        // 提取 "is_end" 的值
        $isEndValue = trim(substr($str, $isEndPos + 9, $isEndValuePos - $isEndPos - 9));

        echo "is_end 的值：$isEndValue\n";
    }

    public function fileToCos($url)
    {
        //获取链接后缀
        $suffix = strrchr($url, '.');
        $file = file_get_contents($url);
        $fileName = time() . rand(1111, 9999) . $suffix;
        $cos = new \Qcloud\Cos\Client([
            'region' => 'ap-guangzhou',
            'credentials' => [
                'secretId' => 'AKID1QXwAnHpHMPi8B6GVkcMmD8UxegOOhT3wjrHGH8lMcGym5L6tDWtz',
                'secretKey' => 'D8UxegOOhT3wjrHGH8lMcGym5L6tDWtz',
            ],
        ]);
        $result = $cos->putObject([
            'Bucket' => 'xiaoyi-1317629730',
            'Key' => $fileName,
            'Body' => $file
        ]);
        var_dump($result);
    }

    /**
     * Notes: 保存网络图片到本地
     * User: 浪子膏
     * Date: 2023/9/10 11:43
     * @param $url
     * @param $local
     * @param $file_name
     * @return false|string
     */
    public function httpTolocal($url, $local = './stoarge/topic/', $file_name = '')
    {
        $suffix = strrchr($url, '.');
        if (empty($file_name)) {
            $file_name = time() . rand(1000, 9999) . $suffix;
        }
        $local = $local . date('Ymd') . '/';
        $state = @file_get_contents($url, 0, null, 0, 1);//获取网络资源的字符内容
        if ($state) {
            if (!file_exists($local)) {
                mkdir($local, 0777, true);
            }
            $filename = $local . $file_name;//文件名称与路径
            ob_start();//打开输出
            readfile($url);//输出图片文件
            $img = ob_get_contents();//得到浏览器输出
            ob_end_clean();//清除输出并关闭
            $size = strlen($img);//得到图片大小
            if ($size > 4 * 1024 * 1024) {
                throwException(SysErrorCode::SYS_ERROR_BASE, '图片大小不能超过4m');
            }
            $fp2 = @fopen($filename, "a");
            fwrite($fp2, $img);//向当前目录写入图片文件，并重新命名
            fclose($fp2);
            return $filename;
        } else {
            return false;
        }
    }

    public function getVoiceList()
    {
        //从json文件导入
        $json =  fopen(__DIR__ . '/voice.json', 'r');
        $json = fread($json, filesize(__DIR__ . '/voice.json'));
        $voiceList = json_decode($json, true);
        $voiceList = $voiceList['data']['list'];
        $ableUseVoices = [];
        $english = [];
        foreach ($voiceList as $key => $value) {
            // if ($value['language'] == 'Chinese') {
            //     //口语
            //     $voiceLocal = '普通话';
            //     if ($value['locale'] == 'zh-HK') {
            //         $voiceLocal = '粤语';
            //     } elseif ($value['locale'] == 'zh-TW') {
            //         $voiceLocal = '台湾话';
            //     }
            //     $ableUseVoices[] = [
            //         'language' => $value['language'],
            //         'name' => ($value['gender'] == 'Female' ? '男性' : '女性') . '，' . $voiceLocal,
            //         'style' => $value['tags'],
            //         'value' => $value['voice_id'],
            //         'movio' => $value['preview']['movio']
            //     ];
            // }

            if ($value['language'] == 'English') {
                //口语
                $voiceLocal = '英语';
                $english[] = [
                    'language' => $value['language'],
                    'name' => ($value['gender'] == 'Female' ? '男性' : '女性') . '，' . $voiceLocal,
                    'style' => $value['tags'],
                    'value' => $value['voice_id'],
                    'movio' => $value['preview']['movio']
                ];
            }
        }
        //将内容输出为json文件
        $json = json_encode($english, JSON_UNESCAPED_UNICODE);
        $file = fopen(__DIR__ . '/englist_voice.json', 'w');
        fwrite($file, $json);
        fclose($file);
    }

    /**
     * 转存文件到本地
     * @param $url
     * @param $ext
     * @param $local
     * @return false|string
     */
    public function httpSavelocal($url, $ext, $local = '/upload/temp/')
    {
        $file_name = time() . rand(1000, 9999) . $ext;
        $saveLocal = public_path($local) . '/';

        // 创建包含查询参数的上下文流
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // 忽略SSL证书验证
        $state = curl_exec($curl);
        curl_close($curl);

        if ($state) {
            if (!file_exists($saveLocal)) {
                mkdir($saveLocal, 0777, true);
            }
            $saveFile = $saveLocal . $file_name;
            file_put_contents($saveFile, $state); // 直接写入文件而不使用缓冲区
            return $local . $file_name;
        } else {
            return false;
        }
    }

    public function alipayTest()
    {
        $notifyUrl = config('app.app_url') . '/index/callback/alipayChatNotify';
        $ret = (new AlipayService())
            ->doWapPay(
                'e108201b02ae42e686bcc4c302cbbd11',
                '测试商品',
                '0.01',
                'chat202310151829200001',
                $notifyUrl
            );
        echo $ret;
    }

    public function saveShuzirenHead()
    {
        $filePath = public_path('/static/') . 'shuzirenhead.json';
        $headList = file_get_contents($filePath);
        $headList = json_decode($headList, true)['data'];
        // 处理每一行数据重新写入一个新文件
        $newFilePath = public_path('/static/') . 'shuzirenhead_new.json';
        $newFileData = [];
        foreach ($headList as $key => $value) {
            $shuzhiren  = $value;
            $shuzhiren['img_url'] = CosService::upload($value['img_url'], 'image/jpeg', 'shuzirenhead', '.webp');
            $shuzhiren['video_url'] = CosService::upload($value['video_url'], 'video/mp4', 'shuzirenhead', '.mp4');
            var_dump($shuzhiren);
            $newFileData[] = $shuzhiren;
        }
        $newFileData = [
            'data' => $newFileData
        ];
        file_put_contents($newFilePath, json_encode($newFileData, JSON_UNESCAPED_UNICODE));
        return;
    }

    public function testShuzirenHead()
    {
        $newFilePath = public_path('/static/') . 'shuzirenhead_new.json';
        $headList = file_get_contents($newFilePath);
        $headList = json_decode($headList, true);
        $showList = [];
        foreach ($headList['data'] as $key => $value) {
            $showList[] = [
                'avatar_id' => $value['avatar_id'],
                'img_url' => $value['img_url']['data'],
                'video_url' => $value['video_url']['data']
            ];
        }
        var_dump($showList);
    }

    public function createVideoByProxy($data)
    {
        $options = [
            'verify' => false,
            'json' => $data,
            'timeout' => 1800, // 设置超时时间为5秒
        ];
        // 创建一个cURL资源
        $client = new \GuzzleHttp\Client();
        $response = $client->request('POST', 'https://xiaoyi.aiaskbot.cn/index/index/creaetHeygenVideo', $options);

        $res = $response->getBody();
        $res = json_decode($res, true);
        if (empty($res['data'])) {
            return [];
        }
        return $res['data'];
    }

    public function getVideoResult(string $videoId, $queryCount = 0): array
    {
        // var_dump('查询id：' . $videoId);
        if ($queryCount > 100) {
            throw new \Exception('获取创作结果失败，请稍后重试');
        }
        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', 'https://api.heygen.com/v1/video_status.get?video_id=' . $videoId, [
            'headers' => [
                'accept' => 'application/json',
                'x-api-key' => config('chatgpt.heygen_api_key'),
            ],
            'timeout' => 1800, //超时时间
        ]);

        $res = $response->getBody();
        $res = json_decode($res, true);
        if (!$res) {
            throw new \Exception('获取创作结果失败，请稍后重试');
        }
        if ($res['data']['status'] == 'failed') {
            throw new \Exception('获取创作结果失败，请稍后重试');
        }
        if ($res['data']['status'] == 'processing') {
            sleep(10);
            $queryCount++;
            return $this->getVideoResult($videoId, $queryCount);
        }
        return [
            'status' => 'success',
            'url' => $res['data']['video_url'] ?? ''
        ];
    }

    public function mjImage()
    {
        var_dump('开始执行');
        $discordChannelId = '1170920909545148530';
        $discordUserToken = 'MTE2NjE4MTY5Mzg0NjE5NjI0Ng.GE9eCi.p8r0OOQeROjpC4vIoErCbx7z8jxntQXAB1xodw';

        $midjourney = new MidjourneyImage($discordChannelId, $discordUserToken);

        // Example of a prompt: text is separated from tags
        $promptText = "a 3d render of a futuristic cityscape at night";
        $promptTags = "8k octane render, photorealistic --ar 9:20 --v 5";

        /**
         * The imageCreationV2 method is responsible for randomly selecting an image from the 4 options provided by Midjourney.
         * If you want to specify a particular image, you can pass its identifier (ranging from 0 to 3) as the third parameter.
         *
         * Example: $midjourneyImageCreator->imageCreation($promptText, $promptTags, 0);
         *
         * This will generate an image for the given prompt, using the specified image identifier (in this case, 0).
         */
        $imgText = 'Summer cool water play scene, high-definition, ultra realistic, realistic portrait, bikini beauty';
        $promptTags = "8k octane render, photorealistic --ar 9:20 --v 5";
         $midjourney->testChange();
    }

    public function contetMjTest()
    {
        // var_dump( str_contains($msg, "1702218391"));
    }

    public function voiceTest()
    {
        $ch = curl_init();

        $speech_region = "japaneast";
        $speech_key = "cbbd62f54ccd40dd9ab7ff7ca8d448be";
        $content = '好没有意思的话，你真的是太过分了！这样太让我伤心了！';
        $voiceValue = 'zh-CN-YunjianNeural';
        $ssml = '
                <speak version="1.0" xml:lang="en-US">
                <voice xml:lang="en-US" xml:gender="Female" name="' . $voiceValue . '">' . $content . '</voice>
                </speak>';

        curl_setopt($ch, CURLOPT_URL, "https://{$speech_region}.tts.speech.microsoft.com/cognitiveservices/v1");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $ssml);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Ocp-Apim-Subscription-Key: {$speech_key}",
            'Content-Type: application/ssml+xml',
            'X-Microsoft-OutputFormat: audio-16khz-128kbitrate-mono-mp3',
            'User-Agent: PHP-curl'
        ));
        $result = curl_exec($ch);

        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }

        curl_close($ch);
        $dayPath = public_path('/static/mp3tmp/') . date('Ymd') . '/';
        if (!file_exists($dayPath)) {
            mkdir($dayPath, 0777, true);
        }
        $savePath = $dayPath . get_guid() . '.mp3';
        file_put_contents($savePath, $result);
        // 返回访问路径
        return config('app.upload_image_url') . '/static/mp3tmp/' . date('Ymd') . '/' . basename($savePath);
    }

    public function voiceToText()
    {
        $audio_file = 'https://cvoiceprodjpe.blob.core.windows.net/acc-public-files/582c426c3b9f4ebf84f15baa17e4bc9b/b8cdef26-deff-46e1-9abb-a54f28f6e9e5.wav';
        $speech_region = 'japaneast';
        $speech_key = 'cbbd62f54ccd40dd9ab7ff7ca8d448be';
        $data = file_get_contents($audio_file);
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://{$speech_region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1?language=zh-CN&format=detailed");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: audio/wav",
            "Ocp-Apim-Subscription-Key: {$speech_key}"
        ));

        $output = curl_exec($ch);

        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $voiceData = json_decode($output, true);
        return  $voiceData['DisplayText'] ?? '';
    }

    public function hunyuanTest()
    {
    }

    public function xinghuoTest()
    {
        $msg =  [
            [
                "role" => "user",
                "content" => "你好啊！你是什么模型"
            ]
        ];
        $chatEntity = new ChatCompleteEntity($msg);
        $chatEntity->stream = true;
        return (new AiChatFactory())->make('xinghuo')->chatComplete($chatEntity);
    }

    public function mp4img()
    {
        $ffmpeg = FFMpeg::create();
        // $video = $ffmpeg->open(__DIR__ . '/' . 'test.mp4');
        $video = $ffmpeg->open('https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/video1/fa20ce3e5389476bb51e6deb2a34a4fb.mp4');
        $frame = $video->frame(TimeCode::fromSeconds(1));
        $frame->save(__DIR__ . '/' . 'image22.jpg');
    }

    public function setVideoImg()
    {
        $allOrder = UserVideoOrderModel::getInstance()
            ->whereNotNull('video_result')
            ->whereNull('video_img')
            ->where('order_status', 'success')
            ->select();
        foreach ($allOrder as $info) {
            try {
                var_dump('开始转换');
                var_dump($info['video_result']);
                // 获取视频第一帧作为封面
                $ffmpeg = FFMpeg::create();
                $video = $ffmpeg->open($info['video_result']);
                $frame = $video->frame(TimeCode::fromSeconds(1));
                $videImgPath = time() . rand(1111, 9999) . '.jpg';
                $videoImg = public_path() . $videImgPath;
                var_dump($videoImg);
                $frame->save($videoImg);
                $videoImgCos = CosService::uploadLocal($videImgPath, 'image/jpeg', 'image');
                $info->videoImg = $videoImgCos['data'] ?? '';
                $info->save();
                var_dump('转换成功:' . $info->videoImg);
                sleep(1);
            } catch (\Exception $exception) {
                var_dump('失败一个：' . $exception->getMessage());
            }
        }
    }

    public function difyTest()
    {
        // 创建知识库
        //$result = DifyDatabaseService::getInstance()->createDatabase('测试知识库');
        // 知识库列表
        //$result = DifyDatabaseService::getInstance()->getDatabaseList();
        // 上传知识库文档
        //$result = DifyDatabaseService::getInstance()->createDocumentByFile(
        //    'ea9c29ce-8762-4300-bc57-7ceb3496117c',
        //    'https://xuzy-chengdu-public.oss-cn-chengdu.aliyuncs.com/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF%E7%BB%8F%E7%90%86%E5%B2%97%E4%BD%8D%E8%AF%B4%E6%98%8E%E4%B9%A6.docx'
        //);
        // 文档列表
        //$result = DifyDatabaseService::getInstance()->getDocumentList('ea9c29ce-8762-4300-bc57-7ceb3496117c');
        // 更新文档
        //$result = DifyDatabaseService::getInstance()->updateDocumentByFile(
        //    'ea9c29ce-8762-4300-bc57-7ceb3496117c',
        //    '87d3de77-ce46-4a9f-8ff0-ae10b6e52532',
        //    '后端技术经理岗位说明书2.docx',
        //    public_path().'/static/后端技术经理岗位说明书.docx'
        //);
        // 删除文档
        //$result = DifyDatabaseService::getInstance()->deleteDocument(
        //    'ea9c29ce-8762-4300-bc57-7ceb3496117c',
        //    '87d3de77-ce46-4a9f-8ff0-ae10b6e52532'
        //);
        // 查询文档
        //$result = DifyDatabaseService::getInstance()->getDocumentSegments(
        //  'ea9c29ce-8762-4300-bc57-7ceb3496117c',
        //  'd8ab3b32-d85f-4d4a-af12-f5bf2efb4f86',
        //    '技术要求'
        //);
        // 通过控制台搜索文档
        //$result = DifyDatabaseService::getInstance()->databaseSearchByConsole(
        //    '447eb19f-1221-4b1a-8456-bf31c0c8ea6f',
        //    '你好'
        //);
        // 获取文档分段列表
        //$result = DifyDatabaseService::getInstance()->getDocumentSegments(
        //    'ea9c29ce-8762-4300-bc57-7ceb3496117c',
        //    '0eccda11-d67a-470e-8d16-323487d682c4'
        //);
        // 修改分段
        // $segmentsDataExample = [
        //     'segment' => [
        //         'content' => '怎么做好一个后端？' . "\n" . '抱紧阿洋大腿，好好学，好好看，就稳了',
        //         'answer' => '怎么做好一个后端',
        //         'keywords' => ['后端', '做好'],
        //         'enable' => true,
        //     ]
        // ];
        // $result = DifyDatabaseService::getInstance()->updateSegments(
        //     'ea9c29ce-8762-4300-bc57-7ceb3496117c',
        //     'a27e8348-f5cc-4c28-b34f-46dc73efd38b',
        //     '0a2f420b-f83b-41f9-8d35-314b55c4b16d',
        //     $segmentsDataExample
        // );
        //var_dump($result);
    }

    public function ttshd()
    {
//        curl https://xiaoyi-swden.openai.azure.com/openai/deployments/xiaoyi-ttshd/audio/speech?api-version=2024-02-15-preview \
// -H "api-key: ********************************" \
// -H "Content-Type: application/json" \
// -d '{
//    "model": "tts-1-hd",
//    "input": "你好小艺！很高兴认识你！我是一个有用的助手，可以帮助您回答问题、提供信息和执行操作。无论您在学习、工作还是生活中遇到什么困难或需要帮助的地方，我都会尽力为您提供支持。请随时告诉我您需要什么帮助，我会尽力满足您的需求！",
//    "voice": "fable"
//}' --output speech2.mp3
    }

    public function bigVipTest()
    {
        $userModel = UsersModel::getInstance()->where('sys_id', 199999)->findOrEmpty();
        dd($userModel->isEmpty());
        //增加注册奖励
        $registerCode = 'T2W8VC';
        $registerCodeType = 'bigVip';
        UserService::getInstance()->registerReward($userModel->sysId, $registerCode, $registerCodeType);
    }

    public function xinghuoDtabaTest()
    {
        $fileUrl = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/file/%E7%BA%AF%E6%96%87%E5%AD%97%E7%9A%84%E6%A8%A1%E6%9D%BF.pdf';
        $xinghuoDatabaseService = new XinghuoDatabaseService();
        $result = (new XinghuoDatabaseService())->uploadFile($fileUrl);
        //$fileId = '83aa95b9aa614cb5b27e84045b12123f';
        //$result = $xinghuoDatabaseService->fileStatusQuery($fileId);
        //$result = $xinghuoDatabaseService->fileQA($fileId, '推荐律师有哪些？');
        //    $result = $xinghuoDatabaseService->fileSimilarSearch($fileId, '李晓红');
        //$result = $xinghuoDatabaseService->fileChunkLists($fileId);
        //$result = $xinghuoDatabaseService->fileLists();
        dd($result);
    }

    public function aiToolsTest()
    {
        //$result = ZhipuTool::getInstance()->completeText('你好');
        // 生成logo
        // 生成封logo文案
        $themeDesc = '
亚太鹏盛税务师事务所是一家具有卓越业绩和专业资质的全国AAAAA级税务师事务所，连续7年在全国排名前十。自2007年成立以来，我们已在全国设立了近80家分公司，并被中税协指定为资本市场涉税业务培训基地。在疫情期间，我们始终保持匀速增长，整体业绩营收已达5.6亿元。

我们的专业团队拥有丰富的经验和深厚的专业知识，能够为客户提供全方位的税务服务。我们的执业资质等级级别及有效期限均符合行业标准，确保了我们的服务质量和专业性。

作为一家领先的税务师事务所，我们致力于为客户提供最优质的服务，帮助他们解决复杂的税务问题，实现税收优化。我们期待与您合作，共同创造更大的价值。';
        // $coverPrompt = PromptBuildService::getInstance()->zhanhuiCoverDesign($themeDesc);
        // $coverPromptText = ZhipuTool::getInstance()->completeText($coverPrompt);
        // var_dump($coverPromptText);
        // // 生成logo
        // $logoImg = AzureAiTool::getInstance()->dall3ImgBuild($coverPromptText);
        // var_dump($logoImg);

        $zhanhuiTask = new ZhanhuiAiBuildTask();
        $zhanhuiTask->execute();

       // $result =  AzureAiTool::getInstance()->gpt4oChatComplete('你好');
       // dd($result);
    }

    public function newPayTest()
    {
        // 文档地址： https://pay.weixin.qq.com/doc/v3/merchant/4012716434

        // 设置参数
        $defaultPath = env('APP.APP_PATH', '') . '/app/merchant/cert/wechat';
        $merchantId = '1645520595';
        $merchantPrivateKeyFilePath = 'file://' . $defaultPath . '/e108201b02ae42e686bcc4c302cbbd11/' . 'apiclient_key.pem';
        $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
        $merchantCertificateSerial = '36A85EE94718173290D10942FA7444536B012D8A';
        $platformCertificateFilePath = 'file://' . $defaultPath . '/e108201b02ae42e686bcc4c302cbbd11/' . 'platform_cert.pem';
        $platformPublicKeyInstance = Rsa::from($platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);
        $platformCertificateSerial = PemUtil::parseCertificateSerialNo($platformCertificateFilePath);
        $instance = Builder::factory([
            'mchid'      => $merchantId,
            'serial'     => $merchantCertificateSerial,
            'privateKey' => $merchantPrivateKeyInstance,
            'certs'      => [
                $platformCertificateSerial => $platformPublicKeyInstance,
            ],
        ]);

        try {
            $requestParam = [
                'appid' => 'wx1d5d60eb69be794f', //申请商户号的appid或商户号绑定的appid（企业号corpid即为此appid）
                'out_bill_no' => 'testpay' . date('YmdHis') . rand(1000, 9999), //商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
                'transfer_scene_id' => '1005', // 该笔转账使用的转账场景，可前往“商户平台-产品中心-商家转账”中申请。如：1000（现金营销），1006（企业报销）等
                'openid' => 'oTW0C7Zx56NbrigQJjETQZcb2XS4', //商户appid下，某用户的openid
                'transfer_amount' => 30, //转账金额单位为“分”。转账总金额必须与批次内所有明细转账金额之和保持一致，否则无法发起转账操作
                'transfer_remark' => '活动奖励',
                //'notify_url' => config('app.url') . '/api/finance/transfer/callback', //回调地址
                'user_recv_perception' => '劳务报酬',
                'transfer_scene_report_infos' => [
                    [
                        'info_type' => '岗位类型',
                        'info_content' => '平台创作者'
                    ],
                    [
                        'info_type' => '报酬说明',
                        'info_content' => '报酬提现'
                    ],
                ]
            ];

            //$requestParam = json_encode($requestParam, JSON_UNESCAPED_UNICODE);
            // 发起商家转账
            $resp = $instance->chain('/v3/fund-app/mch-transfer/transfer-bills')->post(['json' => $requestParam]);
            echo $resp->getBody(), PHP_EOL;

            // 返回示例：{"create_time":"2025-06-26T16:55:32+08:00","out_bill_no":"testpay202506261655372647","package_info":"ABBQO+oYAAABAAAAAAC/9vYMizrxSLNVBQtdaBAAAADnGpepZahT9IkJjn90+1qgx1JbeMXXWMdAwlvmjZR5L8l4zrnHNPGpPFh2SbdWJE484ZVjHl70Ik21t5PtIPfMsnrLLFDC21EXza9c+ZrfzDJtSDw=","state":"WAIT_USER_CONFIRM","transfer_bill_no":"1330001712352862506260009005588832"}
        } catch (\Exception $exception) {
            $responseBody = $exception->getResponse()->getBody();
            $responseBodyArray = json_decode($responseBody, true);
            dd($responseBodyArray);
        }
    }
}
