<?php
/*
 * @Author: 杨红兵
 * @Date: 2022-10-03 11:10:42
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-10-03 11:29:45
 */

namespace app;

use app\libraries\utils\Util;
use InvalidArgumentException;
use LogicException;
use think\Console as ThinkConsole;
use think\console\Command;
use think\console\command\Clear;
use think\console\command\Help;
use think\console\command\Lists;
use think\console\command\make\Command as MakeCommand;
use think\console\command\make\Controller;
use think\console\command\make\Event;
use think\console\command\make\Listener;
use think\console\command\make\Middleware;
use think\console\command\make\Model;
use think\console\command\make\Service;
use think\console\command\make\Subscribe;
use think\console\command\make\Validate;
use think\console\command\optimize\Route;
use think\console\command\optimize\Schema;
use think\console\command\RouteList;
use think\console\command\RunServer;
use think\console\command\ServiceDiscover;
use think\console\command\VendorPublish;
use think\console\command\Version;
use think\console\Input;
use Throwable;

class Console extends ThinkConsole
{
    /**
     * 在生产环境下的command命令
     *
     * @var string
     * <AUTHOR>
     * @DateTime 2022-10-03 17:43:33
     *
     */
    protected string $prodCmdName = "";

    protected $defaultCommands = [
        'list' => Lists::class,
        'optimize:route' => Route::class,
        'optimize:schema' => Schema::class,
        'route:list' => RouteList::class,
    ];

    /**
     * 需要加载的命令行，可能为开发环境被过虑的命令行
     *
     * @var array
     * <AUTHOR>
     * @DateTime 2022-10-03 08:26:35
     *
     */
    protected $needAddCommands = [];

    protected function loadCommands(): void
    {
        if (!Util::isProd()) {
            $this->defaultCommands = [
                'help' => Help::class,
                'list' => Lists::class,
                'clear' => Clear::class,
                'make:command' => MakeCommand::class,
                'make:controller' => Controller::class,
                'make:model' => Model::class,
                'make:middleware' => Middleware::class,
                'make:validate' => Validate::class,
                'make:event' => Event::class,
                'make:listener' => Listener::class,
                'make:service' => Service::class,
                'make:subscribe' => Subscribe::class,
                'optimize:route' => Route::class,
                'optimize:schema' => Schema::class,
                'run' => RunServer::class,
                'version' => Version::class,
                'route:list' => RouteList::class,
                'service:discover' => ServiceDiscover::class,
                'vendor:publish' => VendorPublish::class,
            ];
        } elseif (empty($this->prodCmdName)) {
            try {
                $input = new Input();
                $this->prodCmdName = $this->getCommandName($input);
            } catch (Throwable $ex) {
                unset($ex);
                $this->prodCmdName = "all_not_load";
            }
        }
        parent::loadCommands();
    }

    /**
     * 添加一个指令
     * @access public
     * @param string|Command $command 指令对象或者指令类名
     * @param string $name 指令名 留空则自动获取
     * @return Command|void
     */
    public function addCommand($command, string $name = '')
    {
        //当为线上环境时，且命令行不是list，就只载入当前执行命令
        if (!empty($this->prodCmdName) && $this->prodCmdName !== "list" && $name !== $this->prodCmdName) {
            $this->needAddCommands[$name] = $command;
            return null;
        }
        return $this->addCommandOwer($command, $name);
    }
    protected function addCommandOwer($command, string $name = '')
    {
        if ($name) {
            return $this->addCommandByName($command, $name);
        }

        if (is_string($command)) {
            $command = $this->app->invokeClass($command);
        }
        if (!empty($this->commands[$command->getName()]) && !config('console.loadCover')) {
            return null;
        }
        return $this->addCommandByClass($command);
    }

    /**
     * 判断命令是否已存在
     *
     * <AUTHOR>
     * @DateTime 2022-10-03 11:20:57
     *
     * @param string|Command $command
     * @param string $name
     * @return void
     */
    protected function addCommandByName($command, $name)
    {
        if (!empty($this->commands[$name]) && !config('console.loadCover')) {
            return;
        }
        $this->commands[$name] = $command;
    }

    /**
     * 通过类名添加命令行
     *
     * <AUTHOR>
     * @DateTime 2022-10-03 11:25:04
     *
     * @param Command $command
     * @return Command|void
     */
    protected function addCommandByClass($command)
    {
        $command->setConsole($this);

        if (!$command->isEnabled()) {
            $command->setConsole(null);
            return null;
        }

        $command->setApp($this->app);

        if (null === $command->getDefinition()) {
            throw new LogicException(
                sprintf(
                    'Command class "%s" is not correctly initialized. You probably forgot to call the parent constructor.',
                    get_class($command)
                )
            );
        }

        $this->commands[$command->getName()] = $command;

        foreach ($command->getAliases() as $alias) {
            $this->commands[$alias] = $command;
        }

        return $command;
    }

    /**
     * 查找指令
     * @access public
     * @param string $name 名称或者别名
     * @return Command
     * @throws InvalidArgumentException
     */
    public function find(string $name): Command
    {
        if (!$this->hasCommand($name) && isset($this->needAddCommands[$name])) {
            $this->addCommandOwer($this->needAddCommands[$name], $name);
        }
        return parent::find($name);
    }
}
