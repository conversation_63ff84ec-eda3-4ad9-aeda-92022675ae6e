CREATE TABLE `user_image_order` (
    `sys_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid` char(32) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` varchar(255) NOT NULL default '' COMMENT '商家guid',
    `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
    `order_no` varchar(255) NOT NULL COMMENT '订单编号',
    `order_status` varchar(255) NOT NULL COMMENT '订单状态：wait-等待执行；doing-正在执行；fail-执行失败；success执行成功',
    `copywriting_category_id` int NOT NULL default 0 COMMENT '分类场景id',
    `user_prompt` varchar(3000) NOT NULL DEFAULT '' COMMENT '用户提示词',
    `image_proportion` varchar(255) NOT NULL DEFAULT '' COMMENT '图片比例',
    `image_size` varchar(255) NOT NULL DEFAULT '' COMMENT  '图片尺寸',
    `image_num` varchar(255) NOT NULL DEFAULT 1 COMMENT '生成数量',
    `pay_point` int NOT NULL DEFAULT 1 COMMENT '支付点数',
    `image_result` json  COMMENT '生成图片结果' ,
    `create_time` int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time` int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at` int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (`sys_id`),
    KEY `platform_user_sys_id` (`platform_user_sys_id`)
) COMMENT='用户图片创作订单表';


CREATE TABLE `user_video_order` (
    `sys_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid` char(32) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一关键字段',
    `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
    `merchant_guid` varchar(255) NOT NULL default '' COMMENT '商家guid',
    `order_no` varchar(255) NOT NULL COMMENT '订单编号',
    `order_status` varchar(255) NOT NULL COMMENT '订单状态：wait-等待执行；doing-正在执行；fail-执行失败；success执行成功',
    `user_prompt` varchar(3000) NOT NULL DEFAULT '' COMMENT '用户提示词',
    `portrait` varchar(255) NOT NULL DEFAULT '' COMMENT '数字人头像',
    `choose_voice` varchar(255) NOT NULL DEFAULT '' COMMENT '所选声音',
    `pay_point` int NOT NULL DEFAULT 1 COMMENT '支付点数',
    `video_id` varchar(255) NOT NULL DEFAULT '' COMMENT '生成的第三方作品id',
    `video_result` varchar(255) NOT NULL DEFAULT '' COMMENT '生成视频结果' ,
    `create_time` int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time` int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at` int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (`sys_id`),
    KEY `platform_user_sys_id` (`platform_user_sys_id`)
) COMMENT='用户数字人创作订单表';

CREATE TABLE `user_copywriting_category` (
     `sys_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
     `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
     `copywriting_category_id` int unsigned NOT NULL  COMMENT '分类场景id',
     `cate_type` varchar(255) NOT NULL DEFAULT 'text' COMMENT '分类类型：text-文案生成；image-图像生成',
     `create_time` int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
     `update_time` int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
     `deleted_at` int unsigned DEFAULT NULL COMMENT '删除时间',
     `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
     PRIMARY KEY (`sys_id`),
     KEY `platform_user_sys_id` (`platform_user_sys_id`)
)COMMENT='用户场景收藏记录表';

CREATE TABLE poster_img_order(
    `sys_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid` char(32) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一关键字段',
    `platform_user_sys_id` int DEFAULT 0 COMMENT '用户id',
    `order_no` varchar(255) NOT NULL COMMENT '订单编号',
    `order_status` varchar(255) NOT NULL COMMENT '订单状态：wait-等待执行；doing-正在执行；fail-执行失败；success执行成功',
    `head_img_url` varchar(255) NOT NULL DEFAULT '' COMMENT '头像图片',
    `poster_img_url` varchar(255) NOT NULL DEFAULT '' COMMENT '海报图片',
    `pay_point` int NOT NULL DEFAULT 1 COMMENT '支付点数',
    `image_result` json  COMMENT '生成图片结果',
    `work_task_id` varchar(255) NOT NULL DEFAULT '' COMMENT '作品任务id',
    `create_time` int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time` int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at` int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (`sys_id`),
    KEY `platform_user_sys_id` (`platform_user_sys_id`),
    KEY `work_task_id` (`work_task_id`),
    KEY `order_no` (`order_no`)
)COMMENT='海报创作订单表';